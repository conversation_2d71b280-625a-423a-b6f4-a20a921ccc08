// src/core/texture/texture.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Texture system implementation

#include "texture.hpp"
#include "../math/vec2.hpp"
#include "../image/image_io.hpp"
#include <algorithm>
#include <cmath>

namespace photon {

// Constants
static const float PI = 3.14159265359f;

// Texture base class implementation
float Texture::applyWrap(float coord) const {
    switch (m_wrap) {
        case TextureWrap::REPEAT:
            return coord - std::floor(coord);
        case TextureWrap::CLAMP:
            return std::clamp(coord, 0.0f, 1.0f);
        case TextureWrap::MIRROR: {
            float t = coord - std::floor(coord);
            int i = int(std::floor(coord));
            return (i % 2 == 0) ? t : (1.0f - t);
        }
        case TextureWrap::BORDER:
            return (coord < 0.0f || coord > 1.0f) ? -1.0f : coord; // -1 indicates border
        default:
            return coord;
    }
}

Vec2 Texture::applyWrap(const Vec2& uv) const {
    return Vec2(applyWrap(uv.x), applyWrap(uv.y));
}

// ImageTexture implementation
ImageTexture::ImageTexture(int width, int height, int channels, const float* data)
    : m_width(width), m_height(height), m_channels(channels) {
    
    // Determine format
    if (channels == 1) {
        m_format = TextureFormat::R32F;
    } else if (channels == 3) {
        m_format = TextureFormat::RGB32F;
    } else if (channels == 4) {
        m_format = TextureFormat::RGBA32F;
    }
    
    // Copy data
    size_t dataSize = width * height * channels;
    m_data.resize(dataSize);
    std::copy(data, data + dataSize, m_data.begin());
}

bool ImageTexture::loadFromFile(const std::string& filename) {
    setName(filename);
    
    // Load image using ImageIO
    ImageData imageData = ImageIO::loadImage(filename);
    
    if (imageData.pixels.empty()) {
        return false;
    }
    
    m_width = imageData.width;
    m_height = imageData.height;
    m_channels = imageData.channels;
    m_data = std::move(imageData.pixels);
    
    // Determine format
    if (m_channels == 1) {
        m_format = TextureFormat::R32F;
    } else if (m_channels == 3) {
        m_format = TextureFormat::RGB32F;
    } else if (m_channels == 4) {
        m_format = TextureFormat::RGBA32F;
    }
    
    return true;
}

std::shared_ptr<ImageTexture> ImageTexture::createSolid(const Color3& color) {
    float data[3] = { color.r, color.g, color.b };
    auto texture = std::make_shared<ImageTexture>(1, 1, 3, data);
    texture->setName("solid_color");
    return texture;
}

std::shared_ptr<ImageTexture> ImageTexture::createSolid(float value) {
    float data[1] = { value };
    auto texture = std::make_shared<ImageTexture>(1, 1, 1, data);
    texture->setName("solid_value");
    return texture;
}

Color3 ImageTexture::sample(const Vec2& uv) const {
    if (m_data.empty()) {
        return Color3(1.0f, 0.0f, 1.0f); // Magenta for missing texture
    }
    
    Vec2 wrappedUV = applyWrap(uv);
    
    // Check for border wrap
    if (m_wrap == TextureWrap::BORDER && (wrappedUV.x < 0.0f || wrappedUV.y < 0.0f)) {
        return Color3(0.0f); // Border color (black)
    }
    
    switch (m_filter) {
        case TextureFilter::NEAREST:
            return samplePixel(int(wrappedUV.x * m_width), int(wrappedUV.y * m_height));
        case TextureFilter::BILINEAR:
        default:
            return sampleBilinear(wrappedUV.x, wrappedUV.y);
    }
}

Color3 ImageTexture::sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const {
    // For now, ignore derivatives and use regular sampling
    // TODO: Implement proper mipmap filtering
    return sample(uv);
}

float ImageTexture::sampleFloat(const Vec2& uv) const {
    if (m_channels == 1) {
        // Single channel texture
        Vec2 wrappedUV = applyWrap(uv);
        if (m_wrap == TextureWrap::BORDER && (wrappedUV.x < 0.0f || wrappedUV.y < 0.0f)) {
            return 0.0f;
        }
        
        int x = std::clamp(int(wrappedUV.x * m_width), 0, m_width - 1);
        int y = std::clamp(int(wrappedUV.y * m_height), 0, m_height - 1);
        int index = (y * m_width + x) * m_channels;
        
        return m_data[index];
    } else {
        // Multi-channel texture - use luminance
        Color3 color = sample(uv);
        return color.luminance();
    }
}

Color3 ImageTexture::samplePixel(int x, int y) const {
    x = std::clamp(x, 0, m_width - 1);
    y = std::clamp(y, 0, m_height - 1);
    
    int index = (y * m_width + x) * m_channels;
    
    Color3 color;
    if (m_channels >= 3) {
        color.r = m_data[index];
        color.g = m_data[index + 1];
        color.b = m_data[index + 2];
    } else if (m_channels == 1) {
        float value = m_data[index];
        color = Color3(value);
    }
    
    return applyGamma(color);
}

Color3 ImageTexture::sampleBilinear(float u, float v) const {
    // Convert to pixel coordinates
    float x = u * m_width - 0.5f;
    float y = v * m_height - 0.5f;
    
    int x0 = int(std::floor(x));
    int y0 = int(std::floor(y));
    int x1 = x0 + 1;
    int y1 = y0 + 1;
    
    float fx = x - x0;
    float fy = y - y0;
    
    // Sample four neighboring pixels
    Color3 c00 = samplePixel(x0, y0);
    Color3 c10 = samplePixel(x1, y0);
    Color3 c01 = samplePixel(x0, y1);
    Color3 c11 = samplePixel(x1, y1);
    
    // Bilinear interpolation
    Color3 c0 = c00 * (1.0f - fx) + c10 * fx;
    Color3 c1 = c01 * (1.0f - fx) + c11 * fx;
    
    return c0 * (1.0f - fy) + c1 * fy;
}

Color3 ImageTexture::applyGamma(const Color3& color) const {
    if (m_gamma == 1.0f) {
        return color;
    }
    
    float invGamma = 1.0f / m_gamma;
    return Color3(
        std::pow(color.r, invGamma),
        std::pow(color.g, invGamma),
        std::pow(color.b, invGamma)
    );
}

// CheckerboardTexture implementation
CheckerboardTexture::CheckerboardTexture(const Color3& color1, const Color3& color2, float scale)
    : m_color1(color1), m_color2(color2), m_scale(scale) {
    setName("checkerboard");
}

Color3 CheckerboardTexture::sample(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);
    
    float u = wrappedUV.x * m_scale;
    float v = wrappedUV.y * m_scale;
    
    int checkU = int(std::floor(u));
    int checkV = int(std::floor(v));
    
    bool isEven = (checkU + checkV) % 2 == 0;
    return isEven ? m_color1 : m_color2;
}

// NoiseTexture implementation
NoiseTexture::NoiseTexture(float frequency, float amplitude, int octaves)
    : m_frequency(frequency), m_amplitude(amplitude) {
    m_fractal.octaves = octaves;
    setName("noise");
}

NoiseTexture::NoiseTexture(NoiseType type, float frequency, float amplitude, const FractalParams& fractal)
    : m_noiseType(type), m_frequency(frequency), m_amplitude(amplitude), m_fractal(fractal) {
    setName("noise_" + std::to_string(static_cast<int>(type)));
}

Color3 NoiseTexture::sample(const Vec2& uv) const {
    float noise = sampleFloat(uv);
    return Color3(noise);
}

float NoiseTexture::sampleFloat(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);

    // Generate fractal noise
    float noise = generateFractalNoise(wrappedUV.x, wrappedUV.y);

    // Apply amplitude and normalize to [0,1]
    noise *= m_amplitude;
    noise = (noise + 1.0f) * 0.5f;
    noise = std::clamp(noise, 0.0f, 1.0f);

    return noise;
}

float NoiseTexture::generateFractalNoise(float x, float y) const {
    float result = 0.0f;
    float amplitude = 1.0f;
    float frequency = m_frequency * m_fractal.scale;
    float maxValue = 0.0f;

    for (int i = 0; i < m_fractal.octaves; ++i) {
        float noiseValue = 0.0f;

        // Select noise algorithm
        switch (m_noiseType) {
            case NoiseType::PERLIN:
                noiseValue = perlinNoise(x * frequency, y * frequency);
                break;
            case NoiseType::SIMPLEX:
                noiseValue = simplexNoise(x * frequency, y * frequency);
                break;
            case NoiseType::WORLEY:
                noiseValue = worleyNoise(x * frequency, y * frequency);
                break;
        }

        result += amplitude * noiseValue;
        maxValue += amplitude;

        amplitude *= m_fractal.persistence;
        frequency *= m_fractal.lacunarity;
    }

    // Normalize by maximum possible value
    if (maxValue > 0.0f) {
        result /= maxValue;
    }

    return result;
}

} // namespace photon
