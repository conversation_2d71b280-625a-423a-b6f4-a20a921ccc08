// src/test/test_disney_brdf.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Disney BRDF Test Suite

#include "../core/material/disney_brdf.hpp"
#include "../core/material/material.hpp"
#include "../core/sampler/random_sampler.hpp"
#include "../core/scene/intersection.hpp"
#include <iostream>
#include <cassert>
#include <cmath>

namespace photon {

/**
 * @brief Test Disney BRDF energy conservation
 */
bool testEnergyConservation() {
    std::cout << "Testing Disney BRDF energy conservation..." << std::endl;
    
    DisneyBRDF brdf;
    RandomSampler sampler;
    
    // Test different material configurations
    std::vector<DisneyBRDFParams> testParams = {
        DisneyMaterialPresets::createPlastic(),
        DisneyMaterialPresets::createMetal(),
        DisneyMaterialPresets::createWood(),
        DisneyMaterialPresets::createFabric()
    };
    
    for (const auto& params : testParams) {
        brdf.setParameters(params);
        
        Vec3 n(0, 0, 1);  // Surface normal
        Vec3 wo(0, 0, 1); // Outgoing direction (towards viewer)
        
        // Monte Carlo integration to check energy conservation
        const int numSamples = 10000;
        Color3 totalReflectance(0.0f);
        
        for (int i = 0; i < numSamples; ++i) {
            Vec3 wi;
            float pdf;
            Color3 f = brdf.sample(wo, n, sampler, wi, pdf);
            
            if (pdf > 0.0f && wi.dot(n) > 0.0f) {
                totalReflectance += f * wi.dot(n) / pdf;
            }
        }
        
        totalReflectance = totalReflectance / float(numSamples);
        
        // Check if total reflectance is reasonable (should be < 1.0)
        float maxReflectance = std::max({totalReflectance.r, totalReflectance.g, totalReflectance.b});
        
        std::cout << "  Material: " << (params.metallic > 0.5f ? "Metal" : "Dielectric") 
                  << ", Max reflectance: " << maxReflectance << std::endl;
        
        if (maxReflectance > 1.1f) { // Allow some Monte Carlo error
            std::cout << "  ERROR: Energy conservation violated!" << std::endl;
            return false;
        }
    }
    
    std::cout << "  Energy conservation test PASSED" << std::endl;
    return true;
}

/**
 * @brief Test Disney BRDF reciprocity
 */
bool testReciprocity() {
    std::cout << "Testing Disney BRDF reciprocity..." << std::endl;
    
    DisneyBRDF brdf;
    DisneyBRDFParams params = DisneyMaterialPresets::createPlastic();
    brdf.setParameters(params);
    
    Vec3 n(0, 0, 1);
    Vec3 wo(0.5f, 0.3f, 0.8f);
    Vec3 wi(0.2f, 0.7f, 0.6f);
    wo = wo.normalized();
    wi = wi.normalized();
    
    // Test reciprocity: f(wo, wi) should equal f(wi, wo)
    Color3 f1 = brdf.eval(wo, wi, n);
    Color3 f2 = brdf.eval(wi, wo, n);
    
    float diff = (f1 - f2).length();
    
    std::cout << "  f(wo, wi) = (" << f1.r << ", " << f1.g << ", " << f1.b << ")" << std::endl;
    std::cout << "  f(wi, wo) = (" << f2.r << ", " << f2.g << ", " << f2.b << ")" << std::endl;
    std::cout << "  Difference: " << diff << std::endl;
    
    if (diff > 1e-5f) {
        std::cout << "  ERROR: Reciprocity violated!" << std::endl;
        return false;
    }
    
    std::cout << "  Reciprocity test PASSED" << std::endl;
    return true;
}

/**
 * @brief Test Disney BRDF sampling consistency
 */
bool testSamplingConsistency() {
    std::cout << "Testing Disney BRDF sampling consistency..." << std::endl;
    
    DisneyBRDF brdf;
    DisneyBRDFParams params = DisneyMaterialPresets::createMetal(Color3(0.8f, 0.6f, 0.4f));
    brdf.setParameters(params);
    
    RandomSampler sampler;
    Vec3 n(0, 0, 1);
    Vec3 wo(0, 0, 1);
    
    const int numSamples = 1000;
    int validSamples = 0;
    
    for (int i = 0; i < numSamples; ++i) {
        Vec3 wi;
        float samplePdf;
        Color3 f = brdf.sample(wo, n, sampler, wi, samplePdf);
        
        if (samplePdf > 0.0f && wi.dot(n) > 0.0f) {
            // Check if eval and sample are consistent
            Color3 evalF = brdf.eval(wo, wi, n);
            float evalPdf = brdf.pdf(wo, wi, n);
            
            float fDiff = (f - evalF).length();
            float pdfDiff = std::abs(samplePdf - evalPdf);
            
            if (fDiff > 1e-4f || pdfDiff > 1e-4f) {
                std::cout << "  ERROR: Sample/eval inconsistency!" << std::endl;
                std::cout << "    Sample f: (" << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
                std::cout << "    Eval f: (" << evalF.r << ", " << evalF.g << ", " << evalF.b << ")" << std::endl;
                std::cout << "    Sample PDF: " << samplePdf << ", Eval PDF: " << evalPdf << std::endl;
                return false;
            }
            
            validSamples++;
        }
    }
    
    std::cout << "  Valid samples: " << validSamples << "/" << numSamples << std::endl;
    
    if (validSamples < numSamples * 0.8f) {
        std::cout << "  ERROR: Too many invalid samples!" << std::endl;
        return false;
    }
    
    std::cout << "  Sampling consistency test PASSED" << std::endl;
    return true;
}

/**
 * @brief Test PBR Material integration
 */
bool testPBRMaterial() {
    std::cout << "Testing PBR Material integration..." << std::endl;
    
    // Test different presets
    std::vector<std::string> presets = {"plastic", "metal", "glass", "wood", "fabric"};
    
    for (const auto& preset : presets) {
        auto material = PBRMaterial::createPreset(preset, Color3(0.7f, 0.5f, 0.3f));
        
        // Test energy conservation
        if (!material->validateEnergyConservation()) {
            std::cout << "  ERROR: " << preset << " preset violates energy conservation!" << std::endl;
            return false;
        }
        
        // Test basic functionality
        Intersection isect;
        isect.n = Vec3(0, 0, 1);
        isect.p = Vec3(0, 0, 0);
        
        Vec3 wo(0, 0, 1);
        RandomSampler sampler;
        
        BSDFSample sample = material->sample(isect, wo, sampler);
        
        if (!sample.isValid()) {
            std::cout << "  ERROR: " << preset << " material produced invalid sample!" << std::endl;
            return false;
        }
        
        std::cout << "  " << preset << " preset: OK" << std::endl;
    }
    
    std::cout << "  PBR Material integration test PASSED" << std::endl;
    return true;
}

/**
 * @brief Test material parameter validation
 */
bool testParameterValidation() {
    std::cout << "Testing parameter validation..." << std::endl;
    
    DisneyBRDFParams params;
    
    // Test invalid parameters
    params.metallic = -0.5f;
    params.roughness = 1.5f;
    params.specular = 2.0f;
    
    params.validate();
    
    // Check if parameters were clamped correctly
    if (params.metallic != 0.0f || params.roughness != 1.0f || params.specular != 1.0f) {
        std::cout << "  ERROR: Parameter validation failed!" << std::endl;
        return false;
    }
    
    std::cout << "  Parameter validation test PASSED" << std::endl;
    return true;
}

} // namespace photon

/**
 * @brief Run all Disney BRDF tests
 */
bool runDisneyBRDFTests() {
    std::cout << "\n=== Disney BRDF Test Suite ===" << std::endl;
    
    bool allPassed = true;
    
    allPassed &= photon::testParameterValidation();
    allPassed &= photon::testEnergyConservation();
    allPassed &= photon::testReciprocity();
    allPassed &= photon::testSamplingConsistency();
    allPassed &= photon::testPBRMaterial();
    
    if (allPassed) {
        std::cout << "\n✅ All Disney BRDF tests PASSED!" << std::endl;
    } else {
        std::cout << "\n❌ Some Disney BRDF tests FAILED!" << std::endl;
    }
    
    return allPassed;
}
