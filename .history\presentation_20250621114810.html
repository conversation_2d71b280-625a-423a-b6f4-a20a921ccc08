<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhotonRender - Motore di Rendering Fotorealistico per SketchUp</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            font-weight: bold;
        }

        .logo-text h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }

        .tagline {
            color: #666;
            font-size: 0.9em;
            margin-top: -5px;
        }

        .version-info {
            text-align: right;
            color: #666;
        }

        .version {
            font-weight: 600;
            color: #4ecdc4;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .hero h2 {
            font-size: 3.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.3em;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* Features Section */
        .features {
            background: white;
            padding: 80px 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #333;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Performance Section */
        .performance {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 80px 0;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .performance-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .performance-number {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .performance-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* Comparison Section */
        .comparison {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .comparison-table {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 40px;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 20px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        .check {
            color: #4ecdc4;
            font-weight: bold;
        }

        .cross {
            color: #ff6b6b;
            font-weight: bold;
        }

        /* Roadmap Section */
        .roadmap {
            background: white;
            padding: 80px 0;
        }

        .roadmap-timeline {
            position: relative;
            margin-top: 40px;
        }

        .roadmap-item {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            position: relative;
        }

        .roadmap-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 30px;
            z-index: 2;
        }

        .roadmap-completed {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .roadmap-current {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .roadmap-future {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .roadmap-content {
            flex: 1;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .roadmap-content h4 {
            font-size: 1.3em;
            margin-bottom: 10px;
            color: #333;
        }

        .roadmap-content p {
            color: #666;
            margin-bottom: 10px;
        }

        .progress-bar {
            background: #eee;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            transition: width 0.3s ease;
        }

        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }

        .footer-section h4 {
            margin-bottom: 15px;
            color: #4ecdc4;
        }

        .footer-section p,
        .footer-section li {
            color: #bdc3c7;
            margin-bottom: 8px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-bottom {
            border-top: 1px solid #34495e;
            padding-top: 20px;
            color: #95a5a6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h2 {
                font-size: 2.5em;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .performance-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">PR</div>
                    <div class="logo-text">
                        <h1>PhotonRender</h1>
                        <div class="tagline">Motore di Rendering Fotorealistico per SketchUp</div>
                    </div>
                </div>
                <div class="version-info">
                    <div class="version">v3.2.3-alpha</div>
                    <div>Fase 3.2.3 Advanced Texture System</div>
                    <div>Ultimo aggiornamento: 21 Gennaio 2025</div>
                </div>
            </div>
        </div>
    </header>
