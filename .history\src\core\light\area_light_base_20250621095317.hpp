// src/core/light/area_light_base.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Extended Area Light base class for geometric shapes

#pragma once

#include "../scene/light.hpp"
#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"
#include "../math/matrix4.hpp"
#include <memory>

namespace photon {

// Type aliases
using Transform = Matrix4;  // Transform is Matrix4 for now

// Forward declarations
class Scene;
class Sampler;

/**
 * @brief Area light shapes supported
 */
enum class AreaLightShape {
    RECTANGLE,  // Rectangular area light
    DISK,       // Circular disk area light
    SPHERE      // Spherical area light
};

/**
 * @brief Area light sampling modes
 */
enum class AreaLightSampling {
    UNIFORM,        // Uniform surface sampling
    COSINE_WEIGHTED // Cosine-weighted sampling
};

/**
 * @brief Extended Area Light base class
 */
class AreaLightBase : public Light {
public:
    /**
     * @brief Constructor
     * @param emission Emitted radiance
     * @param intensity Intensity multiplier
     * @param twoSided Whether light emits from both sides
     */
    AreaLightBase(const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Destructor
     */
    virtual ~AreaLightBase() = default;
    
    // Light interface implementation
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return false; }
    std::string getName() const override = 0;
    void preprocess(const Scene& scene) override;
    
    /**
     * @brief Sample point on light surface
     * @param sampler Random sampler
     * @param point Output sampled point
     * @param normal Output surface normal at point
     * @param pdf Output PDF for sampling
     */
    virtual void sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const = 0;
    
    /**
     * @brief Get surface area
     */
    virtual float getArea() const = 0;
    
    /**
     * @brief Get shape type
     */
    virtual AreaLightShape getShape() const = 0;
    
    /**
     * @brief Check if ray intersects light surface
     * @param ray Ray to test
     * @param t Output intersection distance
     * @param point Output intersection point
     * @param normal Output surface normal
     * @return True if intersection found
     */
    virtual bool intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const = 0;
    
    /**
     * @brief Set emission color
     */
    void setEmission(const Color3& emission) { m_emission = emission; }
    
    /**
     * @brief Get emission color
     */
    const Color3& getEmission() const { return m_emission; }
    
    /**
     * @brief Set intensity multiplier
     */
    void setIntensity(float intensity) { m_intensity = intensity; }
    
    /**
     * @brief Get intensity multiplier
     */
    float getIntensity() const { return m_intensity; }
    
    /**
     * @brief Set two-sided emission
     */
    void setTwoSided(bool twoSided) { m_twoSided = twoSided; }
    
    /**
     * @brief Check if two-sided
     */
    bool isTwoSided() const { return m_twoSided; }
    
    /**
     * @brief Set sampling mode
     */
    void setSamplingMode(AreaLightSampling mode) { m_samplingMode = mode; }
    
    /**
     * @brief Get sampling mode
     */
    AreaLightSampling getSamplingMode() const { return m_samplingMode; }
    
    /**
     * @brief Set transform
     */
    void setTransform(const Transform& transform) { m_transform = transform; }
    
    /**
     * @brief Get transform
     */
    const Transform& getTransform() const { return m_transform; }
    
    /**
     * @brief Get effective emission (emission * intensity)
     */
    Color3 getEffectiveEmission() const { return m_emission * m_intensity; }

protected:
    Color3 m_emission;                          // Base emission color
    float m_intensity = 1.0f;                   // Intensity multiplier
    bool m_twoSided = false;                    // Two-sided emission
    AreaLightSampling m_samplingMode = AreaLightSampling::UNIFORM;
    Transform m_transform;                      // Light transform
    
    /**
     * @brief Sample direction using cosine-weighted hemisphere sampling
     * @param normal Surface normal
     * @param sampler Random sampler
     * @return Sampled direction and PDF
     */
    std::pair<Vec3, float> sampleCosineHemisphere(const Vec3& normal, Sampler& sampler) const;
    
    /**
     * @brief Get PDF for cosine-weighted hemisphere sampling
     * @param normal Surface normal
     * @param direction Sampled direction
     * @return PDF value
     */
    float getCosineHemispherePDF(const Vec3& normal, const Vec3& direction) const;
    
    /**
     * @brief Convert area PDF to solid angle PDF
     * @param areaPDF Area PDF
     * @param distance Distance to surface
     * @param cosTheta Cosine of angle between normal and direction
     * @return Solid angle PDF
     */
    float areaToSolidAnglePDF(float areaPDF, float distance, float cosTheta) const;
    
    /**
     * @brief Check if direction is valid for emission
     * @param normal Surface normal
     * @param direction Emission direction
     * @return True if valid
     */
    bool isValidEmissionDirection(const Vec3& normal, const Vec3& direction) const;
};

/**
 * @brief Area light utility functions
 */
namespace AreaLightUtils {
    /**
     * @brief Sample uniform disk
     * @param u Random sample [0,1]^2
     * @return Point on unit disk
     */
    Vec2 sampleUniformDisk(const Vec2& u);
    
    /**
     * @brief Sample uniform sphere
     * @param u Random sample [0,1]^2
     * @return Point on unit sphere
     */
    Vec3 sampleUniformSphere(const Vec2& u);
    
    /**
     * @brief Sample cosine-weighted hemisphere
     * @param u Random sample [0,1]^2
     * @return Direction and PDF
     */
    std::pair<Vec3, float> sampleCosineHemisphere(const Vec2& u);
    
    /**
     * @brief Compute solid angle subtended by rectangle
     * @param p Point from which to compute solid angle
     * @param corners Rectangle corners
     * @return Solid angle
     */
    float rectangleSolidAngle(const Vec3& p, const Vec3 corners[4]);
    
    /**
     * @brief Compute solid angle subtended by disk
     * @param p Point from which to compute solid angle
     * @param center Disk center
     * @param normal Disk normal
     * @param radius Disk radius
     * @return Solid angle
     */
    float diskSolidAngle(const Vec3& p, const Vec3& center, const Vec3& normal, float radius);
}

} // namespace photon
