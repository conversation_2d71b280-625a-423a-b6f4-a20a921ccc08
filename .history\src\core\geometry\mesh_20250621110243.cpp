// src/core/geometry/mesh.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Mesh geometry implementation

#include "mesh.hpp"
#include "../math/matrix4.hpp"
#include <algorithm>
#include <iostream>
#include <cmath>

namespace photon {

// Vertex operations
void Mesh::addVertex(const Vertex& vertex) {
    vertices_.push_back(vertex);
    boundingBoxValid_ = false;
}

void Mesh::addVertex(const Point3& position, const Normal3& normal) {
    vertices_.emplace_back(position, normal);
    boundingBoxValid_ = false;
}

void Mesh::addVertex(const Point3& position, const Normal3& normal, float u, float v) {
    vertices_.emplace_back(position, normal, u, v);
    boundingBoxValid_ = false;
}

// Multiple UV sets operations
void Mesh::addVertex(const Point3& position, const Normal3& normal, const Vec2& uv0,
                     const Vec2& uv1, const Vec2& uv2, const Vec2& uv3) {
    vertices_.emplace_back(position, normal, uv0, uv1, uv2, uv3);
    boundingBoxValid_ = false;
}

void Mesh::setVertexUV(size_t vertexIndex, int uvSet, const Vec2& uvCoords) {
    if (vertexIndex >= vertices_.size()) return;
    vertices_[vertexIndex].setUV(uvSet, uvCoords);
}

Vec2 Mesh::getVertexUV(size_t vertexIndex, int uvSet) const {
    if (vertexIndex >= vertices_.size()) return Vec2(0.0f);
    return vertices_[vertexIndex].getUV(uvSet);
}

bool Mesh::hasVertexUVSet(size_t vertexIndex, int uvSet) const {
    if (vertexIndex >= vertices_.size()) return false;
    return vertices_[vertexIndex].hasUVSet(uvSet);
}

// Triangle operations
void Mesh::addTriangle(const Triangle& triangle) {
    triangles_.push_back(triangle);
}

void Mesh::addTriangle(uint32_t v0, uint32_t v1, uint32_t v2, uint32_t materialId) {
    triangles_.emplace_back(v0, v1, v2, materialId);
}

// Helper method to add triangle from vertices (for compatibility with old interface)
void Mesh::addTriangleFromVertices(const Point3& v0, const Point3& v1, const Point3& v2,
                                   const Normal3& n0, const Normal3& n1, const Normal3& n2,
                                   float u0, float v0_tex, float u1, float v1_tex, float u2, float v2_tex) {
    uint32_t baseIndex = static_cast<uint32_t>(vertices_.size());
    
    // Add vertices
    addVertex(v0, n0, u0, v0_tex);
    addVertex(v1, n1, u1, v1_tex);
    addVertex(v2, n2, u2, v2_tex);
    
    // Add triangle
    addTriangle(baseIndex, baseIndex + 1, baseIndex + 2);
}

// Helper method to add triangle from vertices (simplified)
void Mesh::addTriangleFromVertices(const Point3& v0, const Point3& v1, const Point3& v2) {
    // Compute face normal
    Vec3 edge1 = v1 - v0;
    Vec3 edge2 = v2 - v0;
    Normal3 normal = edge1.cross(edge2).normalized();

    addTriangleFromVertices(v0, v1, v2, normal, normal, normal, 0, 0, 0, 0, 0, 0);
}



// Helper method to add quad from vertices (for compatibility with old interface)
void Mesh::addQuad(const Point3& v0, const Point3& v1, const Point3& v2, const Point3& v3,
                   const Normal3& n0, const Normal3& n1, const Normal3& n2, const Normal3& n3,
                   float u0, float v0_tex, float u1, float v1_tex, float u2, float v2_tex, float u3, float v3_tex) {
    // Split quad into two triangles: (v0,v1,v2) and (v0,v2,v3)
    addTriangleFromVertices(v0, v1, v2, n0, n1, n2, u0, v0_tex, u1, v1_tex, u2, v2_tex);
    addTriangleFromVertices(v0, v2, v3, n0, n2, n3, u0, v0_tex, u2, v2_tex, u3, v3_tex);
}

// Helper method to add quad from vertices (simplified)
void Mesh::addQuad(const Point3& v0, const Point3& v1, const Point3& v2, const Point3& v3) {
    // Compute face normal
    Vec3 edge1 = v1 - v0;
    Vec3 edge2 = v3 - v0;
    Normal3 normal = edge1.cross(edge2).normalized();

    // Split quad into two triangles: (v0,v1,v2) and (v0,v2,v3)
    addTriangleFromVertices(v0, v1, v2, normal, normal, normal, 0, 0, 0, 0, 0, 0);
    addTriangleFromVertices(v0, v2, v3, normal, normal, normal, 0, 0, 0, 0, 0, 0);
}



// Geometry operations
void Mesh::computeNormals() {
    if (vertices_.empty() || triangles_.empty()) return;
    
    // Reset all normals to zero
    for (auto& vertex : vertices_) {
        vertex.normal = Normal3(0);
    }
    
    // Accumulate face normals
    for (const auto& triangle : triangles_) {
        if (triangle.v0 >= vertices_.size() || 
            triangle.v1 >= vertices_.size() || 
            triangle.v2 >= vertices_.size()) {
            continue; // Skip invalid triangles
        }
        
        const Point3& p0 = vertices_[triangle.v0].position;
        const Point3& p1 = vertices_[triangle.v1].position;
        const Point3& p2 = vertices_[triangle.v2].position;
        
        // Compute face normal
        Vec3 edge1 = p1 - p0;
        Vec3 edge2 = p2 - p0;
        Normal3 faceNormal = edge1.cross(edge2);
        
        // Accumulate to vertex normals (weighted by area)
        vertices_[triangle.v0].normal += faceNormal;
        vertices_[triangle.v1].normal += faceNormal;
        vertices_[triangle.v2].normal += faceNormal;
    }
    
    // Normalize vertex normals
    for (auto& vertex : vertices_) {
        if (vertex.normal.lengthSquared() > 0) {
            vertex.normal.normalize();
        } else {
            vertex.normal = Normal3(0, 1, 0); // Default up normal
        }
    }
}

void Mesh::computeTangents() {
    if (vertices_.empty() || triangles_.empty()) return;
    
    // Reset tangents and bitangents
    for (auto& vertex : vertices_) {
        vertex.tangent = Vec3(0);
        vertex.bitangent = Vec3(0);
    }
    
    // Compute tangents for each triangle
    for (const auto& triangle : triangles_) {
        if (triangle.v0 >= vertices_.size() || 
            triangle.v1 >= vertices_.size() || 
            triangle.v2 >= vertices_.size()) {
            continue;
        }
        
        Vertex& v0 = vertices_[triangle.v0];
        Vertex& v1 = vertices_[triangle.v1];
        Vertex& v2 = vertices_[triangle.v2];
        
        // Position deltas
        Vec3 deltaPos1 = v1.position - v0.position;
        Vec3 deltaPos2 = v2.position - v0.position;
        
        // UV deltas
        float deltaUV1_u = v1.u - v0.u;
        float deltaUV1_v = v1.v - v0.v;
        float deltaUV2_u = v2.u - v0.u;
        float deltaUV2_v = v2.v - v0.v;
        
        float r = 1.0f / (deltaUV1_u * deltaUV2_v - deltaUV1_v * deltaUV2_u);
        Vec3 tangent = (deltaPos1 * deltaUV2_v - deltaPos2 * deltaUV1_v) * r;
        Vec3 bitangent = (deltaPos2 * deltaUV1_u - deltaPos1 * deltaUV2_u) * r;
        
        // Accumulate
        v0.tangent += tangent;
        v1.tangent += tangent;
        v2.tangent += tangent;
        v0.bitangent += bitangent;
        v1.bitangent += bitangent;
        v2.bitangent += bitangent;
    }
    
    // Normalize and orthogonalize
    for (auto& vertex : vertices_) {
        // Gram-Schmidt orthogonalize
        vertex.tangent = (vertex.tangent - vertex.normal * vertex.normal.dot(vertex.tangent)).normalized();
        
        // Calculate handedness
        if (vertex.normal.cross(vertex.tangent).dot(vertex.bitangent) < 0.0f) {
            vertex.tangent = vertex.tangent * -1.0f;
        }
        
        vertex.bitangent = vertex.normal.cross(vertex.tangent);
    }
}

void Mesh::computeBoundingBox() {
    if (vertices_.empty()) {
        boundingBox_ = BoundingBox();
        boundingBoxValid_ = false;
        return;
    }
    
    boundingBox_ = BoundingBox();
    for (const auto& vertex : vertices_) {
        boundingBox_.expand(vertex.position);
    }
    boundingBoxValid_ = true;
}

// Utility functions
void Mesh::clear() {
    vertices_.clear();
    triangles_.clear();
    boundingBox_ = BoundingBox();
    boundingBoxValid_ = false;
}

void Mesh::reserve(size_t vertexCount, size_t triangleCount) {
    vertices_.reserve(vertexCount);
    triangles_.reserve(triangleCount);
}

// Transform operations
void Mesh::transform(const Matrix4& matrix) {
    for (auto& vertex : vertices_) {
        vertex.position = matrix.transformPoint(vertex.position);
        vertex.normal = matrix.transformNormal(vertex.normal).normalized();
    }
    boundingBoxValid_ = false;
}

void Mesh::translate(const Vec3& translation) {
    for (auto& vertex : vertices_) {
        vertex.position += translation;
    }
    boundingBoxValid_ = false;
}

void Mesh::scale(float factor) {
    scale(Vec3(factor, factor, factor));
}

void Mesh::scale(const Vec3& factors) {
    for (auto& vertex : vertices_) {
        vertex.position.x *= factors.x;
        vertex.position.y *= factors.y;
        vertex.position.z *= factors.z;
    }
    boundingBoxValid_ = false;
}

// Validation
bool Mesh::validate() const {
    if (vertices_.empty() || triangles_.empty()) return false;
    
    // Check triangle indices
    for (const auto& triangle : triangles_) {
        if (triangle.v0 >= vertices_.size() || 
            triangle.v1 >= vertices_.size() || 
            triangle.v2 >= vertices_.size()) {
            return false;
        }
    }
    
    return true;
}

void Mesh::fixWindingOrder() {
    // Ensure consistent counter-clockwise winding
    for (auto& triangle : triangles_) {
        if (triangle.v0 >= vertices_.size() || 
            triangle.v1 >= vertices_.size() || 
            triangle.v2 >= vertices_.size()) {
            continue;
        }
        
        const Point3& p0 = vertices_[triangle.v0].position;
        const Point3& p1 = vertices_[triangle.v1].position;
        const Point3& p2 = vertices_[triangle.v2].position;
        
        Vec3 edge1 = p1 - p0;
        Vec3 edge2 = p2 - p0;
        Vec3 normal = edge1.cross(edge2);
        
        // If normal points inward (negative), flip winding
        if (normal.y < 0) { // Assuming Y-up coordinate system
            std::swap(triangle.v1, triangle.v2);
        }
    }
}

// Statistics
Mesh::MeshStats Mesh::getStats() const {
    MeshStats stats;
    stats.vertexCount = vertices_.size();
    stats.triangleCount = triangles_.size();
    
    if (!boundingBoxValid_) {
        const_cast<Mesh*>(this)->computeBoundingBox();
    }
    stats.boundingBox = boundingBox_;
    
    // Check for normals, texture coordinates, tangents
    if (!vertices_.empty()) {
        stats.hasNormals = vertices_[0].normal.lengthSquared() > 0;
        stats.hasTexCoords = vertices_[0].u != 0 || vertices_[0].v != 0;
        stats.hasTangents = vertices_[0].tangent.lengthSquared() > 0;
    }
    
    // Compute surface area
    stats.surfaceArea = 0.0f;
    for (const auto& triangle : triangles_) {
        if (triangle.v0 < vertices_.size() && 
            triangle.v1 < vertices_.size() && 
            triangle.v2 < vertices_.size()) {
            stats.surfaceArea += computeTriangleArea(triangle);
        }
    }
    
    return stats;
}

void Mesh::printStats() const {
    auto stats = getStats();
    std::cout << "Mesh Statistics:" << std::endl;
    std::cout << "  Vertices: " << stats.vertexCount << std::endl;
    std::cout << "  Triangles: " << stats.triangleCount << std::endl;
    std::cout << "  Surface Area: " << stats.surfaceArea << std::endl;
    std::cout << "  Has Normals: " << (stats.hasNormals ? "Yes" : "No") << std::endl;
    std::cout << "  Has Texture Coords: " << (stats.hasTexCoords ? "Yes" : "No") << std::endl;
    std::cout << "  Has Tangents: " << (stats.hasTangents ? "Yes" : "No") << std::endl;
    std::cout << "  Bounding Box: " << stats.boundingBox.min << " to " << stats.boundingBox.max << std::endl;
}

// Helper functions
Normal3 Mesh::computeFaceNormal(const Triangle& triangle) const {
    if (triangle.v0 >= vertices_.size() || 
        triangle.v1 >= vertices_.size() || 
        triangle.v2 >= vertices_.size()) {
        return Normal3(0, 1, 0);
    }
    
    const Point3& p0 = vertices_[triangle.v0].position;
    const Point3& p1 = vertices_[triangle.v1].position;
    const Point3& p2 = vertices_[triangle.v2].position;
    
    Vec3 edge1 = p1 - p0;
    Vec3 edge2 = p2 - p0;
    return edge1.cross(edge2).normalized();
}

float Mesh::computeTriangleArea(const Triangle& triangle) const {
    if (triangle.v0 >= vertices_.size() || 
        triangle.v1 >= vertices_.size() || 
        triangle.v2 >= vertices_.size()) {
        return 0.0f;
    }
    
    const Point3& p0 = vertices_[triangle.v0].position;
    const Point3& p1 = vertices_[triangle.v1].position;
    const Point3& p2 = vertices_[triangle.v2].position;
    
    Vec3 edge1 = p1 - p0;
    Vec3 edge2 = p2 - p0;
    return edge1.cross(edge2).length() * 0.5f;
}



} // namespace photon
