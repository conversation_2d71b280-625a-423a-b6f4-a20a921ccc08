// src/core/image/image_io.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Image input/output functionality

#pragma once

#ifdef PHOTON_SIMPLE_BUILD
#include "../common_simple.hpp"
#else
#include "../common.hpp"
#endif
#include <string>
#include <vector>

namespace photon {

#ifndef PHOTON_SIMPLE_BUILD
// Forward declaration for full build
class Film;
#else
// Dummy Film class for simplified build
class Film {
public:
    int getWidth() const { return 0; }
    int getHeight() const { return 0; }
    Color3 getPixel(int, int) const { return Color3(0); }
};
#endif

/**
 * @brief Image format enumeration
 */
enum class ImageFormat {
    PNG,
    JPEG,
    EXR,
    HDR,
    BMP,
    TGA
};

/**
 * @brief Image data structure
 */
struct ImageData {
    int width = 0;
    int height = 0;
    int channels = 0;
    std::vector<float> pixels;  // Always stored as float internally
    
    ImageData() = default;
    ImageData(int w, int h, int c) : width(w), height(h), channels(c), pixels(w * h * c) {}
    
    // Get pixel at coordinates (x, y)
    Color3 getPixel(int x, int y) const {
        if (x < 0 || x >= width || y < 0 || y >= height) return Color3(0);
        int idx = (y * width + x) * channels;
        return Color3(pixels[idx], pixels[idx + 1], pixels[idx + 2]);
    }
    
    // Set pixel at coordinates (x, y)
    void setPixel(int x, int y, const Color3& color) {
        if (x < 0 || x >= width || y < 0 || y >= height) return;
        int idx = (y * width + x) * channels;
        pixels[idx] = color.x;
        pixels[idx + 1] = color.y;
        pixels[idx + 2] = color.z;
        if (channels == 4) pixels[idx + 3] = 1.0f; // Alpha
    }
    
    // Get raw pixel data
    const float* data() const { return pixels.data(); }
    float* data() { return pixels.data(); }
    
    // Get size in bytes
    size_t size() const { return pixels.size() * sizeof(float); }
    
    // Check if valid
    bool isValid() const { return width > 0 && height > 0 && channels > 0 && !pixels.empty(); }
};

/**
 * @brief Image I/O class
 */
class ImageIO {
public:
    /**
     * @brief Save image to file
     * 
     * @param filename Output filename (format determined by extension)
     * @param image Image data to save
     * @param quality JPEG quality (0-100), ignored for other formats
     * @return true if successful, false otherwise
     */
    static bool saveImage(const std::string& filename, const ImageData& image, int quality = 95);
    
    /**
     * @brief Save image from Film object
     * 
     * @param filename Output filename
     * @param film Film object containing pixel data
     * @param quality JPEG quality (0-100)
     * @return true if successful, false otherwise
     */
    static bool saveFromFilm(const std::string& filename, const class Film& film, int quality = 95);
    
    /**
     * @brief Save image from raw pixel data
     * 
     * @param filename Output filename
     * @param pixels Raw pixel data (RGB float)
     * @param width Image width
     * @param height Image height
     * @param quality JPEG quality (0-100)
     * @return true if successful, false otherwise
     */
    static bool saveFromPixels(const std::string& filename, const float* pixels, 
                              int width, int height, int quality = 95);
    
    /**
     * @brief Load image from file
     * 
     * @param filename Input filename
     * @return ImageData object (check isValid() for success)
     */
    static ImageData loadImage(const std::string& filename);
    
    /**
     * @brief Get image format from filename extension
     * 
     * @param filename Input filename
     * @return ImageFormat enum value
     */
    static ImageFormat getFormatFromExtension(const std::string& filename);
    
    /**
     * @brief Check if format is supported for saving
     * 
     * @param format Image format to check
     * @return true if supported, false otherwise
     */
    static bool isFormatSupported(ImageFormat format);
    
    /**
     * @brief Apply tone mapping to image data
     * 
     * @param image Image to tone map (modified in place)
     * @param exposure Exposure adjustment
     * @param gamma Gamma correction
     */
    static void toneMap(ImageData& image, float exposure = 0.0f, float gamma = 2.2f);
    
    /**
     * @brief Convert HDR to LDR with tone mapping
     * 
     * @param hdrPixels HDR pixel data (float)
     * @param ldrPixels Output LDR pixel data (unsigned char)
     * @param width Image width
     * @param height Image height
     * @param exposure Exposure adjustment
     * @param gamma Gamma correction
     */
    static void hdrToLdr(const float* hdrPixels, unsigned char* ldrPixels,
                        int width, int height, float exposure = 0.0f, float gamma = 2.2f);

private:
    // Internal save functions for specific formats
    static bool savePNG(const std::string& filename, const ImageData& image);
    static bool saveJPEG(const std::string& filename, const ImageData& image, int quality);
    static bool saveEXR(const std::string& filename, const ImageData& image);
    static bool saveHDR(const std::string& filename, const ImageData& image);
    static bool saveBMP(const std::string& filename, const ImageData& image);
    static bool saveTGA(const std::string& filename, const ImageData& image);
    
    // Internal load functions
    static ImageData loadSTB(const std::string& filename);
    static ImageData loadEXR(const std::string& filename);
    
    // Utility functions
    static std::string getFileExtension(const std::string& filename);
    static std::string toLower(const std::string& str);
};

} // namespace photon
