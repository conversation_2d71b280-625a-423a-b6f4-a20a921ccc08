// src/core/material/disney_brdf.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Disney Principled BRDF Implementation

#include "disney_brdf.hpp"
#include "../sampler/sampler.hpp"
#include "../scene/intersection.hpp"
#include <algorithm>
#include <cmath>

namespace photon {

// Constants
static const float PI = 3.14159265359f;
static const float INV_PI = 1.0f / PI;
static const float EPSILON = 1e-6f;

DisneyBRDF::DisneyBRDF() {
    // Default parameters create a neutral plastic-like material
    m_params = DisneyBRDFParams();
    m_params.validate();
}

DisneyBRDF::DisneyBRDF(const DisneyBRDFParams& params) : m_params(params) {
    m_params.validate();
}

void DisneyBRDF::setParameters(const DisneyBRDFParams& params) {
    m_params = params;
    m_params.validate();
}

Color3 DisneyBRDF::eval(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    // Check if directions are on the same side of the surface
    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);
    
    if (cosTheta_o <= 0.0f || cosTheta_i <= 0.0f) {
        return Color3(0.0f);
    }
    
    Color3 result(0.0f);
    
    // Calculate component weights
    float diffuseWeight, specularWeight, sheenWeight, clearcoatWeight, subsurfaceWeight;
    calculateComponentWeights(wo, n, diffuseWeight, specularWeight, sheenWeight, clearcoatWeight, subsurfaceWeight);
    
    // Evaluate diffuse component
    if (diffuseWeight > EPSILON) {
        result += diffuseWeight * evalDiffuse(wo, wi, n);
    }
    
    // Evaluate specular component
    if (specularWeight > EPSILON) {
        result += specularWeight * evalSpecular(wo, wi, n);
    }
    
    // Evaluate sheen component
    if (sheenWeight > EPSILON && m_params.sheen > EPSILON) {
        result += sheenWeight * evalSheen(wo, wi, n);
    }
    
    // Evaluate clearcoat component
    if (clearcoatWeight > EPSILON && m_params.clearcoat > EPSILON) {
        result += clearcoatWeight * evalClearcoat(wo, wi, n);
    }

    // Evaluate subsurface component
    if (subsurfaceWeight > EPSILON && m_params.subsurface > EPSILON) {
        result += subsurfaceWeight * evalSubsurface(wo, wi, n);
    }

    return result;
}

Color3 DisneyBRDF::sample(const Vec3& wo, const Vec3& n, Sampler& sampler, Vec3& wi, float& pdf) const {
    // Calculate component weights
    float diffuseWeight, specularWeight, sheenWeight, clearcoatWeight, subsurfaceWeight;
    calculateComponentWeights(wo, n, diffuseWeight, specularWeight, sheenWeight, clearcoatWeight, subsurfaceWeight);
    
    // Normalize weights
    float totalWeight = diffuseWeight + specularWeight + sheenWeight + clearcoatWeight + subsurfaceWeight;
    if (totalWeight <= EPSILON) {
        pdf = 0.0f;
        return Color3(0.0f);
    }

    diffuseWeight /= totalWeight;
    specularWeight /= totalWeight;
    sheenWeight /= totalWeight;
    clearcoatWeight /= totalWeight;
    subsurfaceWeight /= totalWeight;
    
    // Sample component based on weights
    float u = sampler.get1D();
    float componentPdf;
    
    if (u < diffuseWeight) {
        // Sample diffuse
        wi = sampleDiffuse(wo, n, sampler, componentPdf);
        pdf = diffuseWeight * componentPdf;
    } else if (u < diffuseWeight + specularWeight) {
        // Sample specular
        wi = sampleSpecular(wo, n, sampler, componentPdf);
        pdf = specularWeight * componentPdf;
    } else if (u < diffuseWeight + specularWeight + sheenWeight) {
        // Sample sheen (use diffuse sampling for now)
        wi = sampleDiffuse(wo, n, sampler, componentPdf);
        pdf = sheenWeight * componentPdf;
    } else if (u < diffuseWeight + specularWeight + sheenWeight + clearcoatWeight) {
        // Sample clearcoat
        wi = sampleClearcoat(wo, n, sampler, componentPdf);
        pdf = clearcoatWeight * componentPdf;
    } else {
        // Sample subsurface
        wi = sampleSubsurface(wo, n, sampler, componentPdf);
        pdf = subsurfaceWeight * componentPdf;
    }
    
    // Add PDFs from other components
    if (diffuseWeight > EPSILON) {
        pdf += diffuseWeight * pdfDiffuse(wo, wi, n);
    }
    if (specularWeight > EPSILON) {
        pdf += specularWeight * pdfSpecular(wo, wi, n);
    }
    if (clearcoatWeight > EPSILON) {
        pdf += clearcoatWeight * pdfClearcoat(wo, wi, n);
    }
    if (subsurfaceWeight > EPSILON) {
        pdf += subsurfaceWeight * pdfSubsurface(wo, wi, n);
    }
    
    // Evaluate BRDF for sampled direction
    return eval(wo, wi, n);
}

float DisneyBRDF::pdf(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    // Calculate component weights
    float diffuseWeight, specularWeight, sheenWeight, clearcoatWeight, subsurfaceWeight;
    calculateComponentWeights(wo, n, diffuseWeight, specularWeight, sheenWeight, clearcoatWeight, subsurfaceWeight);

    // Normalize weights
    float totalWeight = diffuseWeight + specularWeight + sheenWeight + clearcoatWeight + subsurfaceWeight;
    if (totalWeight <= EPSILON) {
        return 0.0f;
    }

    diffuseWeight /= totalWeight;
    specularWeight /= totalWeight;
    sheenWeight /= totalWeight;
    clearcoatWeight /= totalWeight;
    subsurfaceWeight /= totalWeight;

    float pdf = 0.0f;

    if (diffuseWeight > EPSILON) {
        pdf += diffuseWeight * pdfDiffuse(wo, wi, n);
    }
    if (specularWeight > EPSILON) {
        pdf += specularWeight * pdfSpecular(wo, wi, n);
    }
    if (sheenWeight > EPSILON) {
        pdf += sheenWeight * pdfDiffuse(wo, wi, n); // Use diffuse PDF for sheen
    }
    if (clearcoatWeight > EPSILON) {
        pdf += clearcoatWeight * pdfClearcoat(wo, wi, n);
    }
    if (subsurfaceWeight > EPSILON) {
        pdf += subsurfaceWeight * pdfSubsurface(wo, wi, n);
    }

    return pdf;
}

Color3 DisneyBRDF::getAlbedo() const {
    // Return effective albedo for energy conservation
    Color3 albedo = m_params.baseColor;
    
    // Reduce albedo based on metallic parameter
    if (m_params.metallic > EPSILON) {
        albedo = albedo * (1.0f - m_params.metallic) + albedo * m_params.metallic * 0.5f;
    }
    
    return albedo;
}

Color3 DisneyBRDF::evalDiffuse(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);
    
    if (cosTheta_o <= 0.0f || cosTheta_i <= 0.0f) {
        return Color3(0.0f);
    }
    
    // Disney diffuse model
    Vec3 h = (wo + wi).normalized();
    float cosTheta_d = h.dot(wi);
    
    float fd = disneyDiffuse(cosTheta_o, cosTheta_i, cosTheta_d, m_params.roughness);
    
    // Mix between base color and black based on metallic parameter
    Color3 diffuseColor = m_params.baseColor * (1.0f - m_params.metallic);
    
    return diffuseColor * fd * INV_PI;
}

Color3 DisneyBRDF::evalSpecular(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    Vec3 h = (wo + wi).normalized();
    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);
    float cosTheta_h = h.dot(n);
    
    if (cosTheta_o <= 0.0f || cosTheta_i <= 0.0f || cosTheta_h <= 0.0f) {
        return Color3(0.0f);
    }
    
    // GGX distribution
    float D = distributionGGX(h, n, m_params.roughness);
    
    // Smith masking-shadowing
    float G = geometrySmith(wo, wi, n, m_params.roughness);
    
    // Fresnel term
    Color3 F0 = Color3(0.04f); // Dielectric base
    if (m_params.metallic > EPSILON) {
        F0 = m_params.baseColor * m_params.metallic + F0 * (1.0f - m_params.metallic);
    }
    
    // Apply specular tint
    if (m_params.specularTint > EPSILON) {
        Color3 tint = m_params.baseColor.normalized();
        F0 = F0 * (1.0f - m_params.specularTint) + tint * m_params.specularTint;
    }
    
    Color3 F = fresnelSchlick(wo.dot(h), F0);
    
    // Apply specular parameter
    F = F * m_params.specular;
    
    // Cook-Torrance BRDF
    float denominator = 4.0f * cosTheta_o * cosTheta_i;
    if (denominator < EPSILON) {
        return Color3(0.0f);
    }
    
    return (D * G * F) / denominator;
}

Color3 DisneyBRDF::evalSheen(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    Vec3 h = (wo + wi).normalized();
    float cosTheta_d = h.dot(wi);
    
    // Sheen is a simple cosine-weighted term
    float sheen = std::pow(1.0f - cosTheta_d, 5.0f);
    
    // Sheen color
    Color3 sheenColor = Color3(1.0f);
    if (m_params.sheenTint > EPSILON) {
        Color3 tint = m_params.baseColor.normalized();
        sheenColor = sheenColor * (1.0f - m_params.sheenTint) + tint * m_params.sheenTint;
    }
    
    return sheenColor * sheen * m_params.sheen;
}

Color3 DisneyBRDF::evalClearcoat(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    Vec3 h = (wo + wi).normalized();
    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);
    float cosTheta_h = h.dot(n);
    
    if (cosTheta_o <= 0.0f || cosTheta_i <= 0.0f || cosTheta_h <= 0.0f) {
        return Color3(0.0f);
    }
    
    // Clearcoat uses a separate roughness parameter
    float clearcoatRoughness = std::lerp(0.1f, 0.001f, m_params.clearcoatGloss);
    
    // GGX distribution for clearcoat
    float D = distributionGGX(h, n, clearcoatRoughness);
    
    // Simplified geometry term for clearcoat
    float G = geometrySmith(wo, wi, n, clearcoatRoughness);
    
    // Fixed IOR for clearcoat (1.5)
    float F = fresnelSchlick(wo.dot(h), 0.04f);
    
    float denominator = 4.0f * cosTheta_o * cosTheta_i;
    if (denominator < EPSILON) {
        return Color3(0.0f);
    }
    
    return Color3(m_params.clearcoat * D * G * F / denominator);
}

Color3 DisneyBRDF::evalSubsurface(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);

    if (cosTheta_o <= 0.0f || cosTheta_i <= 0.0f) {
        return Color3(0.0f);
    }

    // Subsurface scattering approximation using diffusion theory
    // Based on "A Practical Model for Subsurface Light Transport" by Jensen et al.

    // Calculate subsurface approximation
    Color3 subsurfaceColor = subsurfaceApproximation(wo, wi, n);

    // Apply subsurface parameter as blend factor
    Color3 diffuseColor = m_params.baseColor * (1.0f - m_params.metallic);

    // Fresnel weight for subsurface
    float fresnelWeight = subsurfaceFresnelWeight(cosTheta_o);

    // Blend between regular diffuse and subsurface
    Color3 regularDiffuse = diffuseColor * INV_PI;
    Color3 result = regularDiffuse * (1.0f - m_params.subsurface) +
                   subsurfaceColor * m_params.subsurface * fresnelWeight;

    return result;
}

Vec3 DisneyBRDF::sampleDiffuse(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const {
    // Cosine-weighted hemisphere sampling
    float u1 = sampler.get1D();
    float u2 = sampler.get1D();

    float cosTheta = std::sqrt(u1);
    float sinTheta = std::sqrt(1.0f - u1);
    float phi = 2.0f * PI * u2;

    Vec3 localWi(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);

    // Transform to world space
    Vec3 tangent, bitangent;
    buildCoordinateSystem(n, tangent, bitangent);
    Vec3 wi = toWorld(localWi, n, tangent, bitangent);

    pdf = cosTheta * INV_PI;
    return wi;
}

Vec3 DisneyBRDF::sampleSpecular(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const {
    // Sample GGX distribution
    float u1 = sampler.get1D();
    float u2 = sampler.get1D();

    float alpha = m_params.roughness * m_params.roughness;
    float cosTheta = std::sqrt((1.0f - u1) / (1.0f + (alpha * alpha - 1.0f) * u1));
    float sinTheta = std::sqrt(1.0f - cosTheta * cosTheta);
    float phi = 2.0f * PI * u2;

    Vec3 localH(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);

    // Transform to world space
    Vec3 tangent, bitangent;
    buildCoordinateSystem(n, tangent, bitangent);
    Vec3 h = toWorld(localH, n, tangent, bitangent);

    // Reflect wo around h to get wi
    Vec3 wi = Vec3::reflect(-wo, h);

    // Calculate PDF
    float D = distributionGGX(h, n, m_params.roughness);
    pdf = D * cosTheta / (4.0f * wo.dot(h));

    return wi;
}

Vec3 DisneyBRDF::sampleClearcoat(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const {
    // Similar to specular but with clearcoat roughness
    float u1 = sampler.get1D();
    float u2 = sampler.get1D();

    float clearcoatRoughness = std::lerp(0.1f, 0.001f, m_params.clearcoatGloss);
    float alpha = clearcoatRoughness * clearcoatRoughness;
    float cosTheta = std::sqrt((1.0f - u1) / (1.0f + (alpha * alpha - 1.0f) * u1));
    float sinTheta = std::sqrt(1.0f - cosTheta * cosTheta);
    float phi = 2.0f * PI * u2;

    Vec3 localH(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);

    Vec3 tangent, bitangent;
    buildCoordinateSystem(n, tangent, bitangent);
    Vec3 h = toWorld(localH, n, tangent, bitangent);

    Vec3 wi = Vec3::reflect(-wo, h);

    float D = distributionGGX(h, n, clearcoatRoughness);
    pdf = D * cosTheta / (4.0f * wo.dot(h));

    return wi;
}

float DisneyBRDF::pdfDiffuse(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    float cosTheta = std::max(0.0f, wi.dot(n));
    return cosTheta * INV_PI;
}

float DisneyBRDF::pdfSpecular(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    Vec3 h = (wo + wi).normalized();
    float cosTheta_h = h.dot(n);

    if (cosTheta_h <= 0.0f) return 0.0f;

    float D = distributionGGX(h, n, m_params.roughness);
    return D * cosTheta_h / (4.0f * wo.dot(h));
}

float DisneyBRDF::pdfClearcoat(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    Vec3 h = (wo + wi).normalized();
    float cosTheta_h = h.dot(n);

    if (cosTheta_h <= 0.0f) return 0.0f;

    float clearcoatRoughness = std::lerp(0.1f, 0.001f, m_params.clearcoatGloss);
    float D = distributionGGX(h, n, clearcoatRoughness);
    return D * cosTheta_h / (4.0f * wo.dot(h));
}

float DisneyBRDF::fresnelSchlick(float cosTheta, float F0) const {
    return F0 + (1.0f - F0) * std::pow(1.0f - cosTheta, 5.0f);
}

Color3 DisneyBRDF::fresnelSchlick(float cosTheta, const Color3& F0) const {
    float factor = std::pow(1.0f - cosTheta, 5.0f);
    return F0 + (Color3(1.0f) - F0) * factor;
}

float DisneyBRDF::distributionGGX(const Vec3& h, const Vec3& n, float roughness) const {
    float alpha = roughness * roughness;
    float alpha2 = alpha * alpha;
    float cosTheta = h.dot(n);
    float cosTheta2 = cosTheta * cosTheta;

    float denom = cosTheta2 * (alpha2 - 1.0f) + 1.0f;
    return alpha2 / (PI * denom * denom);
}

float DisneyBRDF::geometrySmith(const Vec3& wo, const Vec3& wi, const Vec3& n, float roughness) const {
    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);

    float G1_o = geometrySchlickGGX(cosTheta_o, roughness);
    float G1_i = geometrySchlickGGX(cosTheta_i, roughness);

    return G1_o * G1_i;
}

float DisneyBRDF::geometrySchlickGGX(float cosTheta, float roughness) const {
    float alpha = roughness * roughness;
    float k = alpha / 2.0f; // Direct lighting

    return cosTheta / (cosTheta * (1.0f - k) + k);
}

float DisneyBRDF::disneyDiffuse(float cosTheta_o, float cosTheta_i, float cosTheta_d, float roughness) const {
    float fd90 = 0.5f + 2.0f * cosTheta_d * cosTheta_d * roughness;
    float fd_o = 1.0f + (fd90 - 1.0f) * std::pow(1.0f - cosTheta_o, 5.0f);
    float fd_i = 1.0f + (fd90 - 1.0f) * std::pow(1.0f - cosTheta_i, 5.0f);

    return fd_o * fd_i;
}

float DisneyBRDF::disneyFresnel(float cosTheta, float eta, float k) const {
    // Conductor Fresnel for metals
    float cosTheta2 = cosTheta * cosTheta;
    float sinTheta2 = 1.0f - cosTheta2;
    float eta2 = eta * eta;
    float k2 = k * k;

    float t0 = eta2 - k2 - sinTheta2;
    float a2plusb2 = std::sqrt(t0 * t0 + 4.0f * eta2 * k2);
    float t1 = a2plusb2 + cosTheta2;
    float a = std::sqrt(0.5f * (a2plusb2 + t0));
    float t2 = 2.0f * a * cosTheta;
    float Rs = (t1 - t2) / (t1 + t2);

    float t3 = cosTheta2 * a2plusb2 + sinTheta2 * sinTheta2;
    float t4 = t2 * sinTheta2;
    float Rp = Rs * (t3 - t4) / (t3 + t4);

    return 0.5f * (Rs + Rp);
}

void DisneyBRDF::buildCoordinateSystem(const Vec3& n, Vec3& tangent, Vec3& bitangent) const {
    if (std::abs(n.x) > 0.1f) {
        tangent = Vec3(0, 1, 0).cross(n).normalized();
    } else {
        tangent = Vec3(1, 0, 0).cross(n).normalized();
    }
    bitangent = n.cross(tangent);
}

Vec3 DisneyBRDF::toLocal(const Vec3& v, const Vec3& n, const Vec3& tangent, const Vec3& bitangent) const {
    return Vec3(v.dot(tangent), v.dot(bitangent), v.dot(n));
}

Vec3 DisneyBRDF::toWorld(const Vec3& v, const Vec3& n, const Vec3& tangent, const Vec3& bitangent) const {
    return v.x * tangent + v.y * bitangent + v.z * n;
}

void DisneyBRDF::calculateComponentWeights(const Vec3& wo, const Vec3& n,
                                         float& diffuseWeight, float& specularWeight,
                                         float& sheenWeight, float& clearcoatWeight,
                                         float& subsurfaceWeight) const {
    float cosTheta = wo.dot(n);

    // Base weights
    diffuseWeight = 1.0f - m_params.metallic;
    specularWeight = 1.0f;
    sheenWeight = m_params.sheen;
    clearcoatWeight = m_params.clearcoat;
    subsurfaceWeight = m_params.subsurface * (1.0f - m_params.metallic);

    // Adjust based on viewing angle (Fresnel effect)
    float F = fresnelSchlick(cosTheta, 0.04f);
    diffuseWeight *= (1.0f - F) * (1.0f - m_params.subsurface * 0.5f);
    specularWeight *= F;

    // Subsurface is stronger at grazing angles
    subsurfaceWeight *= (1.0f - F * 0.5f);
}

// Disney Material Presets Implementation
DisneyBRDFParams DisneyMaterialPresets::createPlastic(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.3f;
    params.specular = 0.5f;
    params.specularTint = 0.0f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createMetal(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 1.0f;
    params.roughness = 0.1f;
    params.specular = 1.0f;
    params.specularTint = 0.0f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createGlass(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.0f;
    params.specular = 1.0f;
    params.specularTint = 0.0f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createWood(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.8f;
    params.specular = 0.2f;
    params.specularTint = 0.1f;
    params.subsurface = 0.1f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createFabric(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.9f;
    params.specular = 0.0f;
    params.sheen = 1.0f;
    params.sheenTint = 0.5f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createSkin(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.4f;
    params.specular = 0.3f;
    params.subsurface = 0.8f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createCeramic(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.1f;
    params.specular = 0.8f;
    params.clearcoat = 0.5f;
    params.clearcoatGloss = 0.9f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createRubber(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.9f;
    params.specular = 0.1f;
    params.subsurface = 0.2f;
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createWax(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.3f;
    params.specular = 0.4f;
    params.subsurface = 0.9f;  // High subsurface for translucent wax
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createMarble(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.1f;
    params.specular = 0.7f;
    params.subsurface = 0.6f;  // Medium subsurface for marble translucency
    return params;
}

DisneyBRDFParams DisneyMaterialPresets::createJade(const Color3& color) {
    DisneyBRDFParams params;
    params.baseColor = color;
    params.metallic = 0.0f;
    params.roughness = 0.05f;
    params.specular = 0.8f;
    params.subsurface = 0.7f;  // High subsurface for jade translucency
    return params;
}

bool DisneyMaterialPresets::validateEnergyConservation(const DisneyBRDFParams& params) {
    // Check if material parameters respect energy conservation
    float totalReflectance = params.baseColor.luminance();

    // Metallic materials can have higher reflectance
    if (params.metallic > 0.5f) {
        return totalReflectance <= 1.0f;
    }

    // Dielectric materials should have lower reflectance
    return totalReflectance <= 0.9f;
}

DisneyBRDFParams DisneyMaterialPresets::enforceEnergyConservation(const DisneyBRDFParams& params) {
    DisneyBRDFParams result = params;

    // Clamp base color to ensure energy conservation
    float maxComponent = std::max({result.baseColor.r, result.baseColor.g, result.baseColor.b});
    if (maxComponent > 1.0f) {
        result.baseColor = result.baseColor / maxComponent;
    }

    // Ensure metallic materials don't have too high albedo
    if (result.metallic > 0.5f) {
        float luminance = result.baseColor.luminance();
        if (luminance > 0.9f) {
            float scale = 0.9f / luminance;
            result.baseColor = result.baseColor * scale;
        }
    }

    return result;
}

Vec3 DisneyBRDF::sampleSubsurface(const Vec3& wo, const Vec3& n, Sampler& sampler, float& pdf) const {
    // For subsurface scattering, we use cosine-weighted hemisphere sampling
    // similar to diffuse, but with modified distribution

    float u1 = sampler.get1D();
    float u2 = sampler.get1D();

    // Slightly modified cosine distribution for subsurface
    float cosTheta = std::sqrt(u1 * (1.0f + m_params.subsurface * 0.5f));
    cosTheta = std::min(cosTheta, 1.0f);
    float sinTheta = std::sqrt(1.0f - cosTheta * cosTheta);
    float phi = 2.0f * PI * u2;

    Vec3 localWi(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);

    // Transform to world space
    Vec3 tangent, bitangent;
    buildCoordinateSystem(n, tangent, bitangent);
    Vec3 wi = toWorld(localWi, n, tangent, bitangent);

    pdf = cosTheta * INV_PI * (1.0f + m_params.subsurface * 0.2f);
    return wi;
}

float DisneyBRDF::pdfSubsurface(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    float cosTheta = std::max(0.0f, wi.dot(n));
    return cosTheta * INV_PI * (1.0f + m_params.subsurface * 0.2f);
}

float DisneyBRDF::subsurfaceProfile(float distance, float scatteringDistance) const {
    // Simplified subsurface profile based on diffusion approximation
    // Using exponential falloff with distance
    if (scatteringDistance <= 0.0f) return 0.0f;

    float normalizedDistance = distance / scatteringDistance;
    return std::exp(-normalizedDistance * 3.0f) / (scatteringDistance * scatteringDistance);
}

Color3 DisneyBRDF::subsurfaceApproximation(const Vec3& wo, const Vec3& wi, const Vec3& n) const {
    // Simplified subsurface scattering approximation
    // Based on the idea that subsurface scattering creates a softer, more diffuse appearance

    float cosTheta_o = wo.dot(n);
    float cosTheta_i = wi.dot(n);

    // Calculate the "effective" scattering based on angle
    float scatteringFactor = 1.0f + m_params.subsurface * (1.0f - cosTheta_o) * (1.0f - cosTheta_i);

    // Base diffuse color
    Color3 diffuseColor = m_params.baseColor * (1.0f - m_params.metallic);

    // Apply subsurface scattering effect
    // This creates a softer, more translucent appearance
    Color3 subsurfaceColor = diffuseColor * scatteringFactor * INV_PI;

    // Add slight color shift for subsurface (warmer tones)
    Color3 subsurfaceShift = Color3(1.1f, 1.05f, 0.95f);
    subsurfaceColor = subsurfaceColor * subsurfaceShift;

    return subsurfaceColor;
}

float DisneyBRDF::subsurfaceFresnelWeight(float cosTheta) const {
    // Fresnel weight for subsurface scattering
    // Subsurface is more prominent at grazing angles
    float F = fresnelSchlick(cosTheta, 0.04f);
    return 1.0f - F * 0.8f; // Reduce subsurface at high Fresnel angles
}

} // namespace photon
