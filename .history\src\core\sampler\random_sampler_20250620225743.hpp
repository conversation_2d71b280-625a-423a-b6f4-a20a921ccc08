// src/core/sampler/random_sampler.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Random sampler implementation - Extended interface

#pragma once

#include "sampler.hpp"
#include "../math/vec2.hpp"
#include <random>

namespace photon {

/**
 * @brief Extended random sampler interface for Disney BRDF tests
 *
 * This provides additional methods needed by the Disney BRDF system
 * while maintaining compatibility with the base Sampler interface.
 */
class ExtendedRandomSampler {
public:
    /**
     * @brief Constructor
     * @param seed Random seed (0 for time-based seed)
     */
    explicit ExtendedRandomSampler(uint32_t seed = 0);

    /**
     * @brief Copy constructor
     */
    ExtendedRandomSampler(const ExtendedRandomSampler& other);

    /**
     * @brief Assignment operator
     */
    ExtendedRandomSampler& operator=(const ExtendedRandomSampler& other);

    /**
     * @brief Destructor
     */
    virtual ~ExtendedRandomSampler() = default;

    // Extended interface for Disney BRDF
    float next1D();
    Vec2 next2D();
    void setSeed(uint32_t seed);
    std::string getName() const { return "ExtendedRandom"; }

    /**
     * @brief Reset sampler state
     */
    void reset();

    /**
     * @brief Get current seed
     */
    uint32_t getSeed() const { return m_seed; }

private:
    uint32_t m_seed;
    mutable std::mt19937 m_generator;
    mutable std::uniform_real_distribution<float> m_distribution;

    /**
     * @brief Initialize generator with seed
     */
    void initializeGenerator();
};

/**
 * @brief Fast random sampler using simple LCG
 * 
 * Faster than RandomSampler but lower quality randomness.
 * Suitable for quick tests and prototyping.
 */
class FastRandomSampler : public Sampler {
public:
    /**
     * @brief Constructor
     * @param seed Random seed (0 for time-based seed)
     */
    explicit FastRandomSampler(uint32_t seed = 0);
    
    // Sampler interface implementation
    float next1D() override;
    Vec2 next2D() override;
    void setSeed(uint32_t seed) override;
    std::unique_ptr<Sampler> clone() const override;
    std::string getName() const override { return "FastRandom"; }
    
    /**
     * @brief Reset sampler state
     */
    void reset() override;
    
    /**
     * @brief Get current seed
     */
    uint32_t getSeed() const { return m_originalSeed; }

private:
    uint32_t m_originalSeed;
    mutable uint32_t m_state;
    
    /**
     * @brief Linear congruential generator
     */
    uint32_t lcg() const;
    
    /**
     * @brief Convert uint32 to float [0,1)
     */
    float uint32ToFloat(uint32_t value) const;
};

} // namespace photon
