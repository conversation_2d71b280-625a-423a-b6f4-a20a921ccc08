// examples/pattern_generators_demo.cpp
// PhotonRender - Pattern Generators Demo
// Demonstrates geometric pattern generation capabilities

#include <iostream>
#include <memory>
#include <iomanip>
#include <fstream>
#include <chrono>
#include "../src/core/texture/texture.hpp"
#include "../src/core/math/vec2.hpp"
#include "../src/core/math/color3.hpp"

using namespace photon;

/**
 * @brief Demo application for Pattern Generators
 */
class PatternGeneratorDemo {
public:
    /**
     * @brief Run all pattern generator demos
     */
    static void runAllDemos() {
        std::cout << "=== PhotonRender Pattern Generators Demo ===\n\n";
        
        demonstrateBasicPatterns();
        demonstratePatternParameters();
        demonstratePatternTransformations();
        demonstrateAntiAliasing();
        demonstrateMaterialPatterns();
        demonstratePerformance();
        generatePatternSamples();
        
        std::cout << "\n=== Demo Complete ===\n";
        std::cout << "Pattern Generators system ready for production use!\n";
    }

private:
    /**
     * @brief Demonstrate basic pattern types
     */
    static void demonstrateBasicPatterns() {
        std::cout << "=== Basic Pattern Types Demo ===\n";
        
        // Create different pattern types
        PatternTexture checkerboard(PatternType::CHECKERBOARD, 8.0f, 
                                   Color3(1.0f, 1.0f, 1.0f), Color3(0.0f, 0.0f, 0.0f));
        PatternTexture stripes(PatternType::STRIPES, 6.0f,
                              Color3(1.0f, 0.0f, 0.0f), Color3(0.0f, 0.0f, 1.0f));
        PatternTexture dots(PatternType::DOTS, 10.0f,
                           Color3(0.0f, 1.0f, 0.0f), Color3(1.0f, 1.0f, 0.0f));
        PatternTexture grid(PatternType::GRID, 12.0f,
                           Color3(0.8f, 0.8f, 0.8f), Color3(0.2f, 0.2f, 0.2f));
        
        // Sample patterns at specific coordinates
        Vec2 sampleCoord(0.3f, 0.7f);
        
        std::cout << "Sampling patterns at UV(" << sampleCoord.x << ", " << sampleCoord.y << "):\n";
        
        Color3 checkerColor = checkerboard.sample(sampleCoord);
        std::cout << "  Checkerboard: RGB(" << checkerColor.r << ", " << checkerColor.g << ", " << checkerColor.b << ")\n";
        
        Color3 stripeColor = stripes.sample(sampleCoord);
        std::cout << "  Stripes: RGB(" << stripeColor.r << ", " << stripeColor.g << ", " << stripeColor.b << ")\n";
        
        Color3 dotColor = dots.sample(sampleCoord);
        std::cout << "  Dots: RGB(" << dotColor.r << ", " << dotColor.g << ", " << dotColor.b << ")\n";
        
        Color3 gridColor = grid.sample(sampleCoord);
        std::cout << "  Grid: RGB(" << gridColor.r << ", " << gridColor.g << ", " << gridColor.b << ")\n";
        
        std::cout << "\n";
    }
    
    /**
     * @brief Demonstrate pattern parameters
     */
    static void demonstratePatternParameters() {
        std::cout << "=== Pattern Parameters Demo ===\n";
        
        // Create pattern with custom parameters
        PatternParams params = PatternParams::defaultParams();
        params.scale = 4.0f;
        params.rotation = 0.785f; // 45 degrees
        params.offset = Vec2(0.1f, 0.2f);
        params.lineWidth = 0.08f;
        params.dotSize = 0.4f;
        params.antiAlias = true;
        
        PatternTexture customPattern(PatternType::CHECKERBOARD, params,
                                   Color3(1.0f, 0.5f, 0.0f), Color3(0.0f, 0.5f, 1.0f));
        
        std::cout << "Custom pattern parameters:\n";
        std::cout << "  Scale: " << params.scale << "\n";
        std::cout << "  Rotation: " << params.rotation << " radians (" << (params.rotation * 180.0f / 3.14159f) << " degrees)\n";
        std::cout << "  Offset: (" << params.offset.x << ", " << params.offset.y << ")\n";
        std::cout << "  Line Width: " << params.lineWidth << "\n";
        std::cout << "  Dot Size: " << params.dotSize << "\n";
        std::cout << "  Anti-aliasing: " << (params.antiAlias ? "Enabled" : "Disabled") << "\n";
        
        // Test different pattern types with same parameters
        customPattern.setPatternType(PatternType::STRIPES);
        Color3 stripeResult = customPattern.sample(Vec2(0.5f, 0.5f));
        std::cout << "  Stripes with custom params: RGB(" << stripeResult.r << ", " << stripeResult.g << ", " << stripeResult.b << ")\n";
        
        customPattern.setPatternType(PatternType::DOTS);
        Color3 dotResult = customPattern.sample(Vec2(0.5f, 0.5f));
        std::cout << "  Dots with custom params: RGB(" << dotResult.r << ", " << dotResult.g << ", " << dotResult.b << ")\n";
        
        customPattern.setPatternType(PatternType::GRID);
        Color3 gridResult = customPattern.sample(Vec2(0.5f, 0.5f));
        std::cout << "  Grid with custom params: RGB(" << gridResult.r << ", " << gridResult.g << ", " << gridResult.b << ")\n";
        
        std::cout << "\n";
    }
    
    /**
     * @brief Demonstrate pattern transformations
     */
    static void demonstratePatternTransformations() {
        std::cout << "=== Pattern Transformations Demo ===\n";
        
        PatternTexture basePattern(PatternType::CHECKERBOARD, 4.0f);
        
        // Test different rotations
        float rotations[] = {0.0f, 0.785f, 1.57f, 2.356f}; // 0°, 45°, 90°, 135°
        std::string rotationNames[] = {"0°", "45°", "90°", "135°"};
        
        Vec2 testUV(0.6f, 0.4f);
        
        std::cout << "Checkerboard pattern at UV(" << testUV.x << ", " << testUV.y << ") with different rotations:\n";
        
        for (int i = 0; i < 4; ++i) {
            PatternParams params = PatternParams::defaultParams();
            params.rotation = rotations[i];
            basePattern.setPatternParams(params);
            
            float value = basePattern.sampleFloat(testUV);
            std::cout << "  " << rotationNames[i] << " rotation: " << std::fixed << std::setprecision(3) << value << "\n";
        }
        
        // Test different scales
        float scales[] = {1.0f, 2.0f, 4.0f, 8.0f};
        std::cout << "\nCheckerboard pattern with different scales:\n";
        
        for (float scale : scales) {
            PatternParams params = PatternParams::defaultParams();
            params.scale = scale;
            basePattern.setPatternParams(params);
            
            float value = basePattern.sampleFloat(testUV);
            std::cout << "  Scale " << scale << "x: " << std::fixed << std::setprecision(3) << value << "\n";
        }
        
        std::cout << "\n";
    }
    
    /**
     * @brief Demonstrate anti-aliasing effects
     */
    static void demonstrateAntiAliasing() {
        std::cout << "=== Anti-Aliasing Demo ===\n";
        
        PatternParams aliasedParams = PatternParams::defaultParams();
        aliasedParams.antiAlias = false;
        aliasedParams.scale = 8.0f;
        
        PatternParams smoothParams = PatternParams::defaultParams();
        smoothParams.antiAlias = true;
        smoothParams.scale = 8.0f;
        
        PatternTexture aliasedPattern(PatternType::CHECKERBOARD, aliasedParams);
        PatternTexture smoothPattern(PatternType::CHECKERBOARD, smoothParams);
        
        // Sample near pattern edges where anti-aliasing makes a difference
        Vec2 edgeCoords[] = {
            Vec2(0.124f, 0.124f),  // Near checkerboard edge
            Vec2(0.249f, 0.249f),  // Another edge
            Vec2(0.374f, 0.374f)   // Another edge
        };
        
        std::cout << "Comparing aliased vs anti-aliased patterns near edges:\n";
        
        for (int i = 0; i < 3; ++i) {
            float aliasedValue = aliasedPattern.sampleFloat(edgeCoords[i]);
            float smoothValue = smoothPattern.sampleFloat(edgeCoords[i]);
            
            std::cout << "  UV(" << edgeCoords[i].x << ", " << edgeCoords[i].y << "): ";
            std::cout << "Aliased=" << std::fixed << std::setprecision(3) << aliasedValue;
            std::cout << ", Smooth=" << std::fixed << std::setprecision(3) << smoothValue << "\n";
        }
        
        std::cout << "\n";
    }
    
    /**
     * @brief Demonstrate material pattern examples
     */
    static void demonstrateMaterialPatterns() {
        std::cout << "=== Material Pattern Examples ===\n";
        
        // Fabric pattern
        PatternParams fabricParams = PatternParams::defaultParams();
        fabricParams.scale = 20.0f;
        fabricParams.lineWidth = 0.05f;
        PatternTexture fabric(PatternType::GRID, fabricParams,
                             Color3(0.8f, 0.7f, 0.6f), Color3(0.6f, 0.5f, 0.4f));
        
        // Tile pattern
        PatternParams tileParams = PatternParams::defaultParams();
        tileParams.scale = 6.0f;
        tileParams.lineWidth = 0.1f;
        PatternTexture tiles(PatternType::GRID, tileParams,
                            Color3(0.9f, 0.9f, 0.8f), Color3(0.3f, 0.3f, 0.3f));
        
        // Polka dots pattern
        PatternParams dotsParams = PatternParams::defaultParams();
        dotsParams.scale = 8.0f;
        dotsParams.dotSize = 0.3f;
        PatternTexture polkaDots(PatternType::DOTS, dotsParams,
                                Color3(1.0f, 0.2f, 0.2f), Color3(1.0f, 1.0f, 1.0f));
        
        // Racing stripes pattern
        PatternParams stripesParams = PatternParams::defaultParams();
        stripesParams.scale = 4.0f;
        stripesParams.rotation = 0.2f;
        PatternTexture racingStripes(PatternType::STRIPES, stripesParams,
                                    Color3(0.0f, 0.0f, 0.0f), Color3(1.0f, 1.0f, 0.0f));
        
        Vec2 materialUV(0.5f, 0.5f);
        
        std::cout << "Material pattern samples at UV(" << materialUV.x << ", " << materialUV.y << "):\n";
        
        Color3 fabricColor = fabric.sample(materialUV);
        std::cout << "  Fabric: RGB(" << fabricColor.r << ", " << fabricColor.g << ", " << fabricColor.b << ")\n";
        
        Color3 tileColor = tiles.sample(materialUV);
        std::cout << "  Tiles: RGB(" << tileColor.r << ", " << tileColor.g << ", " << tileColor.b << ")\n";
        
        Color3 dotsColor = polkaDots.sample(materialUV);
        std::cout << "  Polka Dots: RGB(" << dotsColor.r << ", " << dotsColor.g << ", " << dotsColor.b << ")\n";
        
        Color3 stripesColor = racingStripes.sample(materialUV);
        std::cout << "  Racing Stripes: RGB(" << stripesColor.r << ", " << stripesColor.g << ", " << stripesColor.b << ")\n";
        
        std::cout << "\n";
    }
    
    /**
     * @brief Demonstrate performance characteristics
     */
    static void demonstratePerformance() {
        std::cout << "=== Performance Demo ===\n";
        
        PatternTexture patterns[] = {
            PatternTexture(PatternType::CHECKERBOARD, 8.0f),
            PatternTexture(PatternType::STRIPES, 8.0f),
            PatternTexture(PatternType::DOTS, 8.0f),
            PatternTexture(PatternType::GRID, 8.0f)
        };
        
        std::string patternNames[] = {"Checkerboard", "Stripes", "Dots", "Grid"};
        
        const int numSamples = 100000;
        
        for (int p = 0; p < 4; ++p) {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            float totalValue = 0.0f;
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 317) / 317.0f; // Prime number for better distribution
                float v = float(i % 331) / 331.0f; // Prime number for better distribution
                totalValue += patterns[p].sampleFloat(Vec2(u, v));
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            
            float avgTimeNs = float(duration.count()) / numSamples;
            float samplesPerSecond = 1e9f / avgTimeNs;
            
            std::cout << "  " << patternNames[p] << ": " << std::fixed << std::setprecision(2) 
                      << avgTimeNs << "ns per sample (" << std::scientific << std::setprecision(2) 
                      << samplesPerSecond << " samples/sec)\n";
        }
        
        std::cout << "\n";
    }
    
    /**
     * @brief Generate pattern samples for visualization
     */
    static void generatePatternSamples() {
        std::cout << "=== Pattern Sample Generation ===\n";
        
        PatternTexture checkerboard(PatternType::CHECKERBOARD, 4.0f);
        
        std::cout << "Generating 8x8 checkerboard pattern sample:\n";
        
        for (int y = 0; y < 8; ++y) {
            std::cout << "  ";
            for (int x = 0; x < 8; ++x) {
                float u = (x + 0.5f) / 8.0f;
                float v = (y + 0.5f) / 8.0f;
                
                float value = checkerboard.sampleFloat(Vec2(u, v));
                char symbol = (value > 0.5f) ? '█' : '░';
                std::cout << symbol << symbol;
            }
            std::cout << "\n";
        }
        
        std::cout << "\n";
    }
};

/**
 * @brief Main demo function
 */
int main() {
    PatternGeneratorDemo::runAllDemos();
    return 0;
}
