// src/core/sampler/random_sampler.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Random sampler implementation

#include "random_sampler.hpp"
#include "../math/vec2.hpp"
#include <chrono>

namespace photon {

// ExtendedRandomSampler implementation

ExtendedRandomSampler::ExtendedRandomSampler(uint32_t seed)
    : m_seed(seed), m_distribution(0.0f, 1.0f) {
    if (m_seed == 0) {
        // Use time-based seed
        m_seed = static_cast<uint32_t>(
            std::chrono::high_resolution_clock::now().time_since_epoch().count()
        );
    }
    initializeGenerator();
}

ExtendedRandomSampler::ExtendedRandomSampler(const ExtendedRandomSampler& other)
    : m_seed(other.m_seed), m_generator(other.m_generator), m_distribution(0.0f, 1.0f) {
}

ExtendedRandomSampler& ExtendedRandomSampler::operator=(const ExtendedRandomSampler& other) {
    if (this != &other) {
        m_seed = other.m_seed;
        m_generator = other.m_generator;
        m_distribution = std::uniform_real_distribution<float>(0.0f, 1.0f);
    }
    return *this;
}

float ExtendedRandomSampler::next1D() {
    return m_distribution(m_generator);
}

Vec2 ExtendedRandomSampler::next2D() {
    return Vec2(next1D(), next1D());
}

void ExtendedRandomSampler::setSeed(uint32_t seed) {
    m_seed = seed;
    if (m_seed == 0) {
        m_seed = static_cast<uint32_t>(
            std::chrono::high_resolution_clock::now().time_since_epoch().count()
        );
    }
    initializeGenerator();
}

void ExtendedRandomSampler::reset() {
    initializeGenerator();
}

void ExtendedRandomSampler::initializeGenerator() {
    m_generator.seed(m_seed);
}

// FastRandomSampler implementation

FastRandomSampler::FastRandomSampler(uint32_t seed) : m_originalSeed(seed) {
    if (m_originalSeed == 0) {
        // Use time-based seed
        m_originalSeed = static_cast<uint32_t>(
            std::chrono::high_resolution_clock::now().time_since_epoch().count()
        );
    }
    m_state = m_originalSeed;
}

float FastRandomSampler::get1D() {
    return uint32ToFloat(lcg());
}

Vec2 FastRandomSampler::next2D() {
    return Vec2(get1D(), get1D());
}

void FastRandomSampler::seed(uint32_t seed) {
    m_originalSeed = seed;
    if (m_originalSeed == 0) {
        m_originalSeed = static_cast<uint32_t>(
            std::chrono::high_resolution_clock::now().time_since_epoch().count()
        );
    }
    m_state = m_originalSeed;
}

std::unique_ptr<Sampler> FastRandomSampler::clone() const {
    auto cloned = std::make_unique<FastRandomSampler>(m_originalSeed);
    cloned->m_state = m_state;
    return cloned;
}

void FastRandomSampler::reset() {
    m_state = m_originalSeed;
}

uint32_t FastRandomSampler::lcg() const {
    // Linear congruential generator (LCG)
    // Using constants from Numerical Recipes
    m_state = m_state * 1664525u + 1013904223u;
    return m_state;
}

float FastRandomSampler::uint32ToFloat(uint32_t value) const {
    // Convert to [0,1) range
    return static_cast<float>(value) * (1.0f / 4294967296.0f);
}

} // namespace photon
