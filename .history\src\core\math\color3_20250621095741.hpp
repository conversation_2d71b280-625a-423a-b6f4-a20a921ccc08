// src/core/math/color3.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// 3D Color class for RGB color representation

#pragma once

#include <cmath>
#include <algorithm>
#include <iostream>

namespace photon {

/**
 * @brief 3D color class for RGB representation
 */
struct Color3 {
    union {
        struct { float r, g, b; };
        struct { float x, y, z; };  // Vec3 compatibility
    };
    
    /**
     * @brief Default constructor (black)
     */
    Color3() : r(0.0f), g(0.0f), b(0.0f) {}
    
    /**
     * @brief Constructor with components
     */
    Color3(float r, float g, float b) : r(r), g(g), b(b) {}
    
    /**
     * @brief Constructor with single value (grayscale)
     */
    explicit Color3(float value) : r(value), g(value), b(value) {}
    
    // Arithmetic operators
    Color3 operator+(const Color3& c) const { return Color3(r + c.r, g + c.g, b + c.b); }
    Color3 operator-(const Color3& c) const { return Color3(r - c.r, g - c.g, b - c.b); }
    Color3 operator*(float s) const { return Color3(r * s, g * s, b * s); }
    Color3 operator*(const Color3& c) const { return Color3(r * c.r, g * c.g, b * c.b); }
    Color3 operator/(float s) const { return Color3(r / s, g / s, b / s); }
    Color3 operator/(const Color3& c) const { return Color3(r / c.r, g / c.g, b / c.b); }
    
    // Assignment operators
    Color3& operator+=(const Color3& c) { r += c.r; g += c.g; b += c.b; return *this; }
    Color3& operator-=(const Color3& c) { r -= c.r; g -= c.g; b -= c.b; return *this; }
    Color3& operator*=(float s) { r *= s; g *= s; b *= s; return *this; }
    Color3& operator*=(const Color3& c) { r *= c.r; g *= c.g; b *= c.b; return *this; }
    Color3& operator/=(float s) { r /= s; g /= s; b /= s; return *this; }
    Color3& operator/=(const Color3& c) { r /= c.r; g /= c.g; b /= c.b; return *this; }
    
    // Unary operators
    Color3 operator-() const { return Color3(-r, -g, -b); }
    
    // Comparison operators
    bool operator==(const Color3& c) const { return r == c.r && g == c.g && b == c.b; }
    bool operator!=(const Color3& c) const { return !(*this == c); }
    
    // Array access
    float& operator[](int i) { return (&r)[i]; }
    const float& operator[](int i) const { return (&r)[i]; }

    // Vec3 compatibility accessors
    float& x() { return r; }
    const float& x() const { return r; }
    float& y() { return g; }
    const float& y() const { return g; }
    float& z() { return b; }
    const float& z() const { return b; }
    
    /**
     * @brief Calculate luminance (perceived brightness)
     */
    float luminance() const { 
        return 0.299f * r + 0.587f * g + 0.114f * b; 
    }
    
    /**
     * @brief Calculate average of RGB components
     */
    float average() const { 
        return (r + g + b) / 3.0f; 
    }
    
    /**
     * @brief Get maximum component
     */
    float maxComponent() const { 
        return std::max({r, g, b}); 
    }
    
    /**
     * @brief Get minimum component
     */
    float minComponent() const { 
        return std::min({r, g, b}); 
    }
    
    /**
     * @brief Check if color is black
     */
    bool isBlack() const {
        return r == 0.0f && g == 0.0f && b == 0.0f;
    }

    /**
     * @brief Check if color is zero (alias for isBlack for compatibility)
     */
    bool isZero() const {
        return isBlack();
    }
    
    /**
     * @brief Check if color has valid values (no NaN, no negative)
     */
    bool isValid() const {
        return std::isfinite(r) && std::isfinite(g) && std::isfinite(b) &&
               r >= 0.0f && g >= 0.0f && b >= 0.0f;
    }
    
    /**
     * @brief Clamp components to range [0, 1]
     */
    Color3 clamp() const {
        return Color3(
            std::clamp(r, 0.0f, 1.0f),
            std::clamp(g, 0.0f, 1.0f),
            std::clamp(b, 0.0f, 1.0f)
        );
    }
    
    /**
     * @brief Clamp components to range [min, max]
     */
    Color3 clamp(float minVal, float maxVal) const {
        return Color3(
            std::clamp(r, minVal, maxVal),
            std::clamp(g, minVal, maxVal),
            std::clamp(b, minVal, maxVal)
        );
    }
    
    /**
     * @brief Apply gamma correction
     */
    Color3 gamma(float gamma) const {
        return Color3(
            std::pow(r, gamma),
            std::pow(g, gamma),
            std::pow(b, gamma)
        );
    }
    
    /**
     * @brief Linear to sRGB conversion
     */
    Color3 toSRGB() const {
        auto linearToSRGB = [](float linear) {
            if (linear <= 0.0031308f) {
                return 12.92f * linear;
            } else {
                return 1.055f * std::pow(linear, 1.0f / 2.4f) - 0.055f;
            }
        };
        
        return Color3(
            linearToSRGB(r),
            linearToSRGB(g),
            linearToSRGB(b)
        );
    }
    
    /**
     * @brief sRGB to linear conversion
     */
    Color3 toLinear() const {
        auto sRGBToLinear = [](float srgb) {
            if (srgb <= 0.04045f) {
                return srgb / 12.92f;
            } else {
                return std::pow((srgb + 0.055f) / 1.055f, 2.4f);
            }
        };
        
        return Color3(
            sRGBToLinear(r),
            sRGBToLinear(g),
            sRGBToLinear(b)
        );
    }
    
    /**
     * @brief Linear interpolation
     */
    Color3 lerp(const Color3& c, float t) const {
        return *this * (1.0f - t) + c * t;
    }
    
    /**
     * @brief Calculate length (magnitude) of color vector
     */
    float length() const {
        return std::sqrt(r * r + g * g + b * b);
    }
    
    /**
     * @brief Calculate squared length
     */
    float lengthSquared() const {
        return r * r + g * g + b * b;
    }
    
    // Static utility functions
    
    /**
     * @brief Black color
     */
    static Color3 black() { return Color3(0.0f, 0.0f, 0.0f); }
    
    /**
     * @brief White color
     */
    static Color3 white() { return Color3(1.0f, 1.0f, 1.0f); }
    
    /**
     * @brief Red color
     */
    static Color3 red() { return Color3(1.0f, 0.0f, 0.0f); }
    
    /**
     * @brief Green color
     */
    static Color3 green() { return Color3(0.0f, 1.0f, 0.0f); }
    
    /**
     * @brief Blue color
     */
    static Color3 blue() { return Color3(0.0f, 0.0f, 1.0f); }
    
    /**
     * @brief Minimum of two colors (component-wise)
     */
    static Color3 min(const Color3& a, const Color3& b) {
        return Color3(std::min(a.r, b.r), std::min(a.g, b.g), std::min(a.b, b.b));
    }
    
    /**
     * @brief Maximum of two colors (component-wise)
     */
    static Color3 max(const Color3& a, const Color3& b) {
        return Color3(std::max(a.r, b.r), std::max(a.g, b.g), std::max(a.b, b.b));
    }
    
    /**
     * @brief Linear interpolation between two colors
     */
    static Color3 lerp(const Color3& a, const Color3& b, float t) {
        return a.lerp(b, t);
    }
};

// Global operators for scalar multiplication
inline Color3 operator*(float s, const Color3& c) { return c * s; }

// Stream operators for debugging
inline std::ostream& operator<<(std::ostream& os, const Color3& c) {
    return os << "Color3(" << c.r << ", " << c.g << ", " << c.b << ")";
}

inline std::istream& operator>>(std::istream& is, Color3& c) {
    return is >> c.r >> c.g >> c.b;
}

} // namespace photon
