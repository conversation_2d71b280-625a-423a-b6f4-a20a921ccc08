# scripts/test_and_deploy.py
#!/usr/bin/env python3
"""
PhotonRender Testing and Deployment Script
Handles automated testing, benchmarking, and packaging for distribution
"""

import os
import sys
import json
import time
import shutil
import subprocess
import argparse
from pathlib import Path
from datetime import datetime

class Colors:
    """Terminal colors for pretty output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(text):
    """Print formatted header"""
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{text.center(60)}{Colors.ENDC}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'='*60}{Colors.ENDC}\n")

def run_command(cmd, cwd=None):
    """Run command and return output"""
    print(f"{Colors.OKBLUE}Running: {' '.join(cmd)}{Colors.ENDC}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"{Colors.FAIL}Error: {result.stderr}{Colors.ENDC}")
        return None
    return result.stdout

class PhotonRenderTester:
    """Main testing and deployment class"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.build_dir = self.project_root / "build"
        self.test_results = {}
        self.benchmark_results = {}
        
    def run_unit_tests(self):
        """Run unit tests"""
        print_header("Running Unit Tests")
        
        test_executable = self.build_dir / "bin" / "photon_tests"
        if not test_executable.exists():
            print(f"{Colors.FAIL}Test executable not found!{Colors.ENDC}")
            return False
            
        # Run tests with detailed output
        result = run_command([str(test_executable), "--gtest_output=json:test_results.json"], 
                           cwd=self.build_dir)
        
        # Parse results
        if result:
            with open(self.build_dir / "test_results.json") as f:
                self.test_results = json.load(f)
            
            # Print summary
            total = self.test_results['tests']
            passed = total - self.test_results['failures'] - self.test_results['disabled']
            print(f"\n{Colors.OKGREEN}Passed: {passed}/{total}{Colors.ENDC}")
            
            if self.test_results['failures'] > 0:
                print(f"{Colors.FAIL}Failed: {self.test_results['failures']}{Colors.ENDC}")
                return False
                
        return True
        
    def run_integration_tests(self):
        """Run integration tests with test scenes"""
        print_header("Running Integration Tests")
        
        test_scenes = [
            ("Cornell Box", "cornell_box.json", 10, 64),
            ("Materials Test", "materials_test.json", 20, 128),
            ("Complex Scene", "sponza.json", 50, 256)
        ]
        
        renderer = self.build_dir / "bin" / "photon_render"
        
        for name, scene_file, spp, expected_time in test_scenes:
            print(f"\n{Colors.OKCYAN}Testing: {name}{Colors.ENDC}")
            scene_path = self.project_root / "tests" / "scenes" / scene_file
            
            if not scene_path.exists():
                print(f"{Colors.WARNING}Scene not found: {scene_file}{Colors.ENDC}")
                continue
                
            # Run render
            start_time = time.time()
            result = run_command([
                str(renderer),
                f"--scene={scene_path}",
                f"--spp={spp}",
                "--width=512",
                "--height=512",
                f"--output=test_{scene_file.replace('.json', '.png')}"
            ])
            
            render_time = time.time() - start_time
            
            if result:
                print(f"{Colors.OKGREEN}✓ Rendered in {render_time:.2f}s{Colors.ENDC}")
                
                # Check performance
                if render_time > expected_time:
                    print(f"{Colors.WARNING}⚠ Slower than expected ({expected_time}s){Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}✗ Render failed{Colors.ENDC}")
                
    def run_benchmarks(self):
        """Run performance benchmarks"""
        print_header("Running Benchmarks")
        
        benchmark_scenes = {
            "simple": {
                "scene": "cornell_box.json",
                "resolutions": [(512, 512), (1024, 1024), (2048, 2048)],
                "spp_values": [10, 50, 100, 500]
            },
            "complex": {
                "scene": "sponza.json", 
                "resolutions": [(1920, 1080), (3840, 2160)],
                "spp_values": [50, 100]
            }
        }
        
        renderer = self.build_dir / "bin" / "photon_benchmark"
        results = {}
        
        for category, config in benchmark_scenes.items():
            print(f"\n{Colors.OKCYAN}Benchmark Category: {category}{Colors.ENDC}")
            results[category] = []
            
            for width, height in config["resolutions"]:
                for spp in config["spp_values"]:
                    print(f"  Resolution: {width}x{height}, SPP: {spp}")
                    
                    # Run benchmark
                    output = run_command([
                        str(renderer),
                        f"--scene={config['scene']}",
                        f"--width={width}",
                        f"--height={height}",
                        f"--spp={spp}",
                        "--json-output"
                    ])
                    
                    if output:
                        try:
                            bench_result = json.loads(output)
                            results[category].append(bench_result)
                            
                            # Print summary
                            mrays = bench_result.get("mrays_per_sec", 0)
                            print(f"    {Colors.OKGREEN}→ {mrays:.2f} Mrays/s{Colors.ENDC}")
                        except:
                            print(f"    {Colors.FAIL}Failed to parse results{Colors.ENDC}")
                            
        self.benchmark_results = results
        self.save_benchmark_results()
        
    def save_benchmark_results(self):
        """Save benchmark results with timestamp"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"benchmark_results_{timestamp}.json"
        
        with open(self.project_root / "benchmarks" / filename, 'w') as f:
            json.dump({
                "timestamp": timestamp,
                "system_info": self.get_system_info(),
                "results": self.benchmark_results
            }, f, indent=2)
            
        print(f"\n{Colors.OKGREEN}Benchmark results saved to: {filename}{Colors.ENDC}")
        
    def get_system_info(self):
        """Get system information for benchmarks"""
        import platform
        
        try:
            import cpuinfo
            cpu = cpuinfo.get_cpu_info()['brand_raw']
        except:
            cpu = platform.processor()
            
        return {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "processor": cpu,
            "python_version": platform.python_version(),
            "cuda_available": self.check_cuda()
        }
        
    def check_cuda(self):
        """Check if CUDA is available"""
        try:
            output = run_command(["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"])
            return output.strip() if output else None
        except:
            return None
            
    def package_plugin(self):
        """Package SketchUp plugin for distribution"""
        print_header("Packaging Plugin")
        
        # Create distribution directory
        dist_dir = self.project_root / "dist"
        dist_dir.mkdir(exist_ok=True)
        
        # Version from CMakeLists.txt
        version = self.get_version()
        package_name = f"PhotonRender_v{version}"
        package_dir = dist_dir / package_name
        
        # Clean previous build
        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir()
        
        print(f"Creating package: {package_name}")
        
        # Copy Ruby files
        print("  Copying Ruby files...")
        shutil.copytree(
            self.project_root / "src" / "ruby",
            package_dir / "PhotonRender",
            ignore=shutil.ignore_patterns('*.pyc', '__pycache__', '.DS_Store')
        )
        
        # Copy native extension
        print("  Copying native extension...")
        extension_name = "photon_core"
        if sys.platform == "win32":
            ext = ".dll"
        elif sys.platform == "darwin":
            ext = ".bundle"
        else:
            ext = ".so"
            
        shutil.copy(
            self.build_dir / "lib" / f"{extension_name}{ext}",
            package_dir / "PhotonRender" / "photon_render"
        )
        
        # Copy dependencies
        print("  Copying dependencies...")
        deps_dir = package_dir / "PhotonRender" / "libs"
        deps_dir.mkdir(exist_ok=True)
        
        # List of required DLLs/libraries
        dependencies = [
            "embree4.dll" if sys.platform == "win32" else "libembree4.so",
            "tbb12.dll" if sys.platform == "win32" else "libtbb.so.12",
        ]
        
        for dep in dependencies:
            dep_path = self.find_dependency(dep)
            if dep_path:
                shutil.copy(dep_path, deps_dir)
            else:
                print(f"{Colors.WARNING}    Warning: {dep} not found{Colors.ENDC}")
                
        # Create RBZ (Ruby Zip) file
        print("  Creating RBZ archive...")
        rbz_file = dist_dir / f"{package_name}.rbz"
        shutil.make_archive(
            str(rbz_file.with_suffix("")),
            'zip',
            package_dir.parent,
            package_dir.name
        )
        rbz_file.with_suffix(".zip").rename(rbz_file)
        
        print(f"\n{Colors.OKGREEN}✓ Plugin packaged: {rbz_file}{Colors.ENDC}")
        
        # Create installer script
        self.create_installer(package_dir, version)
        
    def find_dependency(self, name):
        """Find dependency file"""
        search_paths = [
            self.build_dir / "bin",
            self.build_dir / "lib", 
            Path("/usr/local/lib"),
            Path("C:/Program Files/Intel/Embree4/bin") if sys.platform == "win32" else None
        ]
        
        for path in search_paths:
            if path and path.exists():
                dep_path = path / name
                if dep_path.exists():
                    return dep_path
                    
        return None
        
    def create_installer(self, package_dir, version):
        """Create installer script"""
        installer_content = f'''#!/usr/bin/env ruby
# PhotonRender {version} Installer

require 'fileutils'
require 'rbconfig'

puts "PhotonRender {version} Installer"
puts "=" * 40

# Detect SketchUp installation
sketchup_plugins = nil

if RbConfig::CONFIG['host_os'] =~ /mswin|mingw|cygwin/
  # Windows
  paths = [
    ENV['APPDATA'] + '/SketchUp/SketchUp 2024/SketchUp/Plugins',
    ENV['APPDATA'] + '/SketchUp/SketchUp 2023/SketchUp/Plugins',
    ENV['APPDATA'] + '/SketchUp/SketchUp 2022/SketchUp/Plugins'
  ]
elsif RbConfig::CONFIG['host_os'] =~ /darwin/
  # macOS
  paths = [
    '~/Library/Application Support/SketchUp 2024/SketchUp/Plugins',
    '~/Library/Application Support/SketchUp 2023/SketchUp/Plugins'
  ]
else
  # Linux (unsupported but included)
  paths = []
end

# Find existing SketchUp installation
paths.each do |path|
  expanded = File.expand_path(path)
  if File.directory?(expanded)
    sketchup_plugins = expanded
    break
  end
end

unless sketchup_plugins
  puts "SketchUp installation not found!"
  puts "Please enter the path to your SketchUp Plugins folder:"
  sketchup_plugins = gets.chomp
end

puts "Installing to: #{{sketchup_plugins}}"

# Copy files
source = File.dirname(__FILE__) + '/PhotonRender'
dest = sketchup_plugins + '/PhotonRender'

if File.directory?(dest)
  puts "Removing previous installation..."
  FileUtils.rm_rf(dest)
end

puts "Copying files..."
FileUtils.cp_r(source, dest)

puts ""
puts "Installation complete!"
puts "Please restart SketchUp to use PhotonRender."
'''
        
        installer_path = package_dir.parent / f"install_{package_dir.name}.rb"
        with open(installer_path, 'w') as f:
            f.write(installer_content)
            
        # Make executable on Unix
        if sys.platform != "win32":
            os.chmod(installer_path, 0o755)
            
    def get_version(self):
        """Extract version from CMakeLists.txt"""
        cmake_file = self.project_root / "CMakeLists.txt"
        with open(cmake_file) as f:
            for line in f:
                if "project(PhotonRender VERSION" in line:
                    return line.split("VERSION")[1].split(")")[0].strip()
        return "0.0.0"
        
    def generate_documentation(self):
        """Generate documentation"""
        print_header("Generating Documentation")
        
        # Run Doxygen for C++ docs
        if shutil.which("doxygen"):
            print("Generating C++ API documentation...")
            run_command(["doxygen", "Doxyfile"], cwd=self.project_root)
            print(f"{Colors.OKGREEN}✓ API docs generated{Colors.ENDC}")
        else:
            print(f"{Colors.WARNING}Doxygen not found{Colors.ENDC}")
            
        # Generate user manual
        self.generate_user_manual()
        
    def generate_user_manual(self):
        """Generate user manual markdown"""
        manual = f"""# PhotonRender User Manual

## Installation

1. Download PhotonRender_{self.get_version()}.rbz
2. In SketchUp: Window > Extension Manager > Install Extension
3. Select the .rbz file and click Install
4. Restart SketchUp

## Quick Start

1. Open your SketchUp model
2. Click the PhotonRender toolbar icon
3. Adjust settings in the dialog
4. Click "Render" to start

## Render Settings

### Quality
- **Samples Per Pixel**: Higher values = less noise (default: 100)
- **Max Bounces**: Light bounce limit (default: 8)
- **Enable Denoising**: AI-powered noise reduction

### Performance  
- **Use GPU**: Enable GPU acceleration if available
- **Tile Size**: Render tile dimensions (default: 64)
- **Threads**: CPU thread count (0 = auto)

## Keyboard Shortcuts

- `Ctrl+Shift+R`: Start render
- `Esc`: Stop render
- `Ctrl+S`: Save render

## Troubleshooting

See [troubleshooting guide](docs/troubleshooting.md)
"""
        
        with open(self.project_root / "docs" / "user_manual.md", 'w') as f:
            f.write(manual)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="PhotonRender Test & Deploy")
    parser.add_argument("--skip-tests", action="store_true", help="Skip tests")
    parser.add_argument("--benchmarks", action="store_true", help="Run benchmarks")
    parser.add_argument("--package", action="store_true", help="Create plugin package")
    parser.add_argument("--docs", action="store_true", help="Generate documentation")
    parser.add_argument("--all", action="store_true", help="Run everything")
    
    args = parser.parse_args()
    
    # Get project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    tester = PhotonRenderTester(project_root)
    
    # Run requested operations
    if args.all or (not args.skip_tests and not args.package and not args.docs):
        if not tester.run_unit_tests():
            sys.exit(1)
        tester.run_integration_tests()
        
    if args.all or args.benchmarks:
        tester.run_benchmarks()
        
    if args.all or args.package:
        tester.package_plugin()
        
    if args.all or args.docs:
        tester.generate_documentation()
        
    print(f"\n{Colors.OKGREEN}{Colors.BOLD}All operations completed successfully!{Colors.ENDC}")

if __name__ == "__main__":
    main()