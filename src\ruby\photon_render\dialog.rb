# src/ruby/photon_render/dialog.rb
# PhotonRender - SketchUp Dialog System
# Sistema dialog per impostazioni e progress PhotonRender

module PhotonRender
  module Dialog
    
    # Show render settings dialog
    def self.show_render_settings(&callback)
      puts "Showing render settings dialog..."
      
      # Create dialog HTML
      html_content = create_render_settings_html
      
      # Create web dialog
      dialog = UI::WebDialog.new("PhotonRender Settings", true, "photon_render_settings", 600, 500, 100, 100, true)
      dialog.set_html(html_content)
      
      # Handle dialog callbacks
      setup_render_settings_callbacks(dialog, callback)
      
      dialog.show
      @render_settings_dialog = dialog
    end
    
    # Show render progress dialog
    def self.show_render_progress
      puts "Showing render progress dialog..."
      
      html_content = create_render_progress_html
      
      dialog = UI::WebDialog.new("PhotonRender Progress", false, "photon_render_progress", 400, 300, 200, 200, false)
      dialog.set_html(html_content)
      
      setup_render_progress_callbacks(dialog)
      
      dialog.show
      @render_progress_dialog = dialog
    end
    
    # Hide render progress dialog
    def self.hide_render_progress
      if @render_progress_dialog
        @render_progress_dialog.close
        @render_progress_dialog = nil
      end
    end
    
    # Update render progress
    def self.update_progress(progress, stats = {})
      if @render_progress_dialog
        # Send progress update to dialog
        js_code = "updateProgress(#{progress}, #{stats.to_json});"
        @render_progress_dialog.execute_script(js_code)
      end
    end
    
    # Show material editor dialog
    def self.show_material_editor
      puts "Showing material editor dialog..."
      
      html_content = create_material_editor_html
      
      dialog = UI::WebDialog.new("PhotonRender Material Editor", true, "photon_render_materials", 800, 600, 50, 50, true)
      dialog.set_html(html_content)
      
      setup_material_editor_callbacks(dialog)
      
      dialog.show
      @material_editor_dialog = dialog
    end
    
    # Show render queue dialog
    def self.show_render_queue
      puts "Showing render queue dialog..."
      UI.messagebox("Render queue not yet implemented")
    end
    
    # Show light setup dialog
    def self.show_light_setup
      puts "Showing light setup dialog..."
      UI.messagebox("Light setup not yet implemented")
    end
    
    # Show environment settings dialog
    def self.show_environment_settings
      puts "Showing environment settings dialog..."
      UI.messagebox("Environment settings not yet implemented")
    end
    
    # Show preferences dialog
    def self.show_preferences
      puts "Showing preferences dialog..."
      UI.messagebox("Preferences not yet implemented")
    end
    
    private
    
    # Create render settings HTML
    def self.create_render_settings_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Settings</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .section { margin-bottom: 20px; }
            .section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
            .checkbox-group { display: flex; align-items: center; }
            .checkbox-group input { width: auto; margin-right: 10px; }
            .buttons { text-align: right; margin-top: 20px; }
            button { padding: 10px 20px; margin-left: 10px; border: none; border-radius: 4px; cursor: pointer; }
            .btn-primary { background: #4CAF50; color: white; }
            .btn-secondary { background: #6c757d; color: white; }
            .btn-primary:hover { background: #45a049; }
            .btn-secondary:hover { background: #5a6268; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Settings</h2>
            
            <div class="section">
              <h3>Image Settings</h3>
              <div class="form-group">
                <label for="width">Width:</label>
                <input type="number" id="width" value="1920" min="64" max="8192">
              </div>
              <div class="form-group">
                <label for="height">Height:</label>
                <input type="number" id="height" value="1080" min="64" max="8192">
              </div>
              <div class="form-group">
                <label for="samples">Samples per Pixel:</label>
                <input type="number" id="samples" value="100" min="1" max="10000">
              </div>
            </div>
            
            <div class="section">
              <h3>Rendering Settings</h3>
              <div class="form-group">
                <label for="bounces">Max Bounces:</label>
                <input type="number" id="bounces" value="8" min="1" max="50">
              </div>
              <div class="form-group">
                <label for="integrator">Integrator:</label>
                <select id="integrator">
                  <option value="path_tracing">Path Tracing</option>
                  <option value="direct_lighting">Direct Lighting</option>
                  <option value="ambient_occlusion">Ambient Occlusion</option>
                </select>
              </div>
              <div class="form-group checkbox-group">
                <input type="checkbox" id="use_gpu" checked>
                <label for="use_gpu">Use GPU Acceleration</label>
              </div>
              <div class="form-group checkbox-group">
                <input type="checkbox" id="enable_denoising" checked>
                <label for="enable_denoising">Enable AI Denoising</label>
              </div>
            </div>
            
            <div class="section">
              <h3>Performance Settings</h3>
              <div class="form-group">
                <label for="tile_size">Tile Size:</label>
                <select id="tile_size">
                  <option value="32">32x32</option>
                  <option value="64" selected>64x64</option>
                  <option value="128">128x128</option>
                  <option value="256">256x256</option>
                </select>
              </div>
              <div class="form-group">
                <label for="threads">CPU Threads:</label>
                <input type="number" id="threads" value="8" min="1" max="64">
              </div>
            </div>
            
            <div class="buttons">
              <button type="button" class="btn-secondary" onclick="cancelSettings()">Cancel</button>
              <button type="button" class="btn-primary" onclick="applySettings()">Start Render</button>
            </div>
          </div>
          
          <script>
            function applySettings() {
              const settings = {
                width: parseInt(document.getElementById('width').value),
                height: parseInt(document.getElementById('height').value),
                samples_per_pixel: parseInt(document.getElementById('samples').value),
                max_bounces: parseInt(document.getElementById('bounces').value),
                integrator: document.getElementById('integrator').value,
                use_gpu: document.getElementById('use_gpu').checked,
                enable_denoising: document.getElementById('enable_denoising').checked,
                tile_size: parseInt(document.getElementById('tile_size').value),
                threads: parseInt(document.getElementById('threads').value)
              };
              
              window.location = 'skp:apply_settings@' + JSON.stringify(settings);
            }
            
            function cancelSettings() {
              window.location = 'skp:cancel_settings@';
            }
          </script>
        </body>
        </html>
      HTML
    end
    
    # Create render progress HTML
    def self.create_render_progress_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Progress</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .progress-bar { width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
            .progress-fill { height: 100%; background: #4CAF50; width: 0%; transition: width 0.3s ease; }
            .stats { margin-top: 15px; font-size: 14px; }
            .stat-item { margin: 5px 0; }
            .buttons { text-align: center; margin-top: 20px; }
            button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; background: #f44336; color: white; }
            button:hover { background: #da190b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h3>Rendering in Progress...</h3>
            
            <div class="progress-bar">
              <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div id="progressText">0%</div>
            
            <div class="stats">
              <div class="stat-item">Time Elapsed: <span id="timeElapsed">00:00</span></div>
              <div class="stat-item">Time Remaining: <span id="timeRemaining">--:--</span></div>
              <div class="stat-item">Samples: <span id="samples">0</span></div>
              <div class="stat-item">Rays/sec: <span id="raysPerSec">0</span></div>
            </div>
            
            <div class="buttons">
              <button onclick="stopRender()">Stop Render</button>
            </div>
          </div>
          
          <script>
            let startTime = Date.now();
            
            function updateProgress(progress, stats) {
              const progressPercent = Math.round(progress * 100);
              document.getElementById('progressFill').style.width = progressPercent + '%';
              document.getElementById('progressText').textContent = progressPercent + '%';
              
              // Update time
              const elapsed = Math.floor((Date.now() - startTime) / 1000);
              const minutes = Math.floor(elapsed / 60);
              const seconds = elapsed % 60;
              document.getElementById('timeElapsed').textContent = 
                String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
              
              // Update stats if provided
              if (stats.samples) {
                document.getElementById('samples').textContent = stats.samples;
              }
              if (stats.rays_per_sec) {
                document.getElementById('raysPerSec').textContent = Math.round(stats.rays_per_sec).toLocaleString();
              }
              
              // Estimate remaining time
              if (progress > 0.01) {
                const totalTime = elapsed / progress;
                const remaining = Math.floor(totalTime - elapsed);
                const remMinutes = Math.floor(remaining / 60);
                const remSeconds = remaining % 60;
                document.getElementById('timeRemaining').textContent = 
                  String(remMinutes).padStart(2, '0') + ':' + String(remSeconds).padStart(2, '0');
              }
            }
            
            function stopRender() {
              window.location = 'skp:stop_render@';
            }
          </script>
        </body>
        </html>
      HTML
    end
    
    # Create material editor HTML (simplified)
    def self.create_material_editor_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Material Editor</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            h2 { color: #333; margin-top: 0; }
            .placeholder { text-align: center; padding: 50px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Material Editor</h2>
            <div class="placeholder">
              <h3>Material Editor</h3>
              <p>Advanced material editing interface will be implemented here.</p>
              <p>Features will include:</p>
              <ul style="text-align: left; display: inline-block;">
                <li>Disney PBR material editing</li>
                <li>Real-time material preview</li>
                <li>Texture mapping controls</li>
                <li>Material library management</li>
              </ul>
            </div>
          </div>
        </body>
        </html>
      HTML
    end
    
    # Setup render settings dialog callbacks
    def self.setup_render_settings_callbacks(dialog, callback)
      dialog.add_action_callback("apply_settings") do |web_dialog, action_name|
        begin
          settings_json = action_name.split('@', 2)[1]
          settings = JSON.parse(settings_json)
          
          puts "Render settings applied: #{settings}"
          
          dialog.close
          callback.call(settings) if callback
        rescue => e
          puts "Error applying settings: #{e.message}"
        end
      end
      
      dialog.add_action_callback("cancel_settings") do |web_dialog, action_name|
        puts "Render settings cancelled"
        dialog.close
        callback.call(nil) if callback
      end
    end
    
    # Setup render progress dialog callbacks
    def self.setup_render_progress_callbacks(dialog)
      dialog.add_action_callback("stop_render") do |web_dialog, action_name|
        puts "Stop render requested from dialog"
        PhotonRender.render_manager.stop_render
        dialog.close
      end
    end
    
    # Setup material editor dialog callbacks
    def self.setup_material_editor_callbacks(dialog)
      # TODO: Implement material editor callbacks
    end
    
  end
end
