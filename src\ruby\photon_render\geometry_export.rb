# src/ruby/photon_render/geometry_export.rb
# PhotonRender - Geometry Export System
# Sistema per convertire geometrie SketchUp in formato PhotonRender

module PhotonRender
  module GeometryExport
    
    # Export complete scene geometry from SketchUp model
    def self.export_scene_geometry(model)
      puts "Exporting scene geometry from SketchUp model..."
      
      scene_data = {
        meshes: [],
        materials: {},
        lights: [],
        camera: nil,
        environment: {},
        stats: {
          total_faces: 0,
          total_vertices: 0,
          total_triangles: 0,
          materials_count: 0,
          lights_count: 0
        }
      }
      
      # Export camera
      scene_data[:camera] = export_camera(model.active_view)
      
      # Export materials first
      scene_data[:materials] = export_materials(model.materials)
      scene_data[:stats][:materials_count] = scene_data[:materials].size
      
      # Export geometry
      transformation_stack = [Geom::Transformation.new]
      export_entities(model.entities, scene_data, transformation_stack)
      
      # Export lights
      scene_data[:lights] = export_lights(model)
      scene_data[:stats][:lights_count] = scene_data[:lights].size
      
      # Export environment
      scene_data[:environment] = export_environment(model)
      
      puts "Export completed: #{scene_data[:stats][:total_triangles]} triangles, #{scene_data[:stats][:materials_count]} materials"
      scene_data
    end
    
    private
    
    # Export camera data
    def self.export_camera(view)
      camera = view.camera
      
      # Get camera parameters
      eye = camera.eye
      target = camera.target
      up = camera.up
      fov = camera.fov
      aspect = view.vpwidth.to_f / view.vpheight.to_f
      
      camera_data = {
        type: "perspective",
        position: [eye.x, eye.y, eye.z],
        target: [target.x, target.y, target.z],
        up: [up.x, up.y, up.z],
        fov: Math.degrees_to_radians(fov),
        aspect: aspect,
        near: 0.1,
        far: 10000.0
      }
      
      puts "Camera exported: position=#{camera_data[:position]}, fov=#{fov}°"
      camera_data
    end
    
    # Export all materials
    def self.export_materials(materials)
      material_map = {}
      
      # Default material
      material_map["default"] = {
        name: "default",
        type: "diffuse",
        color: [0.8, 0.8, 0.8],
        roughness: 0.8,
        metallic: 0.0,
        emission: [0.0, 0.0, 0.0],
        texture: nil
      }
      
      materials.each do |material|
        next unless material
        
        # Get material color
        color = material.color
        rgb = [color.red / 255.0, color.green / 255.0, color.blue / 255.0]
        
        # Determine material type based on properties
        material_type = "diffuse"
        metallic = 0.0
        roughness = 0.8
        
        # Check for texture
        texture_path = nil
        if material.texture
          texture_path = material.texture.filename
        end
        
        material_data = {
          name: material.name,
          type: material_type,
          color: rgb,
          roughness: roughness,
          metallic: metallic,
          emission: [0.0, 0.0, 0.0],
          texture: texture_path
        }
        
        material_map[material.name] = material_data
        puts "Material exported: #{material.name} (#{material_type})"
      end
      
      material_map
    end
    
    # Export entities recursively
    def self.export_entities(entities, scene_data, transform_stack)
      entities.each do |entity|
        case entity
        when Sketchup::Group
          # Push group transformation
          transform_stack.push(transform_stack.last * entity.transformation)
          export_entities(entity.entities, scene_data, transform_stack)
          transform_stack.pop
          
        when Sketchup::ComponentInstance
          # Push component transformation
          transform_stack.push(transform_stack.last * entity.transformation)
          export_entities(entity.definition.entities, scene_data, transform_stack)
          transform_stack.pop
          
        when Sketchup::Face
          # Export face as triangulated mesh
          export_face(entity, scene_data, transform_stack.last)
          scene_data[:stats][:total_faces] += 1
        end
      end
    end
    
    # Export single face as triangulated mesh
    def self.export_face(face, scene_data, transform)
      # Get triangulated mesh with normals and UVs
      mesh = face.mesh(7) # 7 = points + normals + UVs
      
      return if mesh.count_points == 0
      
      # Extract vertices, normals, and UVs
      vertices = []
      normals = []
      uvs = []
      
      (1..mesh.count_points).each do |i|
        # Transform vertex position
        point = mesh.point_at(i)
        transformed_point = transform * point
        vertices << [transformed_point.x, transformed_point.y, transformed_point.z]
        
        # Transform normal
        normal = mesh.normal_at(i)
        transformed_normal = transform.rotation * normal
        transformed_normal.normalize!
        normals << [transformed_normal.x, transformed_normal.y, transformed_normal.z]
        
        # UV coordinates
        if mesh.uv_at(i, true)
          uv = mesh.uv_at(i, true)
          uvs << [uv.x, 1.0 - uv.y] # Flip V coordinate
        else
          uvs << [0.0, 0.0]
        end
      end
      
      # Extract triangle indices
      triangles = []
      (1..mesh.count_polygons).each do |i|
        polygon = mesh.polygon_at(i)
        if polygon.size == 3
          # Already a triangle
          triangles << [polygon[0] - 1, polygon[1] - 1, polygon[2] - 1] # Convert to 0-based
        elsif polygon.size == 4
          # Quad - split into two triangles
          triangles << [polygon[0] - 1, polygon[1] - 1, polygon[2] - 1]
          triangles << [polygon[0] - 1, polygon[2] - 1, polygon[3] - 1]
        end
      end
      
      # Get material name
      material_name = face.material ? face.material.name : "default"
      
      # Create mesh data
      mesh_data = {
        name: "face_#{scene_data[:meshes].size}",
        vertices: vertices,
        normals: normals,
        uvs: uvs,
        triangles: triangles,
        material: material_name,
        smooth_normals: true
      }
      
      scene_data[:meshes] << mesh_data
      scene_data[:stats][:total_vertices] += vertices.size
      scene_data[:stats][:total_triangles] += triangles.size
    end
    
    # Export lights (simplified)
    def self.export_lights(model)
      lights = []
      
      # Add default sun light
      shadow_info = model.shadow_info
      if shadow_info["DisplayShadows"]
        sun_direction = shadow_info["SunDirection"]
        
        sun_light = {
          type: "directional",
          direction: [sun_direction.x, sun_direction.y, sun_direction.z],
          color: [1.0, 0.95, 0.8],
          intensity: 3.0
        }
        
        lights << sun_light
        puts "Sun light exported"
      end
      
      # Add default environment light
      env_light = {
        type: "environment",
        color: [0.5, 0.7, 1.0],
        intensity: 1.0
      }
      
      lights << env_light
      puts "Environment light exported"
      
      lights
    end
    
    # Export environment settings
    def self.export_environment(model)
      rendering_options = model.rendering_options
      
      environment = {
        background_color: [0.5, 0.7, 1.0],
        use_sky: true,
        ground_plane: false
      }
      
      # Get background color if set
      if rendering_options["BackgroundColor"]
        bg_color = rendering_options["BackgroundColor"]
        environment[:background_color] = [
          bg_color.red / 255.0,
          bg_color.green / 255.0,
          bg_color.blue / 255.0
        ]
      end
      
      puts "Environment exported"
      environment
    end
    
    # Utility: Convert degrees to radians
    def self.degrees_to_radians(degrees)
      degrees * Math::PI / 180.0
    end
    
  end
end
