# src/ruby/photon_render/toolbar.rb
# PhotonRender - SketchUp Toolbar Integration
# Sistema toolbar per PhotonRender in SketchUp

module PhotonRender
  module Toolbar
    
    # Create PhotonRender toolbar in SketchUp
    def self.create
      puts "Creating PhotonRender toolbar..."
      
      # Create toolbar
      @toolbar = UI::Toolbar.new("PhotonRender")
      
      # Add toolbar buttons
      add_render_buttons
      add_settings_buttons
      add_tools_buttons
      
      # Show toolbar
      @toolbar.show
      
      puts "PhotonRender toolbar created successfully"
    end
    
    # Hide toolbar
    def self.hide
      @toolbar.hide if @toolbar
    end
    
    # Show toolbar
    def self.show
      @toolbar.show if @toolbar
    end
    
    # Remove toolbar
    def self.remove
      @toolbar = nil
    end
    
    # Check if toolbar is visible
    def self.visible?
      @toolbar && @toolbar.visible?
    end
    
    private
    
    # Add rendering buttons
    def self.add_render_buttons
      # Start Render button
      start_render_cmd = UI::Command.new("Start Render") do
        start_render_action
      end
      start_render_cmd.menu_text = "Start Render"
      start_render_cmd.tooltip = "Start PhotonRender rendering"
      start_render_cmd.status_bar_text = "Start rendering the current scene"
      start_render_cmd.small_icon = get_icon_path("start_render_16.png")
      start_render_cmd.large_icon = get_icon_path("start_render_24.png")
      @toolbar.add_item(start_render_cmd)
      
      # Quick Render button
      quick_render_cmd = UI::Command.new("Quick Render") do
        quick_render_action
      end
      quick_render_cmd.menu_text = "Quick Render"
      quick_render_cmd.tooltip = "Quick render with default settings"
      quick_render_cmd.status_bar_text = "Start quick render with default settings"
      quick_render_cmd.small_icon = get_icon_path("quick_render_16.png")
      quick_render_cmd.large_icon = get_icon_path("quick_render_24.png")
      @toolbar.add_item(quick_render_cmd)
      
      # Stop Render button
      stop_render_cmd = UI::Command.new("Stop Render") do
        stop_render_action
      end
      stop_render_cmd.menu_text = "Stop Render"
      stop_render_cmd.tooltip = "Stop current render"
      stop_render_cmd.status_bar_text = "Stop the current rendering process"
      stop_render_cmd.small_icon = get_icon_path("stop_render_16.png")
      stop_render_cmd.large_icon = get_icon_path("stop_render_24.png")
      @toolbar.add_item(stop_render_cmd)
      
      # Add separator
      @toolbar.add_separator
    end
    
    # Add settings buttons
    def self.add_settings_buttons
      # Render Settings button
      settings_cmd = UI::Command.new("Render Settings") do
        show_render_settings_action
      end
      settings_cmd.menu_text = "Render Settings"
      settings_cmd.tooltip = "Open render settings dialog"
      settings_cmd.status_bar_text = "Configure rendering settings"
      settings_cmd.small_icon = get_icon_path("settings_16.png")
      settings_cmd.large_icon = get_icon_path("settings_24.png")
      @toolbar.add_item(settings_cmd)
      
      # Material Editor button
      materials_cmd = UI::Command.new("Material Editor") do
        show_material_editor_action
      end
      materials_cmd.menu_text = "Material Editor"
      materials_cmd.tooltip = "Open material editor"
      materials_cmd.status_bar_text = "Edit materials and textures"
      materials_cmd.small_icon = get_icon_path("materials_16.png")
      materials_cmd.large_icon = get_icon_path("materials_24.png")
      @toolbar.add_item(materials_cmd)
      
      # Add separator
      @toolbar.add_separator
    end
    
    # Add tools buttons
    def self.add_tools_buttons
      # Viewport Preview button
      preview_cmd = UI::Command.new("Viewport Preview") do
        toggle_viewport_preview_action
      end
      preview_cmd.menu_text = "Viewport Preview"
      preview_cmd.tooltip = "Toggle viewport preview"
      preview_cmd.status_bar_text = "Toggle real-time viewport preview"
      preview_cmd.small_icon = get_icon_path("preview_16.png")
      preview_cmd.large_icon = get_icon_path("preview_24.png")
      @toolbar.add_item(preview_cmd)
      
      # Export Scene button
      export_cmd = UI::Command.new("Export Scene") do
        export_scene_action
      end
      export_cmd.menu_text = "Export Scene"
      export_cmd.tooltip = "Export scene to file"
      export_cmd.status_bar_text = "Export current scene to PhotonRender format"
      export_cmd.small_icon = get_icon_path("export_16.png")
      export_cmd.large_icon = get_icon_path("export_24.png")
      @toolbar.add_item(export_cmd)
      
      # Help button
      help_cmd = UI::Command.new("Help") do
        show_help_action
      end
      help_cmd.menu_text = "Help"
      help_cmd.tooltip = "PhotonRender help"
      help_cmd.status_bar_text = "Open PhotonRender help documentation"
      help_cmd.small_icon = get_icon_path("help_16.png")
      help_cmd.large_icon = get_icon_path("help_24.png")
      @toolbar.add_item(help_cmd)
    end
    
    # Toolbar action implementations
    
    def self.start_render_action
      puts "Toolbar: Start Render"
      
      if PhotonRender.render_manager.is_rendering
        UI.messagebox("Render already in progress!")
        return
      end
      
      # Show render settings dialog
      Dialog.show_render_settings do |settings|
        if settings
          PhotonRender.render_manager.start_render(settings)
        end
      end
    end
    
    def self.quick_render_action
      puts "Toolbar: Quick Render"
      
      if PhotonRender.render_manager.is_rendering
        UI.messagebox("Render already in progress!")
        return
      end
      
      # Start render with default settings
      PhotonRender.render_manager.start_render
    end
    
    def self.stop_render_action
      puts "Toolbar: Stop Render"
      
      if PhotonRender.render_manager.is_rendering
        PhotonRender.render_manager.stop_render
      else
        UI.messagebox("No render in progress")
      end
    end
    
    def self.show_render_settings_action
      puts "Toolbar: Render Settings"
      Dialog.show_render_settings
    end
    
    def self.show_material_editor_action
      puts "Toolbar: Material Editor"
      Dialog.show_material_editor
    end
    
    def self.toggle_viewport_preview_action
      puts "Toolbar: Toggle Viewport Preview"
      ViewportTool.toggle_preview
    end
    
    def self.export_scene_action
      puts "Toolbar: Export Scene"
      Menu.send(:export_scene_command)
    end
    
    def self.show_help_action
      puts "Toolbar: Help"
      Menu.send(:show_help_documentation)
    end
    
    # Get icon path (with fallback to default icons)
    def self.get_icon_path(icon_name)
      icon_path = File.join(PhotonRender::PLUGIN_PATH, "icons", icon_name)
      
      # Check if custom icon exists
      if File.exist?(icon_path)
        return icon_path
      end
      
      # Fallback to default SketchUp icons or create simple colored rectangles
      case icon_name
      when /start_render/
        return create_default_icon("start", "#4CAF50") # Green
      when /quick_render/
        return create_default_icon("quick", "#FF9800") # Orange
      when /stop_render/
        return create_default_icon("stop", "#F44336") # Red
      when /settings/
        return create_default_icon("set", "#2196F3") # Blue
      when /materials/
        return create_default_icon("mat", "#9C27B0") # Purple
      when /preview/
        return create_default_icon("prev", "#00BCD4") # Cyan
      when /export/
        return create_default_icon("exp", "#795548") # Brown
      when /help/
        return create_default_icon("help", "#607D8B") # Blue Grey
      else
        return create_default_icon("?", "#9E9E9E") # Grey
      end
    end
    
    # Create simple default icon (placeholder)
    def self.create_default_icon(text, color)
      # For now, return nil to use SketchUp default icons
      # In a real implementation, you would create simple bitmap icons
      nil
    end
    
    # Update toolbar state based on render status
    def self.update_state
      return unless @toolbar
      
      # Update button states based on current render status
      is_rendering = PhotonRender.render_manager.is_rendering rescue false
      
      # Enable/disable buttons based on state
      # Note: SketchUp doesn't provide direct button state control
      # This would be implemented with custom button management
    end
    
  end
end
