// src/test_hdri_environment.cpp
// PhotonRender - Test for HDRI Environment Lighting implementation
// Phase 3.2.2 Task 1 validation

#include "core/light/hdri_environment_light.hpp"
#include "core/texture/hdr_texture.hpp"
#include "core/scene/intersection.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/math/vec3.hpp"
#include "core/math/color3.hpp"

#include <iostream>
#include <vector>
#include <chrono>
#include <memory>

using namespace photon;

/**
 * @brief HDRI Environment Lighting Test Suite
 */
class HDRIEnvironmentTest {
public:
    /**
     * @brief Run complete test suite
     */
    bool runTests() {
        std::cout << "\n🌟 HDRI Environment Lighting Test Suite" << std::endl;
        std::cout << "Phase 3.2.2 Task 1 Validation" << std::endl;
        std::cout << "=======================================" << std::endl;
        
        bool allPassed = true;
        
        // Test 1: HDR Texture Creation
        allPassed &= testHDRTextureCreation();
        
        // Test 2: Environment Light Basic Functionality
        allPassed &= testEnvironmentLightBasic();
        
        // Test 3: Importance Sampling
        allPassed &= testImportanceSampling();
        
        // Test 4: Procedural Sky
        allPassed &= testProceduralSky();
        
        // Test 5: Performance Benchmark
        allPassed &= testPerformance();
        
        // Final report
        printFinalReport(allPassed);
        
        return allPassed;
    }

private:
    int m_totalTests = 0;
    int m_passedTests = 0;
    
    void recordTest(const std::string& testName, bool passed) {
        m_totalTests++;
        if (passed) {
            m_passedTests++;
            std::cout << "✅ " << testName << " - PASSED" << std::endl;
        } else {
            std::cout << "❌ " << testName << " - FAILED" << std::endl;
        }
    }
    
    /**
     * @brief Test HDR texture creation and sampling
     */
    bool testHDRTextureCreation() {
        std::cout << "\n📊 HDR Texture Creation Tests" << std::endl;
        std::cout << "-----------------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Test solid color texture
            auto solidTexture = HDRTextureFactory::createSolid(Color3(1.0f, 0.5f, 0.2f));
            bool solidTest = solidTexture && solidTexture->isLoaded();
            recordTest("HDR Solid Texture Creation", solidTest);
            allPassed &= solidTest;
            
            if (solidTest) {
                Color3 sample = solidTexture->sample(Vec2(0.5f, 0.5f));
                bool colorTest = (std::abs(sample.r - 1.0f) < 0.01f && 
                                 std::abs(sample.g - 0.5f) < 0.01f && 
                                 std::abs(sample.b - 0.2f) < 0.01f);
                recordTest("HDR Texture Sampling", colorTest);
                allPassed &= colorTest;
            }
            
            // Test gradient texture
            auto gradientTexture = HDRTextureFactory::createGradient(
                Color3(1.0f, 1.0f, 1.0f), Color3(0.0f, 0.0f, 0.0f));
            bool gradientTest = gradientTexture && gradientTexture->isLoaded();
            recordTest("HDR Gradient Texture Creation", gradientTest);
            allPassed &= gradientTest;
            
            // Test sky texture
            auto skyTexture = HDRTextureFactory::createSky(
                Color3(0.5f, 0.7f, 1.0f), Color3(0.8f, 0.9f, 1.0f), 10.0f);
            bool skyTest = skyTexture && skyTexture->isLoaded();
            recordTest("HDR Sky Texture Creation", skyTest);
            allPassed &= skyTest;
            
        } catch (const std::exception& e) {
            std::cout << "Exception in HDR texture tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test environment light basic functionality
     */
    bool testEnvironmentLightBasic() {
        std::cout << "\n🌍 Environment Light Basic Tests" << std::endl;
        std::cout << "--------------------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Create environment light
            auto envLight = HDRIEnvironmentLightFactory::createSky();
            bool creationTest = (envLight != nullptr);
            recordTest("Environment Light Creation", creationTest);
            allPassed &= creationTest;
            
            if (creationTest) {
                // Test basic properties
                bool nameTest = (envLight->getName() == "HDRIEnvironment");
                recordTest("Environment Light Name", nameTest);
                allPassed &= nameTest;
                
                bool deltaTest = !envLight->isDelta();
                recordTest("Environment Light Non-Delta", deltaTest);
                allPassed &= deltaTest;
                
                // Test evaluation
                Vec3 testDirection(0, 1, 0); // Up direction
                Color3 radiance = envLight->evaluate(testDirection);
                bool evaluationTest = radiance.isValid() && radiance.maxComponent() > 0.0f;
                recordTest("Environment Light Evaluation", evaluationTest);
                allPassed &= evaluationTest;
                
                // Test power calculation
                Color3 power = envLight->power();
                bool powerTest = power.isValid() && power.maxComponent() > 0.0f;
                recordTest("Environment Light Power", powerTest);
                allPassed &= powerTest;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Exception in environment light tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test importance sampling
     */
    bool testImportanceSampling() {
        std::cout << "\n🎯 Importance Sampling Tests" << std::endl;
        std::cout << "----------------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Create environment light with importance sampling
            auto envLight = HDRIEnvironmentLightFactory::createSky();
            envLight->setImportanceSampling(true);
            
            // Create test intersection
            Intersection isect;
            isect.p = Vec3(0, 0, 0);
            isect.n = Vec3(0, 1, 0);
            
            ExtendedRandomSampler sampler(12345);
            
            // Test sampling
            const int numSamples = 1000;
            int validSamples = 0;
            float totalPDF = 0.0f;
            
            for (int i = 0; i < numSamples; ++i) {
                LightSample sample = envLight->sample(isect, sampler);
                
                if (sample.isValid() && sample.pdf > 0.0f) {
                    validSamples++;
                    totalPDF += sample.pdf;
                    
                    // Check that sampled direction is above surface
                    bool aboveSurface = sample.wi.dot(isect.n) > 0.0f;
                    if (!aboveSurface) {
                        std::cout << "Sample below surface detected" << std::endl;
                    }
                }
            }
            
            float validRatio = static_cast<float>(validSamples) / numSamples;
            bool samplingTest = (validRatio > 0.9f); // At least 90% valid samples
            recordTest("Importance Sampling Validity", samplingTest);
            allPassed &= samplingTest;
            
            float avgPDF = totalPDF / validSamples;
            bool pdfTest = (avgPDF > 0.0f && avgPDF < 10.0f); // Reasonable PDF range
            recordTest("Importance Sampling PDF", pdfTest);
            allPassed &= pdfTest;
            
        } catch (const std::exception& e) {
            std::cout << "Exception in importance sampling tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test procedural sky generation
     */
    bool testProceduralSky() {
        std::cout << "\n🌤️ Procedural Sky Tests" << std::endl;
        std::cout << "-----------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Test different sky configurations
            std::vector<std::pair<Color3, Color3>> skyConfigs = {
                {Color3(0.5f, 0.7f, 1.0f), Color3(0.8f, 0.9f, 1.0f)}, // Blue sky
                {Color3(1.0f, 0.8f, 0.6f), Color3(1.0f, 0.9f, 0.8f)}, // Sunset
                {Color3(0.2f, 0.2f, 0.3f), Color3(0.1f, 0.1f, 0.2f)}  // Night
            };
            
            for (size_t i = 0; i < skyConfigs.size(); ++i) {
                auto envLight = HDRIEnvironmentLightFactory::createSky(
                    skyConfigs[i].first, skyConfigs[i].second, 5.0f, 1.0f);
                
                bool creationTest = (envLight != nullptr && envLight->getHDRTexture() != nullptr);
                recordTest("Procedural Sky " + std::to_string(i + 1), creationTest);
                allPassed &= creationTest;
                
                if (creationTest) {
                    // Test evaluation at different directions
                    Vec3 zenith(0, 1, 0);
                    Vec3 horizon(1, 0, 0);
                    
                    Color3 zenithColor = envLight->evaluate(zenith);
                    Color3 horizonColor = envLight->evaluate(horizon);
                    
                    bool evaluationTest = (zenithColor.isValid() && horizonColor.isValid());
                    recordTest("Sky Evaluation " + std::to_string(i + 1), evaluationTest);
                    allPassed &= evaluationTest;
                }
            }
            
        } catch (const std::exception& e) {
            std::cout << "Exception in procedural sky tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test performance benchmarks
     */
    bool testPerformance() {
        std::cout << "\n⚡ Performance Benchmark Tests" << std::endl;
        std::cout << "-----------------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            auto envLight = HDRIEnvironmentLightFactory::createSky();
            
            Intersection isect;
            isect.p = Vec3(0, 0, 0);
            isect.n = Vec3(0, 1, 0);
            
            ExtendedRandomSampler sampler(12345);
            
            // Benchmark sampling
            const int numSamples = 10000;
            auto start = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < numSamples; ++i) {
                LightSample sample = envLight->sample(isect, sampler);
                // Prevent optimization
                volatile float dummy = sample.Li.r + sample.pdf;
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
            
            float nsPerSample = static_cast<float>(duration.count()) / numSamples;
            std::cout << "    Environment sampling: " << nsPerSample << " ns/sample" << std::endl;
            
            // Target: < 1000 ns per sample
            bool performanceTest = (nsPerSample < 1000.0f);
            recordTest("Environment Sampling Performance", performanceTest);
            allPassed &= performanceTest;
            
        } catch (const std::exception& e) {
            std::cout << "Exception in performance tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    void printFinalReport(bool allPassed) {
        std::cout << "\n🎊 HDRI Environment Lighting Test Report" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "Total Tests: " << m_totalTests << std::endl;
        std::cout << "Passed: " << m_passedTests << std::endl;
        std::cout << "Failed: " << (m_totalTests - m_passedTests) << std::endl;
        std::cout << "Success Rate: " << (100.0f * m_passedTests / m_totalTests) << "%" << std::endl;
        
        if (allPassed) {
            std::cout << "\n🎉 ALL TESTS PASSED!" << std::endl;
            std::cout << "✅ HDRI Environment Lighting System Working!" << std::endl;
            std::cout << "🚀 Task 1 Phase 3.2.2 COMPLETE!" << std::endl;
        } else {
            std::cout << "\n❌ Some tests failed." << std::endl;
        }
    }
};

int main() {
    std::cout << "PhotonRender HDRI Environment Lighting Test" << std::endl;
    std::cout << "Phase 3.2.2 Task 1 Validation" << std::endl;
    
    HDRIEnvironmentTest test;
    bool success = test.runTests();
    
    return success ? 0 : 1;
}
