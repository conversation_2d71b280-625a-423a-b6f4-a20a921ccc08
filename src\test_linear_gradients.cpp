// src/test_linear_gradients.cpp
// PhotonRender - Linear Gradients Test Suite
// Tests for linear gradient generation system

#include <iostream>
#include <memory>
#include <chrono>
#include <fstream>
#include <iomanip>
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Test suite for Linear Gradients
 */
class LinearGradientTest {
public:
    /**
     * @brief Run all linear gradient tests
     */
    static bool runAllTests() {
        std::cout << "=== PhotonRender Linear Gradients Test Suite ===\n\n";
        
        int passed = 0;
        int total = 0;
        
        // Basic gradient tests
        if (testBasicLinearGradient()) passed++; total++;
        if (testGradientParameters()) passed++; total++;
        if (testColorStops()) passed++; total++;
        if (testGradientDirection()) passed++; total++;
        if (testRepeatMode()) passed++; total++;
        if (testPerformance()) passed++; total++;
        if (testColorInterpolation()) passed++; total++;
        if (testEdgeCases()) passed++; total++;
        
        std::cout << "\n=== Test Results ===\n";
        std::cout << "Passed: " << passed << "/" << total << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Linear Gradients working perfectly!\n";
            return true;
        } else {
            std::cout << "❌ Some tests failed. Check implementation.\n";
            return false;
        }
    }

private:
    /**
     * @brief Test basic linear gradient generation
     */
    static bool testBasicLinearGradient() {
        std::cout << "Testing basic linear gradient... ";
        
        try {
            // Create basic linear gradient
            Color3 red(1.0f, 0.0f, 0.0f);
            Color3 blue(0.0f, 0.0f, 1.0f);
            
            GradientTexture gradient(GradientType::LINEAR, red, blue);
            
            // Test sampling at various coordinates
            Vec2 testCoords[] = {
                Vec2(0.0f, 0.5f),   // Start
                Vec2(0.5f, 0.5f),   // Middle
                Vec2(1.0f, 0.5f),   // End
                Vec2(0.25f, 0.5f),  // Quarter
                Vec2(0.75f, 0.5f)   // Three quarters
            };
            
            for (const auto& coord : testCoords) {
                Color3 color = gradient.sample(coord);
                float value = gradient.sampleFloat(coord);
                
                // Validate ranges
                if (color.r < 0.0f || color.r > 1.0f ||
                    color.g < 0.0f || color.g > 1.0f ||
                    color.b < 0.0f || color.b > 1.0f ||
                    value < 0.0f || value > 1.0f) {
                    std::cout << "[FAIL] - Values out of range [0,1]\n";
                    return false;
                }
            }
            
            // Test gradient progression
            Color3 startColor = gradient.sample(Vec2(0.0f, 0.5f));
            Color3 endColor = gradient.sample(Vec2(1.0f, 0.5f));
            
            // Start should be more red, end should be more blue
            if (startColor.r < startColor.b || endColor.b < endColor.r) {
                std::cout << "[FAIL] - Gradient direction incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test gradient parameters
     */
    static bool testGradientParameters() {
        std::cout << "Testing gradient parameters... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.startPoint = Vec2(0.2f, 0.2f);
            params.endPoint = Vec2(0.8f, 0.8f);
            params.spread = 2.0f;
            
            std::vector<ColorStop> colorStops = {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            };
            
            GradientTexture gradient(GradientType::LINEAR, params, colorStops);
            
            // Test parameter setting
            gradient.setGradientParams(params);
            gradient.setColorStops(colorStops);
            
            // Sample to ensure no crashes
            Vec2 testUV(0.5f, 0.5f);
            Color3 color = gradient.sample(testUV);
            
            if (color.r < 0.0f || color.r > 1.0f ||
                color.g < 0.0f || color.g > 1.0f ||
                color.b < 0.0f || color.b > 1.0f) {
                std::cout << "[FAIL] - Parameter test color out of range\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test color stops
     */
    static bool testColorStops() {
        std::cout << "Testing color stops... ";
        
        try {
            GradientTexture gradient(GradientType::LINEAR);
            
            // Clear and add color stops
            gradient.clearColorStops();
            gradient.addColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)); // Red
            gradient.addColorStop(0.5f, Color3(0.0f, 1.0f, 0.0f)); // Green
            gradient.addColorStop(1.0f, Color3(0.0f, 0.0f, 1.0f)); // Blue
            
            // Test sampling at color stop positions
            Color3 colorAt0 = gradient.sample(Vec2(0.0f, 0.5f));
            Color3 colorAt05 = gradient.sample(Vec2(0.5f, 0.5f));
            Color3 colorAt1 = gradient.sample(Vec2(1.0f, 0.5f));
            
            // Check if colors are approximately correct
            if (colorAt0.r < 0.8f || colorAt0.g > 0.2f || colorAt0.b > 0.2f) {
                std::cout << "[FAIL] - Color stop 0 incorrect\n";
                return false;
            }
            
            if (colorAt05.g < 0.8f || colorAt05.r > 0.2f || colorAt05.b > 0.2f) {
                std::cout << "[FAIL] - Color stop 0.5 incorrect\n";
                return false;
            }
            
            if (colorAt1.b < 0.8f || colorAt1.r > 0.2f || colorAt1.g > 0.2f) {
                std::cout << "[FAIL] - Color stop 1 incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test gradient direction
     */
    static bool testGradientDirection() {
        std::cout << "Testing gradient direction... ";
        
        try {
            // Test different gradient directions
            GradientParams horizontalParams = GradientParams::defaultParams();
            horizontalParams.startPoint = Vec2(0.0f, 0.5f);
            horizontalParams.endPoint = Vec2(1.0f, 0.5f);
            
            GradientParams verticalParams = GradientParams::defaultParams();
            verticalParams.startPoint = Vec2(0.5f, 0.0f);
            verticalParams.endPoint = Vec2(0.5f, 1.0f);
            
            GradientParams diagonalParams = GradientParams::defaultParams();
            diagonalParams.startPoint = Vec2(0.0f, 0.0f);
            diagonalParams.endPoint = Vec2(1.0f, 1.0f);
            
            std::vector<ColorStop> colorStops = {
                ColorStop(0.0f, Color3(0.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(1.0f, 1.0f, 1.0f))
            };
            
            GradientTexture horizontalGradient(GradientType::LINEAR, horizontalParams, colorStops);
            GradientTexture verticalGradient(GradientType::LINEAR, verticalParams, colorStops);
            GradientTexture diagonalGradient(GradientType::LINEAR, diagonalParams, colorStops);
            
            // Test horizontal gradient
            float leftValue = horizontalGradient.sampleFloat(Vec2(0.0f, 0.5f));
            float rightValue = horizontalGradient.sampleFloat(Vec2(1.0f, 0.5f));
            
            if (leftValue > rightValue) {
                std::cout << "[FAIL] - Horizontal gradient direction incorrect\n";
                return false;
            }
            
            // Test vertical gradient
            float topValue = verticalGradient.sampleFloat(Vec2(0.5f, 0.0f));
            float bottomValue = verticalGradient.sampleFloat(Vec2(0.5f, 1.0f));
            
            if (topValue > bottomValue) {
                std::cout << "[FAIL] - Vertical gradient direction incorrect\n";
                return false;
            }
            
            // Test diagonal gradient
            float cornerValue1 = diagonalGradient.sampleFloat(Vec2(0.0f, 0.0f));
            float cornerValue2 = diagonalGradient.sampleFloat(Vec2(1.0f, 1.0f));
            
            if (cornerValue1 > cornerValue2) {
                std::cout << "[FAIL] - Diagonal gradient direction incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test repeat mode
     */
    static bool testRepeatMode() {
        std::cout << "Testing repeat mode... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.repeat = true;
            params.spread = 0.5f; // Make gradient repeat twice
            
            std::vector<ColorStop> colorStops = {
                ColorStop(0.0f, Color3(0.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(1.0f, 1.0f, 1.0f))
            };
            
            GradientTexture repeatGradient(GradientType::LINEAR, params, colorStops);
            
            // Test that gradient repeats
            float value1 = repeatGradient.sampleFloat(Vec2(0.25f, 0.5f));
            float value2 = repeatGradient.sampleFloat(Vec2(0.75f, 0.5f));
            
            // Due to repeat, these should be similar
            if (std::abs(value1 - value2) > 0.1f) {
                std::cout << "[FAIL] - Repeat mode not working correctly\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test performance
     */
    static bool testPerformance() {
        std::cout << "Testing performance... ";
        
        try {
            GradientTexture gradient(GradientType::LINEAR, Color3(0.0f), Color3(1.0f));
            
            const int numSamples = 10000;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            float totalValue = 0.0f;
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                totalValue += gradient.sampleFloat(Vec2(u, v));
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            
            float avgTimeNs = float(duration.count()) / numSamples;
            
            // Performance target: < 1000ns per sample
            if (avgTimeNs > 1000.0f) {
                std::cout << "[FAIL] - Performance too slow: " << avgTimeNs << "ns per sample\n";
                return false;
            }
            
            std::cout << "[PASS] (" << std::fixed << std::setprecision(2) << avgTimeNs << "ns per sample)\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test color interpolation
     */
    static bool testColorInterpolation() {
        std::cout << "Testing color interpolation... ";
        
        try {
            GradientTexture gradient(GradientType::LINEAR);
            gradient.clearColorStops();
            gradient.addColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)); // Red
            gradient.addColorStop(1.0f, Color3(0.0f, 0.0f, 1.0f)); // Blue
            
            // Test interpolation at middle point
            Color3 middleColor = gradient.sample(Vec2(0.5f, 0.5f));
            
            // Should be purple (mix of red and blue)
            if (middleColor.r < 0.3f || middleColor.r > 0.7f ||
                middleColor.g > 0.2f ||
                middleColor.b < 0.3f || middleColor.b > 0.7f) {
                std::cout << "[FAIL] - Color interpolation incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test edge cases
     */
    static bool testEdgeCases() {
        std::cout << "Testing edge cases... ";
        
        try {
            // Test with no color stops
            GradientTexture emptyGradient(GradientType::LINEAR);
            emptyGradient.clearColorStops();
            
            Color3 emptyColor = emptyGradient.sample(Vec2(0.5f, 0.5f));
            if (emptyColor.r != 0.0f || emptyColor.g != 0.0f || emptyColor.b != 0.0f) {
                std::cout << "[FAIL] - Empty gradient should return black\n";
                return false;
            }
            
            // Test with single color stop
            GradientTexture singleGradient(GradientType::LINEAR);
            singleGradient.clearColorStops();
            singleGradient.addColorStop(0.5f, Color3(0.5f, 0.5f, 0.5f));
            
            Color3 singleColor = singleGradient.sample(Vec2(0.0f, 0.0f));
            if (std::abs(singleColor.r - 0.5f) > 0.01f ||
                std::abs(singleColor.g - 0.5f) > 0.01f ||
                std::abs(singleColor.b - 0.5f) > 0.01f) {
                std::cout << "[FAIL] - Single color stop gradient incorrect\n";
                return false;
            }
            
            // Test with degenerate gradient (same start and end points)
            GradientParams degenerateParams = GradientParams::defaultParams();
            degenerateParams.startPoint = Vec2(0.5f, 0.5f);
            degenerateParams.endPoint = Vec2(0.5f, 0.5f);
            
            GradientTexture degenerateGradient(GradientType::LINEAR, degenerateParams, {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            });
            
            Color3 degenerateColor = degenerateGradient.sample(Vec2(0.5f, 0.5f));
            // Should not crash and return valid color
            if (std::isnan(degenerateColor.r) || std::isnan(degenerateColor.g) || std::isnan(degenerateColor.b)) {
                std::cout << "[FAIL] - Degenerate gradient produces NaN\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    bool success = LinearGradientTest::runAllTests();
    return success ? 0 : 1;
}
