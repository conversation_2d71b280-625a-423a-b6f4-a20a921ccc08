// src/core/texture/texture.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Texture system implementation

#include "texture.hpp"
#include "../math/vec2.hpp"
#include "../image/image_io.hpp"
#include <algorithm>
#include <cmath>

namespace photon {

// Constants
static const float PI = 3.14159265359f;

// Texture base class implementation
float Texture::applyWrap(float coord) const {
    switch (m_wrap) {
        case TextureWrap::REPEAT:
            return coord - std::floor(coord);
        case TextureWrap::CLAMP:
            return std::clamp(coord, 0.0f, 1.0f);
        case TextureWrap::MIRROR: {
            float t = coord - std::floor(coord);
            int i = int(std::floor(coord));
            return (i % 2 == 0) ? t : (1.0f - t);
        }
        case TextureWrap::BORDER:
            return (coord < 0.0f || coord > 1.0f) ? -1.0f : coord; // -1 indicates border
        default:
            return coord;
    }
}

Vec2 Texture::applyWrap(const Vec2& uv) const {
    return Vec2(applyWrap(uv.x), applyWrap(uv.y));
}

// ImageTexture implementation
ImageTexture::ImageTexture(int width, int height, int channels, const float* data)
    : m_width(width), m_height(height), m_channels(channels) {
    
    // Determine format
    if (channels == 1) {
        m_format = TextureFormat::R32F;
    } else if (channels == 3) {
        m_format = TextureFormat::RGB32F;
    } else if (channels == 4) {
        m_format = TextureFormat::RGBA32F;
    }
    
    // Copy data
    size_t dataSize = width * height * channels;
    m_data.resize(dataSize);
    std::copy(data, data + dataSize, m_data.begin());
}

bool ImageTexture::loadFromFile(const std::string& filename) {
    setName(filename);
    
    // Load image using ImageIO
    ImageData imageData = ImageIO::loadImage(filename);
    
    if (imageData.pixels.empty()) {
        return false;
    }
    
    m_width = imageData.width;
    m_height = imageData.height;
    m_channels = imageData.channels;
    m_data = std::move(imageData.pixels);
    
    // Determine format
    if (m_channels == 1) {
        m_format = TextureFormat::R32F;
    } else if (m_channels == 3) {
        m_format = TextureFormat::RGB32F;
    } else if (m_channels == 4) {
        m_format = TextureFormat::RGBA32F;
    }
    
    return true;
}

std::shared_ptr<ImageTexture> ImageTexture::createSolid(const Color3& color) {
    float data[3] = { color.r, color.g, color.b };
    auto texture = std::make_shared<ImageTexture>(1, 1, 3, data);
    texture->setName("solid_color");
    return texture;
}

std::shared_ptr<ImageTexture> ImageTexture::createSolid(float value) {
    float data[1] = { value };
    auto texture = std::make_shared<ImageTexture>(1, 1, 1, data);
    texture->setName("solid_value");
    return texture;
}

Color3 ImageTexture::sample(const Vec2& uv) const {
    if (m_data.empty()) {
        return Color3(1.0f, 0.0f, 1.0f); // Magenta for missing texture
    }
    
    Vec2 wrappedUV = applyWrap(uv);
    
    // Check for border wrap
    if (m_wrap == TextureWrap::BORDER && (wrappedUV.x < 0.0f || wrappedUV.y < 0.0f)) {
        return Color3(0.0f); // Border color (black)
    }
    
    switch (m_filter) {
        case TextureFilter::NEAREST:
            return samplePixel(int(wrappedUV.x * m_width), int(wrappedUV.y * m_height));
        case TextureFilter::BILINEAR:
        default:
            return sampleBilinear(wrappedUV.x, wrappedUV.y);
    }
}

Color3 ImageTexture::sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const {
    // For now, ignore derivatives and use regular sampling
    // TODO: Implement proper mipmap filtering
    return sample(uv);
}

float ImageTexture::sampleFloat(const Vec2& uv) const {
    if (m_channels == 1) {
        // Single channel texture
        Vec2 wrappedUV = applyWrap(uv);
        if (m_wrap == TextureWrap::BORDER && (wrappedUV.x < 0.0f || wrappedUV.y < 0.0f)) {
            return 0.0f;
        }
        
        int x = std::clamp(int(wrappedUV.x * m_width), 0, m_width - 1);
        int y = std::clamp(int(wrappedUV.y * m_height), 0, m_height - 1);
        int index = (y * m_width + x) * m_channels;
        
        return m_data[index];
    } else {
        // Multi-channel texture - use luminance
        Color3 color = sample(uv);
        return color.luminance();
    }
}

Color3 ImageTexture::samplePixel(int x, int y) const {
    x = std::clamp(x, 0, m_width - 1);
    y = std::clamp(y, 0, m_height - 1);
    
    int index = (y * m_width + x) * m_channels;
    
    Color3 color;
    if (m_channels >= 3) {
        color.r = m_data[index];
        color.g = m_data[index + 1];
        color.b = m_data[index + 2];
    } else if (m_channels == 1) {
        float value = m_data[index];
        color = Color3(value);
    }
    
    return applyGamma(color);
}

Color3 ImageTexture::sampleBilinear(float u, float v) const {
    // Convert to pixel coordinates
    float x = u * m_width - 0.5f;
    float y = v * m_height - 0.5f;
    
    int x0 = int(std::floor(x));
    int y0 = int(std::floor(y));
    int x1 = x0 + 1;
    int y1 = y0 + 1;
    
    float fx = x - x0;
    float fy = y - y0;
    
    // Sample four neighboring pixels
    Color3 c00 = samplePixel(x0, y0);
    Color3 c10 = samplePixel(x1, y0);
    Color3 c01 = samplePixel(x0, y1);
    Color3 c11 = samplePixel(x1, y1);
    
    // Bilinear interpolation
    Color3 c0 = c00 * (1.0f - fx) + c10 * fx;
    Color3 c1 = c01 * (1.0f - fx) + c11 * fx;
    
    return c0 * (1.0f - fy) + c1 * fy;
}

Color3 ImageTexture::applyGamma(const Color3& color) const {
    if (m_gamma == 1.0f) {
        return color;
    }
    
    float invGamma = 1.0f / m_gamma;
    return Color3(
        std::pow(color.r, invGamma),
        std::pow(color.g, invGamma),
        std::pow(color.b, invGamma)
    );
}

// CheckerboardTexture implementation
CheckerboardTexture::CheckerboardTexture(const Color3& color1, const Color3& color2, float scale)
    : m_color1(color1), m_color2(color2), m_scale(scale) {
    setName("checkerboard");
}

Color3 CheckerboardTexture::sample(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);
    
    float u = wrappedUV.x * m_scale;
    float v = wrappedUV.y * m_scale;
    
    int checkU = int(std::floor(u));
    int checkV = int(std::floor(v));
    
    bool isEven = (checkU + checkV) % 2 == 0;
    return isEven ? m_color1 : m_color2;
}

// NoiseTexture implementation
NoiseTexture::NoiseTexture(float frequency, float amplitude, int octaves)
    : m_frequency(frequency), m_amplitude(amplitude) {
    m_fractal.octaves = octaves;
    setName("noise");
}

NoiseTexture::NoiseTexture(NoiseType type, float frequency, float amplitude, const FractalParams& fractal)
    : m_noiseType(type), m_frequency(frequency), m_amplitude(amplitude), m_fractal(fractal) {
    setName("noise_" + std::to_string(static_cast<int>(type)));
}

Color3 NoiseTexture::sample(const Vec2& uv) const {
    float noise = sampleFloat(uv);
    return Color3(noise);
}

float NoiseTexture::sampleFloat(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);

    // Generate fractal noise
    float noise = generateFractalNoise(wrappedUV.x, wrappedUV.y);

    // Apply amplitude and normalize to [0,1]
    noise *= m_amplitude;
    noise = (noise + 1.0f) * 0.5f;
    noise = std::clamp(noise, 0.0f, 1.0f);

    return noise;
}

float NoiseTexture::generateFractalNoise(float x, float y) const {
    float result = 0.0f;
    float amplitude = 1.0f;
    float frequency = m_frequency * m_fractal.scale;
    float maxValue = 0.0f;

    for (int i = 0; i < m_fractal.octaves; ++i) {
        float noiseValue = 0.0f;

        // Select noise algorithm
        switch (m_noiseType) {
            case NoiseType::PERLIN:
                noiseValue = perlinNoise(x * frequency, y * frequency);
                break;
            case NoiseType::SIMPLEX:
                noiseValue = simplexNoise(x * frequency, y * frequency);
                break;
            case NoiseType::WORLEY:
                noiseValue = worleyNoise(x * frequency, y * frequency);
                break;
        }

        result += amplitude * noiseValue;
        maxValue += amplitude;

        amplitude *= m_fractal.persistence;
        frequency *= m_fractal.lacunarity;
    }

    // Normalize by maximum possible value
    if (maxValue > 0.0f) {
        result /= maxValue;
    }

    return result;
}

float NoiseTexture::perlinNoise(float x, float y) const {
    // Improved Perlin noise implementation
    int xi = int(std::floor(x)) & 255;
    int yi = int(std::floor(y)) & 255;

    float xf = x - std::floor(x);
    float yf = y - std::floor(y);

    // Fade curves (Ken Perlin's improved version)
    float u = smoothstep(xf);
    float v = smoothstep(yf);

    // Hash coordinates with seed
    float n00 = hash(xi + m_seed, yi + m_seed);
    float n10 = hash(xi + 1 + m_seed, yi + m_seed);
    float n01 = hash(xi + m_seed, yi + 1 + m_seed);
    float n11 = hash(xi + 1 + m_seed, yi + 1 + m_seed);

    // Convert to gradients
    n00 = n00 * 2.0f - 1.0f;
    n10 = n10 * 2.0f - 1.0f;
    n01 = n01 * 2.0f - 1.0f;
    n11 = n11 * 2.0f - 1.0f;

    // Dot product with distance vectors
    float dot00 = n00 * (xf * 0.5f + yf * 0.866f);
    float dot10 = n10 * ((xf - 1.0f) * 0.5f + yf * 0.866f);
    float dot01 = n01 * (xf * 0.5f + (yf - 1.0f) * 0.866f);
    float dot11 = n11 * ((xf - 1.0f) * 0.5f + (yf - 1.0f) * 0.866f);

    // Interpolate
    float x1 = lerp(dot00, dot10, u);
    float x2 = lerp(dot01, dot11, u);

    return lerp(x1, x2, v);
}

float NoiseTexture::simplexNoise(float x, float y) const {
    // Simplex noise implementation (Ken Perlin 2001)
    const float F2 = 0.5f * (std::sqrt(3.0f) - 1.0f);
    const float G2 = (3.0f - std::sqrt(3.0f)) / 6.0f;

    // Skew the input space
    float s = (x + y) * F2;
    int i = int(std::floor(x + s));
    int j = int(std::floor(y + s));

    float t = (i + j) * G2;
    float X0 = i - t;
    float Y0 = j - t;
    float x0 = x - X0;
    float y0 = y - Y0;

    // Determine which simplex we are in
    int i1, j1;
    if (x0 > y0) {
        i1 = 1; j1 = 0;
    } else {
        i1 = 0; j1 = 1;
    }

    // Offsets for second and third corners
    float x1 = x0 - i1 + G2;
    float y1 = y0 - j1 + G2;
    float x2 = x0 - 1.0f + 2.0f * G2;
    float y2 = y0 - 1.0f + 2.0f * G2;

    // Calculate contributions from each corner
    float n0 = 0.0f, n1 = 0.0f, n2 = 0.0f;

    float t0 = 0.5f - x0 * x0 - y0 * y0;
    if (t0 >= 0.0f) {
        t0 *= t0;
        float grad0 = hash(i + m_seed, j + m_seed) * 2.0f - 1.0f;
        n0 = t0 * t0 * grad0 * (x0 + y0);
    }

    float t1 = 0.5f - x1 * x1 - y1 * y1;
    if (t1 >= 0.0f) {
        t1 *= t1;
        float grad1 = hash(i + i1 + m_seed, j + j1 + m_seed) * 2.0f - 1.0f;
        n1 = t1 * t1 * grad1 * (x1 + y1);
    }

    float t2 = 0.5f - x2 * x2 - y2 * y2;
    if (t2 >= 0.0f) {
        t2 *= t2;
        float grad2 = hash(i + 1 + m_seed, j + 1 + m_seed) * 2.0f - 1.0f;
        n2 = t2 * t2 * grad2 * (x2 + y2);
    }

    // Sum contributions and scale to [-1, 1]
    return 70.0f * (n0 + n1 + n2);
}

float NoiseTexture::worleyNoise(float x, float y) const {
    // Worley noise (cellular/Voronoi noise)
    int xi = int(std::floor(x));
    int yi = int(std::floor(y));

    float minDist = 10.0f;

    // Check 3x3 grid of cells
    for (int dy = -1; dy <= 1; ++dy) {
        for (int dx = -1; dx <= 1; ++dx) {
            int cellX = xi + dx;
            int cellY = yi + dy;

            // Generate random point in cell
            float pointX = cellX + hash(cellX + m_seed, cellY + m_seed);
            float pointY = cellY + hash(cellX + m_seed + 1, cellY + m_seed + 1);

            // Calculate distance to point
            float distX = x - pointX;
            float distY = y - pointY;
            float dist = std::sqrt(distX * distX + distY * distY);

            minDist = std::min(minDist, dist);
        }
    }

    // Convert distance to noise value [-1, 1]
    return std::clamp(minDist * 2.0f - 1.0f, -1.0f, 1.0f);
}

float NoiseTexture::hash(float x, float y) const {
    // High-quality hash function for noise generation
    float n = std::sin(x * 12.9898f + y * 78.233f) * 43758.5453f;
    return n - std::floor(n);
}

float NoiseTexture::smoothstep(float t) const {
    // Ken Perlin's improved smoothstep function
    return t * t * t * (t * (t * 6.0f - 15.0f) + 10.0f);
}

float NoiseTexture::lerp(float a, float b, float t) const {
    return a + t * (b - a);
}

// PatternTexture implementation
PatternTexture::PatternTexture(PatternType type, float scale,
                               const Color3& color1, const Color3& color2)
    : m_patternType(type), m_color1(color1), m_color2(color2) {
    m_params = PatternParams::defaultParams();
    m_params.scale = scale;
    setName("pattern_" + std::to_string(static_cast<int>(type)));
}

PatternTexture::PatternTexture(PatternType type, const PatternParams& params,
                               const Color3& color1, const Color3& color2)
    : m_patternType(type), m_params(params), m_color1(color1), m_color2(color2) {
    setName("pattern_" + std::to_string(static_cast<int>(type)));
}

Color3 PatternTexture::sample(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);
    float pattern = sampleFloat(wrappedUV);

    // Interpolate between colors based on pattern value
    return m_color1 * pattern + m_color2 * (1.0f - pattern);
}

float PatternTexture::sampleFloat(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);

    // Apply rotation and offset
    Vec2 transformedUV = applyRotation(wrappedUV + m_params.offset);

    // Scale coordinates
    float x = transformedUV.x * m_params.scale;
    float y = transformedUV.y * m_params.scale;

    return generatePattern(x, y);
}

float PatternTexture::generatePattern(float x, float y) const {
    switch (m_patternType) {
        case PatternType::CHECKERBOARD:
            return checkerboardPattern(x, y);
        case PatternType::STRIPES:
            return stripePattern(x, y);
        case PatternType::DOTS:
            return dotPattern(x, y);
        case PatternType::GRID:
            return gridPattern(x, y);
        default:
            return 0.0f;
    }
}

float PatternTexture::checkerboardPattern(float x, float y) const {
    // Classic checkerboard pattern
    int xi = static_cast<int>(std::floor(x));
    int yi = static_cast<int>(std::floor(y));

    // XOR to create checkerboard
    bool checker = (xi % 2) ^ (yi % 2);

    if (!m_params.antiAlias) {
        return checker ? 1.0f : 0.0f;
    }

    // Anti-aliased version
    float fx = x - std::floor(x);
    float fy = y - std::floor(y);

    // Distance to nearest edge
    float edgeDistX = std::min(fx, 1.0f - fx);
    float edgeDistY = std::min(fy, 1.0f - fy);
    float edgeDist = std::min(edgeDistX, edgeDistY);

    // Smooth transition near edges
    float smoothWidth = 0.02f; // Adjust for more/less anti-aliasing
    float smoothFactor = smoothStep(0.0f, smoothWidth, edgeDist);

    return checker ? smoothFactor : (1.0f - smoothFactor);
}

float PatternTexture::stripePattern(float x, float y) const {
    // Horizontal stripes (can be rotated via transformation)
    float stripe = std::sin(y * 2.0f * PI);

    if (!m_params.antiAlias) {
        return stripe > 0.0f ? 1.0f : 0.0f;
    }

    // Anti-aliased stripes
    float smoothWidth = 0.1f;
    return smoothStep(-smoothWidth, smoothWidth, stripe);
}

float PatternTexture::dotPattern(float x, float y) const {
    // Grid of dots
    float cellX = x - std::floor(x);
    float cellY = y - std::floor(y);

    // Center dot in each cell
    float centerX = 0.5f;
    float centerY = 0.5f;

    // Distance from center of cell
    float dx = cellX - centerX;
    float dy = cellY - centerY;
    float dist = std::sqrt(dx * dx + dy * dy);

    if (!m_params.antiAlias) {
        return dist <= m_params.dotSize ? 1.0f : 0.0f;
    }

    // Anti-aliased dots
    float smoothWidth = 0.02f;
    return 1.0f - smoothStep(m_params.dotSize - smoothWidth,
                            m_params.dotSize + smoothWidth, dist);
}

float PatternTexture::gridPattern(float x, float y) const {
    // Grid lines
    float cellX = x - std::floor(x);
    float cellY = y - std::floor(y);

    // Distance to nearest grid line
    float distToLineX = std::min(cellX, 1.0f - cellX);
    float distToLineY = std::min(cellY, 1.0f - cellY);

    bool onLineX = distToLineX <= m_params.lineWidth * 0.5f;
    bool onLineY = distToLineY <= m_params.lineWidth * 0.5f;

    if (!m_params.antiAlias) {
        return (onLineX || onLineY) ? 1.0f : 0.0f;
    }

    // Anti-aliased grid
    float smoothWidth = 0.01f;
    float lineX = 1.0f - smoothStep(m_params.lineWidth * 0.5f - smoothWidth,
                                   m_params.lineWidth * 0.5f + smoothWidth, distToLineX);
    float lineY = 1.0f - smoothStep(m_params.lineWidth * 0.5f - smoothWidth,
                                   m_params.lineWidth * 0.5f + smoothWidth, distToLineY);

    // Combine lines (max operation for union)
    return std::max(lineX, lineY);
}

Vec2 PatternTexture::applyRotation(const Vec2& uv) const {
    if (std::abs(m_params.rotation) < 1e-6f) {
        return uv; // No rotation
    }

    float cos_r = std::cos(m_params.rotation);
    float sin_r = std::sin(m_params.rotation);

    // Rotate around center (0.5, 0.5)
    Vec2 centered = uv - Vec2(0.5f);
    Vec2 rotated(
        centered.x * cos_r - centered.y * sin_r,
        centered.x * sin_r + centered.y * cos_r
    );

    return rotated + Vec2(0.5f);
}

float PatternTexture::smoothStep(float edge0, float edge1, float x) const {
    float t = std::clamp((x - edge0) / (edge1 - edge0), 0.0f, 1.0f);
    return t * t * (3.0f - 2.0f * t);
}

// GradientTexture implementation
GradientTexture::GradientTexture(GradientType type, const Color3& color1, const Color3& color2)
    : m_gradientType(type) {
    m_params = GradientParams::defaultParams();
    m_colorStops.clear();
    m_colorStops.emplace_back(0.0f, color1);
    m_colorStops.emplace_back(1.0f, color2);
    setName("gradient_" + std::to_string(static_cast<int>(type)));
}

GradientTexture::GradientTexture(GradientType type, const GradientParams& params,
                                 const std::vector<ColorStop>& colorStops)
    : m_gradientType(type), m_params(params), m_colorStops(colorStops) {
    sortColorStops();
    setName("gradient_" + std::to_string(static_cast<int>(type)));
}

Color3 GradientTexture::sample(const Vec2& uv) const {
    Vec2 wrappedUV = applyWrap(uv);
    float gradientValue = generateGradientValue(wrappedUV.x, wrappedUV.y);

    // Apply repeat mode if enabled
    if (m_params.repeat) {
        gradientValue = applyRepeat(gradientValue);
    } else {
        // Clamp to [0,1] range only if not repeating
        gradientValue = std::clamp(gradientValue, 0.0f, 1.0f);
    }

    return interpolateColorStops(gradientValue);
}

float GradientTexture::sampleFloat(const Vec2& uv) const {
    Color3 color = sample(uv);
    return color.luminance();
}

float GradientTexture::generateGradientValue(float x, float y) const {
    switch (m_gradientType) {
        case GradientType::LINEAR:
            return linearGradient(x, y);
        case GradientType::RADIAL:
            return radialGradient(x, y);
        case GradientType::ANGULAR:
            return angularGradient(x, y);
        default:
            return 0.0f;
    }
}

float GradientTexture::linearGradient(float x, float y) const {
    // Calculate gradient direction vector
    Vec2 direction = m_params.endPoint - m_params.startPoint;
    float dirLength = std::sqrt(direction.x * direction.x + direction.y * direction.y);

    if (dirLength < 1e-6f) {
        return 0.0f; // Degenerate gradient
    }

    // Calculate position relative to start point
    Vec2 position(x, y);
    Vec2 relativePos = position - m_params.startPoint;

    // Project onto gradient direction (dot product)
    float projection = relativePos.x * direction.x + relativePos.y * direction.y;

    // Normalize by gradient length squared to get t in [0,1] range
    float t = projection / (dirLength * dirLength);

    // Apply spread
    t *= m_params.spread;

    // Debug output for coordinates near (1.0, 0.5)
    if (std::abs(x - 1.0f) < 0.01f && std::abs(y - 0.5f) < 0.01f) {
        std::cout << "DEBUG linearGradient(" << x << ", " << y << "):\n";
        std::cout << "  startPoint: (" << m_params.startPoint.x << ", " << m_params.startPoint.y << ")\n";
        std::cout << "  endPoint: (" << m_params.endPoint.x << ", " << m_params.endPoint.y << ")\n";
        std::cout << "  direction: (" << direction.x << ", " << direction.y << ")\n";
        std::cout << "  dirLength: " << dirLength << "\n";
        std::cout << "  relativePos: (" << relativePos.x << ", " << relativePos.y << ")\n";
        std::cout << "  projection: " << projection << "\n";
        std::cout << "  t (before spread): " << (projection / (dirLength * dirLength)) << "\n";
        std::cout << "  spread: " << m_params.spread << "\n";
        std::cout << "  t (final): " << t << "\n";
    }

    return t;
}

float GradientTexture::radialGradient(float x, float y) const {
    // Calculate distance from center
    float dx = x - m_params.center.x;
    float dy = y - m_params.center.y;
    float distance = std::sqrt(dx * dx + dy * dy);

    // Normalize by radius and apply spread
    float t = (distance / m_params.radius) * m_params.spread;

    return t;
}

float GradientTexture::angularGradient(float x, float y) const {
    // Calculate angle from center
    float dx = x - m_params.center.x;
    float dy = y - m_params.center.y;

    float angle = std::atan2(dy, dx);

    // Normalize angle to [0, 1] range
    angle += PI; // Shift from [-π, π] to [0, 2π]
    angle /= (2.0f * PI); // Normalize to [0, 1]

    // Apply rotation offset
    angle += m_params.angle / (2.0f * PI);

    // Wrap to [0, 1] range
    angle = angle - std::floor(angle);

    // Apply spread
    float t = angle * m_params.spread;

    return t;
}

Color3 GradientTexture::interpolateColorStops(float t) const {
    if (m_colorStops.empty()) {
        return Color3(0.0f); // No color stops
    }

    if (m_colorStops.size() == 1) {
        return m_colorStops[0].color; // Single color stop
    }

    // Don't clamp here - let the caller handle clamping
    // t = std::clamp(t, 0.0f, 1.0f);

    // Find the two color stops to interpolate between
    for (size_t i = 0; i < m_colorStops.size() - 1; ++i) {
        const ColorStop& stop1 = m_colorStops[i];
        const ColorStop& stop2 = m_colorStops[i + 1];

        if (t >= stop1.position && t <= stop2.position) {
            // Interpolate between these two stops
            float range = stop2.position - stop1.position;
            if (range < 1e-6f) {
                return stop1.color; // Degenerate range
            }

            float localT = (t - stop1.position) / range;

            // Linear interpolation
            return Color3(
                stop1.color.r + localT * (stop2.color.r - stop1.color.r),
                stop1.color.g + localT * (stop2.color.g - stop1.color.g),
                stop1.color.b + localT * (stop2.color.b - stop1.color.b)
            );
        }
    }

    // If we get here, t is outside the range of color stops
    if (t < m_colorStops[0].position) {
        return m_colorStops[0].color;
    } else {
        return m_colorStops.back().color;
    }
}

void GradientTexture::sortColorStops() {
    std::sort(m_colorStops.begin(), m_colorStops.end(),
              [](const ColorStop& a, const ColorStop& b) {
                  return a.position < b.position;
              });
}

float GradientTexture::applyRepeat(float t) const {
    // Repeat the gradient pattern
    return t - std::floor(t);
}

} // namespace photon
