// src/core/scene/light.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light source implementations

#include "light.hpp"
#include "../sampler/sampler.hpp"
#include "scene.hpp"
#include "../common.hpp"
#include <algorithm>

namespace photon {

// PointLight implementation
PointLight::PointLight()
    : m_position(Point3(0, 1, 0)), m_intensity(Color3(1, 1, 1)) {
}

PointLight::PointLight(const Point3& position, const Color3& intensity)
    : m_position(position), m_intensity(intensity) {
}

LightSample PointLight::sample(const Intersection& isect, Sampler& sampler) const {
    Vec3 wi = (m_position - isect.p);
    float distance = wi.length();
    wi /= distance; // Normalize
    
    // Inverse square law
    Color3 Li = m_intensity / (distance * distance);
    
    return LightSample(Li, wi, 1.0f, distance, true);
}

Color3 PointLight::Li(const Intersection& isect, const Vec3& wi) const {
    // Check if direction points towards light
    Vec3 toLight = (m_position - isect.p).normalized();
    if (wi.dot(toLight) < 0.999f) return Color3(0); // Not pointing at light
    
    float distance = (m_position - isect.p).length();
    return m_intensity / (distance * distance);
}

float PointLight::pdf(const Intersection& isect, const Vec3& wi) const {
    return 0.0f; // Delta light
}

Color3 PointLight::power() const {
    return m_intensity * 4.0f * M_PI; // Integrate over sphere
}

// DirectionalLight implementation
DirectionalLight::DirectionalLight(const Vec3& direction, const Color3& irradiance)
    : m_direction(direction.normalized()), m_irradiance(irradiance), m_worldRadius(1000.0f) {
}

LightSample DirectionalLight::sample(const Intersection& isect, Sampler& sampler) const {
    Vec3 wi = -m_direction; // Direction towards light
    float distance = 2.0f * m_worldRadius; // Far away
    
    return LightSample(m_irradiance, wi, 1.0f, distance, true);
}

Color3 DirectionalLight::Li(const Intersection& isect, const Vec3& wi) const {
    // Check if direction matches light direction
    if (wi.dot(-m_direction) < 0.999f) return Color3(0);
    
    return m_irradiance;
}

float DirectionalLight::pdf(const Intersection& isect, const Vec3& wi) const {
    return 0.0f; // Delta light
}

Color3 DirectionalLight::power() const {
    return m_irradiance * M_PI * m_worldRadius * m_worldRadius;
}

// AreaLight implementation
AreaLight::AreaLight(const Color3& emission, float area)
    : m_emission(emission), m_area(area) {
}

LightSample AreaLight::sample(const Intersection& isect, Sampler& sampler) const {
    // Sample point on light surface
    Point3 lightPoint = sampleSurface(sampler);
    Normal3 lightNormal = getNormal(lightPoint);

    Vec3 wi = (lightPoint - isect.p);
    float distance = wi.length();
    wi /= distance;

    // Check if light faces the surface
    float cosTheta = -wi.dot(lightNormal);
    if (cosTheta <= 0.0f) return LightSample(); // Back-facing

    // Convert area PDF to solid angle PDF
    float pdf = (distance * distance) / (m_area * cosTheta);

    return LightSample(m_emission, wi, pdf, distance, false);
}

// Simple quad area light implementation
class QuadAreaLight : public AreaLight {
public:
    QuadAreaLight(const Point3& corner, const Vec3& u, const Vec3& v, const Color3& emission)
        : AreaLight(emission, u.cross(v).length()), m_corner(corner), m_u(u), m_v(v) {
        m_normal = u.cross(v).normalized();
    }

    Point3 sampleSurface(Sampler& sampler) const override {
        float u = sampler.get1D();
        float v = sampler.get1D();
        return m_corner + u * m_u + v * m_v;
    }

    Normal3 getNormal(const Point3& point) const override {
        return m_normal;
    }

private:
    Point3 m_corner;
    Vec3 m_u, m_v;
    Normal3 m_normal;
};

Color3 AreaLight::Li(const Intersection& isect, const Vec3& wi) const {
    return m_emission; // Simplified - should check if ray hits light
}

float AreaLight::pdf(const Intersection& isect, const Vec3& wi) const {
    // Simplified - should compute actual solid angle PDF
    return 1.0f / (4.0f * M_PI);
}

Color3 AreaLight::power() const {
    return m_emission * m_area * M_PI;
}

// EnvironmentLight implementation
EnvironmentLight::EnvironmentLight(const Color3& emission)
    : m_emission(emission), m_worldRadius(1000.0f) {
}

LightSample EnvironmentLight::sample(const Intersection& isect, Sampler& sampler) const {
    // Sample uniform direction on hemisphere
    float u1 = sampler.get1D();
    float u2 = sampler.get1D();
    
    float cosTheta = u1;
    float sinTheta = std::sqrt(1.0f - u1 * u1);
    float phi = 2.0f * M_PI * u2;
    
    Vec3 wi(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);
    
    // Transform to world space around surface normal
    Normal3 n = isect.n;
    Vec3 nt = (std::abs(n.x) > 0.1f) ? Vec3(0, 1, 0) : Vec3(1, 0, 0);
    Vec3 tangent = n.cross(nt).normalized();
    Vec3 bitangent = n.cross(tangent);
    
    Vec3 worldWi = wi.x * tangent + wi.y * bitangent + wi.z * n;
    
    float pdf = 1.0f / (2.0f * M_PI); // Uniform hemisphere
    Color3 Li = evaluate(worldWi);
    
    return LightSample(Li, worldWi, pdf, m_worldRadius, false);
}

Color3 EnvironmentLight::Li(const Intersection& isect, const Vec3& wi) const {
    return evaluate(wi);
}

float EnvironmentLight::pdf(const Intersection& isect, const Vec3& wi) const {
    float cosTheta = std::max(0.0f, wi.dot(isect.n));
    return cosTheta > 0.0f ? 1.0f / (2.0f * M_PI) : 0.0f;
}

Color3 EnvironmentLight::power() const {
    return m_emission * 2.0f * M_PI * M_PI * m_worldRadius * m_worldRadius;
}

Color3 EnvironmentLight::evaluate(const Vec3& direction) const {
    return m_emission; // Constant environment
}

// SpotLight implementation
SpotLight::SpotLight(const Point3& position, const Vec3& direction, const Color3& intensity,
                    float innerAngle, float outerAngle)
    : m_position(position), m_direction(direction.normalized()), m_intensity(intensity),
      m_innerAngle(innerAngle), m_outerAngle(outerAngle),
      m_cosInner(std::cos(innerAngle)), m_cosOuter(std::cos(outerAngle)) {
}

LightSample SpotLight::sample(const Intersection& isect, Sampler& sampler) const {
    Vec3 wi = (m_position - isect.p);
    float distance = wi.length();
    wi /= distance;
    
    // Check if point is within spot cone
    float cosTheta = (-wi).dot(m_direction);
    if (cosTheta < m_cosOuter) {
        return LightSample(); // Outside cone
    }
    
    // Apply falloff
    float falloffFactor = falloff(-wi);
    Color3 Li = m_intensity * falloffFactor / (distance * distance);
    
    return LightSample(Li, wi, 1.0f, distance, true);
}

Color3 SpotLight::Li(const Intersection& isect, const Vec3& wi) const {
    Vec3 toLight = (m_position - isect.p).normalized();
    if (wi.dot(toLight) < 0.999f) return Color3(0);
    
    float cosTheta = (-wi).dot(m_direction);
    if (cosTheta < m_cosOuter) return Color3(0);
    
    float distance = (m_position - isect.p).length();
    float falloffFactor = falloff(-wi);
    
    return m_intensity * falloffFactor / (distance * distance);
}

float SpotLight::pdf(const Intersection& isect, const Vec3& wi) const {
    return 0.0f; // Delta light
}

Color3 SpotLight::power() const {
    // Approximate power (should integrate over cone)
    float solidAngle = 2.0f * M_PI * (1.0f - m_cosOuter);
    return m_intensity * solidAngle;
}

float SpotLight::falloff(const Vec3& wi) const {
    float cosTheta = wi.dot(m_direction);
    
    if (cosTheta > m_cosInner) {
        return 1.0f; // Full intensity
    } else if (cosTheta > m_cosOuter) {
        // Smooth falloff
        float t = (cosTheta - m_cosOuter) / (m_cosInner - m_cosOuter);
        return t * t; // Quadratic falloff
    } else {
        return 0.0f; // Outside cone
    }
}

} // namespace photon
