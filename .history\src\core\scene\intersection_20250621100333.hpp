// src/core/scene/intersection.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Intersection data structure for ray-surface intersections

#pragma once

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"
#include <memory>

namespace photon {

// Forward declarations
class Material;
class Light;
class Primitive;

/**
 * @brief Ray-surface intersection information
 */
struct Intersection {
    // Hit information
    bool hit = false;           // Whether intersection occurred (for compatibility)

    // Geometric information
    Vec3 p;                     // Intersection point
    Vec3 n;                     // Surface normal (geometric)
    Vec3 ns;                    // Shading normal (may differ from geometric)
    
    // Parametric information
    float t = -1.0f;            // Ray parameter at intersection
    float u = 0.0f;             // Barycentric/parametric coordinate u
    float v = 0.0f;             // Barycentric/parametric coordinate v
    
    // Texture coordinates
    Vec2 uv;                    // UV texture coordinates
    bool hasUV = false;         // Whether UV coordinates are valid
    
    // Tangent space (for normal mapping)
    Vec3 dpdu;                  // Partial derivative dp/du
    Vec3 dpdv;                  // Partial derivative dp/dv
    bool hasTangents = false;   // Whether tangent vectors are valid
    
    // Material and lighting
    std::shared_ptr<Material> material;  // Surface material
    std::shared_ptr<Light> areaLight;    // Area light (if surface is emissive)
    
    // Primitive reference
    const Primitive* primitive = nullptr;  // Intersected primitive
    uint32_t primitiveID = 0;              // Primitive ID
    uint32_t triangleID = 0;               // Triangle ID (for meshes)
    
    // Ray information
    Vec3 wo;                    // Outgoing direction (towards camera)
    
    /**
     * @brief Default constructor
     */
    Intersection() = default;
    
    /**
     * @brief Constructor with basic information
     */
    Intersection(const Vec3& point, const Vec3& normal, float rayT)
        : p(point), n(normal), ns(normal), t(rayT) {}
    
    /**
     * @brief Check if intersection is valid
     */
    bool isValid() const {
        return t >= 0.0f && material != nullptr;
    }
    
    /**
     * @brief Check if surface is emissive (area light)
     */
    bool isEmissive() const {
        return areaLight != nullptr;
    }
    
    /**
     * @brief Get UV coordinates
     */
    Vec2 getUV() const {
        return hasUV ? uv : Vec2(u, v);
    }
    
    /**
     * @brief Get tangent vector
     */
    Vec3 getTangent() const {
        if (hasTangents) {
            return dpdu.normalized();
        }
        
        // Compute tangent from normal
        Vec3 tangent;
        if (std::abs(n.x) > 0.1f) {
            tangent = Vec3(0, 1, 0).cross(n);
        } else {
            tangent = Vec3(1, 0, 0).cross(n);
        }
        return tangent.normalized();
    }
    
    /**
     * @brief Get bitangent vector
     */
    Vec3 getBitangent() const {
        if (hasTangents) {
            return dpdv.normalized();
        }
        
        // Compute bitangent from normal and tangent
        return n.cross(getTangent()).normalized();
    }
    
    /**
     * @brief Set UV coordinates
     */
    void setUV(const Vec2& texCoords) {
        uv = texCoords;
        hasUV = true;
    }
    
    /**
     * @brief Set UV coordinates from parametric coordinates
     */
    void setUV(float texU, float texV) {
        uv = Vec2(texU, texV);
        hasUV = true;
    }
    
    /**
     * @brief Set tangent space vectors
     */
    void setTangents(const Vec3& tangentU, const Vec3& tangentV) {
        dpdu = tangentU;
        dpdv = tangentV;
        hasTangents = true;
    }
    
    /**
     * @brief Compute tangent space from UV derivatives
     */
    void computeTangents(const Vec3& dp1, const Vec3& dp2, 
                        const Vec2& duv1, const Vec2& duv2) {
        float det = duv1.x * duv2.y - duv1.y * duv2.x;
        
        if (std::abs(det) < 1e-8f) {
            // Degenerate case - use coordinate system from normal
            Vec3 tangent = getTangent();
            Vec3 bitangent = getBitangent();
            setTangents(tangent, bitangent);
            return;
        }
        
        float invDet = 1.0f / det;
        dpdu = (duv2.y * dp1 - duv1.y * dp2) * invDet;
        dpdv = (-duv2.x * dp1 + duv1.x * dp2) * invDet;
        hasTangents = true;
    }
    
    /**
     * @brief Transform direction from world to local tangent space
     */
    Vec3 worldToLocal(const Vec3& worldDir) const {
        Vec3 tangent = getTangent();
        Vec3 bitangent = getBitangent();
        
        return Vec3(
            worldDir.dot(tangent),
            worldDir.dot(bitangent),
            worldDir.dot(ns)
        );
    }
    
    /**
     * @brief Transform direction from local tangent space to world
     */
    Vec3 localToWorld(const Vec3& localDir) const {
        Vec3 tangent = getTangent();
        Vec3 bitangent = getBitangent();
        
        return tangent * localDir.x + 
               bitangent * localDir.y + 
               ns * localDir.z;
    }
    
    /**
     * @brief Apply normal mapping
     */
    void applyNormalMap(const Vec3& normalMapValue) {
        if (!hasTangents) {
            // Compute tangent space if not available
            Vec3 tangent = getTangent();
            Vec3 bitangent = getBitangent();
            setTangents(tangent, bitangent);
        }
        
        // Convert normal map from [0,1] to [-1,1]
        Vec3 localNormal = normalMapValue * 2.0f - Vec3(1.0f);
        
        // Transform to world space
        ns = localToWorld(localNormal).normalized();
    }
    
    /**
     * @brief Get emission color (if surface is emissive)
     */
    Color3 getEmission() const;
    
    /**
     * @brief Offset point along normal to avoid self-intersection
     */
    Vec3 offsetPoint(const Vec3& direction) const {
        // Use geometric normal for offset to avoid self-intersection
        float offset = 1e-4f;
        return p + n * (n.dot(direction) > 0 ? offset : -offset);
    }
};

/**
 * @brief Surface interaction for light transport
 */
struct SurfaceInteraction : public Intersection {
    // Additional data for light transport
    Vec3 wi;                    // Incident direction
    Color3 f;                   // BSDF value
    float pdf = 0.0f;           // Probability density function
    
    /**
     * @brief Constructor from intersection
     */
    SurfaceInteraction(const Intersection& isect) : Intersection(isect) {}
    
    /**
     * @brief Check if interaction is valid for light transport
     */
    bool isValidInteraction() const {
        return isValid() && pdf > 0.0f && f.isValid();
    }
};

} // namespace photon
