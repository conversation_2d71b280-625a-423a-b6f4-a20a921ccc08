// src/test_pattern_generators.cpp
// PhotonRender - Pattern Generators Test Suite
// Tests for geometric pattern generation system

#include <iostream>
#include <memory>
#include <chrono>
#include <fstream>
#include <iomanip>
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Test suite for Pattern Generators
 */
class PatternGeneratorTest {
public:
    /**
     * @brief Run all pattern generator tests
     */
    static bool runAllTests() {
        std::cout << "=== PhotonRender Pattern Generators Test Suite ===\n\n";
        
        int passed = 0;
        int total = 0;
        
        // Basic pattern tests
        if (testBasicPatterns()) passed++; total++;
        if (testPatternParameters()) passed++; total++;
        if (testPatternColors()) passed++; total++;
        if (testPatternTransformations()) passed++; total++;
        if (testAntiAliasing()) passed++; total++;
        if (testPerformance()) passed++; total++;
        if (testPatternCombinations()) passed++; total++;
        if (testEdgeCases()) passed++; total++;
        
        std::cout << "\n=== Test Results ===\n";
        std::cout << "Passed: " << passed << "/" << total << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Pattern Generators working perfectly!\n";
            return true;
        } else {
            std::cout << "❌ Some tests failed. Check implementation.\n";
            return false;
        }
    }

private:
    /**
     * @brief Test basic pattern generation
     */
    static bool testBasicPatterns() {
        std::cout << "Testing basic pattern generation... ";
        
        try {
            // Test all pattern types
            PatternTexture checkerboard(PatternType::CHECKERBOARD, 4.0f);
            PatternTexture stripes(PatternType::STRIPES, 8.0f);
            PatternTexture dots(PatternType::DOTS, 6.0f);
            PatternTexture grid(PatternType::GRID, 10.0f);
            
            // Test sampling at various coordinates
            Vec2 testCoords[] = {
                Vec2(0.0f, 0.0f),
                Vec2(0.5f, 0.5f),
                Vec2(0.25f, 0.75f),
                Vec2(1.0f, 1.0f)
            };
            
            for (const auto& coord : testCoords) {
                // Test checkerboard
                Color3 checkerColor = checkerboard.sample(coord);
                float checkerValue = checkerboard.sampleFloat(coord);
                
                // Test stripes
                Color3 stripeColor = stripes.sample(coord);
                float stripeValue = stripes.sampleFloat(coord);
                
                // Test dots
                Color3 dotColor = dots.sample(coord);
                float dotValue = dots.sampleFloat(coord);
                
                // Test grid
                Color3 gridColor = grid.sample(coord);
                float gridValue = grid.sampleFloat(coord);
                
                // Validate ranges
                if (checkerValue < 0.0f || checkerValue > 1.0f ||
                    stripeValue < 0.0f || stripeValue > 1.0f ||
                    dotValue < 0.0f || dotValue > 1.0f ||
                    gridValue < 0.0f || gridValue > 1.0f) {
                    std::cout << "[FAIL] - Values out of range [0,1]\n";
                    return false;
                }
                
                // Validate color consistency
                if (std::abs(checkerColor.luminance() - checkerValue) > 0.01f) {
                    std::cout << "[FAIL] - Color/float sampling inconsistent\n";
                    return false;
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test pattern parameters
     */
    static bool testPatternParameters() {
        std::cout << "Testing pattern parameters... ";
        
        try {
            // Test parameter effects
            PatternParams params = PatternParams::defaultParams();
            params.scale = 2.0f;
            params.rotation = 0.785f; // 45 degrees
            params.offset = Vec2(0.1f, 0.2f);
            params.lineWidth = 0.05f;
            params.dotSize = 0.4f;
            
            PatternTexture pattern(PatternType::CHECKERBOARD, params);
            
            // Test parameter setting
            pattern.setPatternParams(params);
            
            // Test different pattern types with same parameters
            pattern.setPatternType(PatternType::STRIPES);
            pattern.setPatternType(PatternType::DOTS);
            pattern.setPatternType(PatternType::GRID);
            
            // Sample to ensure no crashes
            Vec2 testUV(0.5f, 0.5f);
            float value = pattern.sampleFloat(testUV);
            
            if (value < 0.0f || value > 1.0f) {
                std::cout << "[FAIL] - Parameter test value out of range\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test pattern colors
     */
    static bool testPatternColors() {
        std::cout << "Testing pattern colors... ";
        
        try {
            Color3 red(1.0f, 0.0f, 0.0f);
            Color3 blue(0.0f, 0.0f, 1.0f);
            
            PatternTexture pattern(PatternType::CHECKERBOARD, 4.0f, red, blue);
            
            // Test color setting
            Color3 green(0.0f, 1.0f, 0.0f);
            Color3 yellow(1.0f, 1.0f, 0.0f);
            pattern.setColors(green, yellow);
            
            // Sample and check colors
            Vec2 testUV(0.1f, 0.1f);
            Color3 sampledColor = pattern.sample(testUV);
            
            // Color should be either green, yellow, or interpolation
            bool validColor = (sampledColor.r >= 0.0f && sampledColor.r <= 1.0f &&
                              sampledColor.g >= 0.0f && sampledColor.g <= 1.0f &&
                              sampledColor.b >= 0.0f && sampledColor.b <= 1.0f);
            
            if (!validColor) {
                std::cout << "[FAIL] - Invalid color values\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test pattern transformations
     */
    static bool testPatternTransformations() {
        std::cout << "Testing pattern transformations... ";
        
        try {
            PatternParams params = PatternParams::defaultParams();
            
            // Test rotation
            params.rotation = 1.57f; // 90 degrees
            PatternTexture rotatedPattern(PatternType::STRIPES, params);
            
            // Test offset
            params.rotation = 0.0f;
            params.offset = Vec2(0.5f, 0.5f);
            PatternTexture offsetPattern(PatternType::CHECKERBOARD, params);
            
            // Test scale
            params.offset = Vec2(0.0f);
            params.scale = 0.5f;
            PatternTexture scaledPattern(PatternType::DOTS, params);
            
            // Sample all patterns
            Vec2 testUV(0.5f, 0.5f);
            
            float rotatedValue = rotatedPattern.sampleFloat(testUV);
            float offsetValue = offsetPattern.sampleFloat(testUV);
            float scaledValue = scaledPattern.sampleFloat(testUV);
            
            // All values should be valid
            if (rotatedValue < 0.0f || rotatedValue > 1.0f ||
                offsetValue < 0.0f || offsetValue > 1.0f ||
                scaledValue < 0.0f || scaledValue > 1.0f) {
                std::cout << "[FAIL] - Transformation values out of range\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test anti-aliasing
     */
    static bool testAntiAliasing() {
        std::cout << "Testing anti-aliasing... ";
        
        try {
            PatternParams aliasedParams = PatternParams::defaultParams();
            aliasedParams.antiAlias = false;
            
            PatternParams smoothParams = PatternParams::defaultParams();
            smoothParams.antiAlias = true;
            
            PatternTexture aliasedPattern(PatternType::CHECKERBOARD, aliasedParams);
            PatternTexture smoothPattern(PatternType::CHECKERBOARD, smoothParams);
            
            // Sample near pattern edges where anti-aliasing should make a difference
            Vec2 edgeUV(0.499f, 0.499f); // Near checkerboard edge
            
            float aliasedValue = aliasedPattern.sampleFloat(edgeUV);
            float smoothValue = smoothPattern.sampleFloat(edgeUV);
            
            // Both should be valid
            if (aliasedValue < 0.0f || aliasedValue > 1.0f ||
                smoothValue < 0.0f || smoothValue > 1.0f) {
                std::cout << "[FAIL] - Anti-aliasing values out of range\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test performance
     */
    static bool testPerformance() {
        std::cout << "Testing performance... ";
        
        try {
            PatternTexture pattern(PatternType::CHECKERBOARD, 8.0f);
            
            const int numSamples = 10000;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            float totalValue = 0.0f;
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                totalValue += pattern.sampleFloat(Vec2(u, v));
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            
            float avgTimeNs = float(duration.count()) / numSamples;
            
            // Performance target: < 1000ns per sample
            if (avgTimeNs > 1000.0f) {
                std::cout << "[FAIL] - Performance too slow: " << avgTimeNs << "ns per sample\n";
                return false;
            }
            
            std::cout << "[PASS] (" << std::fixed << std::setprecision(2) << avgTimeNs << "ns per sample)\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test pattern combinations
     */
    static bool testPatternCombinations() {
        std::cout << "Testing pattern combinations... ";
        
        try {
            // Test all pattern types with various parameters
            PatternType types[] = {
                PatternType::CHECKERBOARD,
                PatternType::STRIPES,
                PatternType::DOTS,
                PatternType::GRID
            };
            
            for (auto type : types) {
                PatternParams params = PatternParams::defaultParams();
                params.scale = 4.0f + static_cast<int>(type);
                params.rotation = static_cast<int>(type) * 0.785f;
                
                PatternTexture pattern(type, params);
                
                // Sample multiple coordinates
                for (int i = 0; i < 10; ++i) {
                    Vec2 uv(i * 0.1f, (10 - i) * 0.1f);
                    float value = pattern.sampleFloat(uv);
                    
                    if (value < 0.0f || value > 1.0f) {
                        std::cout << "[FAIL] - Combination test value out of range\n";
                        return false;
                    }
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test edge cases
     */
    static bool testEdgeCases() {
        std::cout << "Testing edge cases... ";
        
        try {
            PatternTexture pattern(PatternType::CHECKERBOARD, 1.0f);
            
            // Test extreme coordinates
            Vec2 extremeCoords[] = {
                Vec2(-1000.0f, -1000.0f),
                Vec2(1000.0f, 1000.0f),
                Vec2(0.0f, 1000.0f),
                Vec2(1000.0f, 0.0f)
            };
            
            for (const auto& coord : extremeCoords) {
                float value = pattern.sampleFloat(coord);
                
                if (value < 0.0f || value > 1.0f || std::isnan(value) || std::isinf(value)) {
                    std::cout << "[FAIL] - Edge case value invalid: " << value << "\n";
                    return false;
                }
            }
            
            // Test very small scale
            PatternParams params = PatternParams::defaultParams();
            params.scale = 0.001f;
            pattern.setPatternParams(params);
            
            float smallScaleValue = pattern.sampleFloat(Vec2(0.5f, 0.5f));
            if (smallScaleValue < 0.0f || smallScaleValue > 1.0f) {
                std::cout << "[FAIL] - Small scale value out of range\n";
                return false;
            }
            
            // Test very large scale
            params.scale = 1000.0f;
            pattern.setPatternParams(params);
            
            float largeScaleValue = pattern.sampleFloat(Vec2(0.5f, 0.5f));
            if (largeScaleValue < 0.0f || largeScaleValue > 1.0f) {
                std::cout << "[FAIL] - Large scale value out of range\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    bool success = PatternGeneratorTest::runAllTests();
    return success ? 0 : 1;
}
