// src/test_advanced_lights.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Advanced Lights Test Suite - Spot Lights, IES Profiles, Photometric Lights

#include "core/light/spot_light.hpp"
#include "core/light/ies_profile.hpp"
#include "core/light/photometric_light.hpp"
#include "core/scene/scene.hpp"
#include "core/scene/intersection.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/math/vec3.hpp"
#include <iostream>
#include <chrono>
#include <cassert>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

using namespace photon;

/**
 * @brief Test result structure
 */
struct TestResult {
    std::string testName;
    bool passed = false;
    double executionTime = 0.0;
    std::string errorMessage;
    
    TestResult(const std::string& name) : testName(name) {}
};

/**
 * @brief Test timer utility
 */
class TestTimer {
public:
    TestTimer() : start(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed() const {
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        return duration.count() / 1000.0; // Return milliseconds
    }
    
private:
    std::chrono::high_resolution_clock::time_point start;
};

/**
 * @brief Test Spot Light basic functionality
 */
TestResult testSpotLightBasic() {
    TestResult result("Spot Light Basic Functionality");
    TestTimer timer;
    
    try {
        // Create spot light
        Vec3 position(0, 0, 0);
        Vec3 direction(0, 0, -1);
        Color3 intensity(1000.0f);
        float innerAngle = M_PI / 6.0f;  // 30 degrees
        float outerAngle = M_PI / 4.0f;  // 45 degrees
        
        SpotLight light(position, direction, intensity, innerAngle, outerAngle);
        
        // Test basic properties
        assert(light.getPosition() == position);
        assert(light.getDirection() == direction);
        assert(light.getIntensity() == intensity);
        assert(std::abs(light.getInnerAngle() - innerAngle) < 1e-6f);
        assert(std::abs(light.getOuterAngle() - outerAngle) < 1e-6f);
        assert(light.isDelta() == true);
        assert(light.getName() == "SpotLight");
        
        // Test power calculation
        Color3 power = light.power();
        assert(power.x > 0.0f && power.y > 0.0f && power.z > 0.0f);
        
        result.passed = true;
        
    } catch (const std::exception& e) {
        result.errorMessage = e.what();
    } catch (...) {
        result.errorMessage = "Unknown exception";
    }
    
    result.executionTime = timer.elapsed();
    return result;
}

/**
 * @brief Test Spot Light sampling and evaluation
 */
TestResult testSpotLightSampling() {
    TestResult result("Spot Light Sampling and Evaluation");
    TestTimer timer;
    
    try {
        // Create spot light
        SpotLight light(Vec3(0, 5, 0), Vec3(0, -1, 0), Color3(1000.0f), 
                       M_PI / 6.0f, M_PI / 4.0f);
        
        // Create intersection point
        Intersection isect;
        isect.p = Vec3(0, 0, 0);
        isect.n = Vec3(0, 1, 0);
        
        // Create sampler
        RandomSampler sampler;
        
        // Test sampling
        LightSample sample = light.sample(isect, sampler);
        assert(sample.isValid());
        assert(sample.isDelta == true);
        assert(sample.distance > 0.0f);
        assert(!sample.Li.isZero());
        
        // Test Li evaluation
        Color3 Li = light.Li(isect, sample.wi);
        assert(!Li.isZero());
        
        // Test PDF (should be 0 for delta lights)
        float pdf = light.pdf(isect, sample.wi);
        assert(pdf == 0.0f);
        
        // Test outside cone (should return zero)
        Intersection outsideIsect;
        outsideIsect.p = Vec3(10, 0, 0); // Far to the side
        outsideIsect.n = Vec3(0, 1, 0);
        
        LightSample outsideSample = light.sample(outsideIsect, sampler);
        // Should be invalid or zero contribution
        
        result.passed = true;
        
    } catch (const std::exception& e) {
        result.errorMessage = e.what();
    } catch (...) {
        result.errorMessage = "Unknown exception";
    }
    
    result.executionTime = timer.elapsed();
    return result;
}

/**
 * @brief Test Spot Light falloff patterns
 */
TestResult testSpotLightFalloff() {
    TestResult result("Spot Light Falloff Patterns");
    TestTimer timer;
    
    try {
        Vec3 position(0, 0, 0);
        Vec3 direction(0, 0, -1);
        Color3 intensity(1000.0f);
        float innerAngle = M_PI / 6.0f;
        float outerAngle = M_PI / 4.0f;
        
        // Test different falloff patterns
        std::vector<SpotLightFalloff> patterns = {
            SpotLightFalloff::LINEAR,
            SpotLightFalloff::QUADRATIC,
            SpotLightFalloff::CUBIC,
            SpotLightFalloff::SMOOTH_STEP
        };
        
        for (auto pattern : patterns) {
            SpotLight light(position, direction, intensity, innerAngle, outerAngle, pattern);
            
            // Test center (should be full intensity)
            Intersection centerIsect;
            centerIsect.p = Vec3(0, 0, -1);
            centerIsect.n = Vec3(0, 0, 1);
            
            RandomSampler sampler;
            LightSample centerSample = light.sample(centerIsect, sampler);
            assert(centerSample.isValid());
            
            // Test edge (should have reduced intensity)
            float edgeAngle = (innerAngle + outerAngle) * 0.5f;
            Vec3 edgeDir = Vec3(std::sin(edgeAngle), 0, -std::cos(edgeAngle));
            
            Intersection edgeIsect;
            edgeIsect.p = edgeDir;
            edgeIsect.n = -edgeDir;
            
            LightSample edgeSample = light.sample(edgeIsect, sampler);
            // Edge should have less intensity than center
            if (edgeSample.isValid()) {
                // This is expected behavior
            }
        }
        
        result.passed = true;
        
    } catch (const std::exception& e) {
        result.errorMessage = e.what();
    } catch (...) {
        result.errorMessage = "Unknown exception";
    }
    
    result.executionTime = timer.elapsed();
    return result;
}

/**
 * @brief Test IES Profile creation and evaluation
 */
TestResult testIESProfile() {
    TestResult result("IES Profile System");
    TestTimer timer;
    
    try {
        // Test cone profile creation
        auto coneProfile = IESProfile::createConeProfile(M_PI / 6.0f, M_PI / 3.0f);
        assert(coneProfile != nullptr);
        assert(coneProfile->isValid());
        
        // Test uniform profile creation
        auto uniformProfile = IESProfile::createUniformProfile();
        assert(uniformProfile != nullptr);
        assert(uniformProfile->isValid());
        
        // Test evaluation
        Vec3 centerDir(0, 0, -1);
        float centerIntensity = coneProfile->evaluate(centerDir);
        assert(centerIntensity > 0.0f);
        
        Vec3 sideDir(1, 0, 0);
        float sideIntensity = coneProfile->evaluate(sideDir);
        // Side intensity should be less than center for cone profile
        
        // Test statistics
        auto stats = coneProfile->getStatistics();
        assert(stats.maxIntensity > 0.0f);
        assert(stats.beamAngle > 0.0f);
        assert(stats.fieldAngle > 0.0f);
        
        result.passed = true;
        
    } catch (const std::exception& e) {
        result.errorMessage = e.what();
    } catch (...) {
        result.errorMessage = "Unknown exception";
    }
    
    result.executionTime = timer.elapsed();
    return result;
}

/**
 * @brief Test Photometric Light functionality
 */
TestResult testPhotometricLight() {
    TestResult result("Photometric Light System");
    TestTimer timer;
    
    try {
        // Create photometric light
        Vec3 position(0, 0, 0);
        Vec3 direction(0, 0, -1);
        Color3 intensity(1.0f);
        
        PhotometricLight light(position, direction, intensity, PhotometricLightType::POINT);
        
        // Test basic properties
        assert(light.getPosition() == position);
        assert(light.getDirection() == direction);
        assert(light.getIntensity() == intensity);
        assert(light.getType() == PhotometricLightType::POINT);
        assert(light.isEnabled() == true);
        
        // Set IES profile
        auto iesProfile = IESProfile::createConeProfile(M_PI / 4.0f, M_PI / 2.0f);
        light.setIESProfile(iesProfile);
        assert(light.getIESProfile() != nullptr);
        
        // Test sampling
        Intersection isect;
        isect.p = Vec3(0, 0, -1);
        isect.n = Vec3(0, 0, 1);
        
        RandomSampler sampler;
        LightSample sample = light.sample(isect, sampler);
        assert(sample.isValid());
        
        // Test Li evaluation
        Color3 Li = light.Li(isect, sample.wi);
        assert(!Li.isZero());
        
        // Test power calculation
        Color3 power = light.power();
        assert(!power.isZero());
        
        // Test statistics
        auto stats = light.getStatistics();
        assert(stats.hasIESProfile == true);
        assert(stats.totalLumens > 0.0f);
        
        result.passed = true;
        
    } catch (const std::exception& e) {
        result.errorMessage = e.what();
    } catch (...) {
        result.errorMessage = "Unknown exception";
    }
    
    result.executionTime = timer.elapsed();
    return result;
}

/**
 * @brief Test Photometric Light utility functions
 */
TestResult testPhotometricLightUtils() {
    TestResult result("Photometric Light Utilities");
    TestTimer timer;
    
    try {
        // Test preset creation
        auto downlight = PhotometricLightUtils::createPreset("downlight", Vec3(0, 5, 0));
        assert(downlight != nullptr);
        assert(downlight->getType() == PhotometricLightType::SPOT);
        
        auto floodlight = PhotometricLightUtils::createPreset("floodlight", Vec3(0, 5, 0));
        assert(floodlight != nullptr);
        assert(floodlight->getType() == PhotometricLightType::SPOT);
        
        auto uniform = PhotometricLightUtils::createPreset("uniform", Vec3(0, 5, 0));
        assert(uniform != nullptr);
        assert(uniform->getType() == PhotometricLightType::POINT);
        
        // Test utility functions
        float candela = PhotometricLightUtils::lumensToCandelaPerSteradian(1000.0f, M_PI);
        assert(candela > 0.0f);
        
        result.passed = true;
        
    } catch (const std::exception& e) {
        result.errorMessage = e.what();
    } catch (...) {
        result.errorMessage = "Unknown exception";
    }
    
    result.executionTime = timer.elapsed();
    return result;
}

/**
 * @brief Run all tests and print results
 */
int main() {
    std::cout << "=== PhotonRender Advanced Lights Test Suite ===" << std::endl;
    std::cout << "Testing Spot Lights, IES Profiles, and Photometric Lights" << std::endl;
    std::cout << std::endl;
    
    std::vector<TestResult> results;
    
    // Run all tests
    results.push_back(testSpotLightBasic());
    results.push_back(testSpotLightSampling());
    results.push_back(testSpotLightFalloff());
    results.push_back(testIESProfile());
    results.push_back(testPhotometricLight());
    results.push_back(testPhotometricLightUtils());
    
    // Print results
    int passed = 0;
    double totalTime = 0.0;
    
    for (const auto& result : results) {
        std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                  << result.testName << " (" << result.executionTime << " ms)";
        
        if (!result.passed && !result.errorMessage.empty()) {
            std::cout << " - " << result.errorMessage;
        }
        
        std::cout << std::endl;
        
        if (result.passed) passed++;
        totalTime += result.executionTime;
    }
    
    std::cout << std::endl;
    std::cout << "=== Test Summary ===" << std::endl;
    std::cout << "Passed: " << passed << "/" << results.size() << std::endl;
    std::cout << "Total Time: " << totalTime << " ms" << std::endl;
    std::cout << "Success Rate: " << (100.0 * passed / results.size()) << "%" << std::endl;
    
    if (passed == results.size()) {
        std::cout << std::endl;
        std::cout << "🎉 ALL TESTS PASSED! Advanced Lights system is working correctly." << std::endl;
        return 0;
    } else {
        std::cout << std::endl;
        std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        return 1;
    }
}
