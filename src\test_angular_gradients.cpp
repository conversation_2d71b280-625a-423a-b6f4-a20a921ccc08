// src/test_angular_gradients.cpp - Test suite for angular gradients
// PhotonRender Angular Gradients Test Suite

#include <iostream>
#include <iomanip>
#include <chrono>
#include <cmath>
#include "core/texture/texture.hpp"

using namespace photon;

/**
 * @brief Test suite for angular gradients
 */
class AngularGradientTest {
public:
    /**
     * @brief Run all angular gradient tests
     */
    static bool runAllTests() {
        std::cout << "=== PhotonRender Angular Gradients Test Suite ===\n\n";
        
        int passed = 0;
        int total = 0;
        
        // Basic gradient tests
        if (testBasicAngularGradient()) passed++; total++;
        if (testGradientParameters()) passed++; total++;
        if (testColorStops()) passed++; total++;
        if (testCenterPoint()) passed++; total++;
        if (testAngleControl()) passed++; total++;
        if (testRepeatMode()) passed++; total++;
        if (testPerformance()) passed++; total++;
        if (testEdgeCases()) passed++; total++;
        
        std::cout << "\n=== Test Results ===\n";
        std::cout << "Passed: " << passed << "/" << total << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Angular Gradients working perfectly!\n";
            return true;
        } else {
            std::cout << "❌ Some tests failed. Check implementation.\n";
            return false;
        }
    }

private:
    /**
     * @brief Test basic angular gradient generation
     */
    static bool testBasicAngularGradient() {
        std::cout << "Testing basic angular gradient... ";
        
        try {
            // Create basic angular gradient (red to blue)
            Color3 red(1.0f, 0.0f, 0.0f);
            Color3 blue(0.0f, 0.0f, 1.0f);
            GradientTexture gradient(GradientType::ANGULAR, red, blue);
            
            // Test different angles
            Color3 rightColor = gradient.sample(Vec2(1.0f, 0.5f)); // 0 degrees
            Color3 topColor = gradient.sample(Vec2(0.5f, 1.0f));   // 90 degrees
            Color3 leftColor = gradient.sample(Vec2(0.0f, 0.5f));  // 180 degrees
            Color3 bottomColor = gradient.sample(Vec2(0.5f, 0.0f)); // 270 degrees
            
            // Colors should be different due to angular variation
            if (std::abs(rightColor.r - leftColor.r) < 0.1f) {
                std::cout << "[FAIL] - Angular variation not working\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test gradient parameters
     */
    static bool testGradientParameters() {
        std::cout << "Testing gradient parameters... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.center = Vec2(0.3f, 0.7f);
            params.angle = 1.57f; // 90 degrees rotation
            
            std::vector<ColorStop> colorStops = {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            };
            
            GradientTexture gradient(GradientType::ANGULAR, params, colorStops);
            
            // Test that gradient works with custom parameters
            Color3 testColor = gradient.sample(Vec2(0.5f, 0.5f));
            if (std::isnan(testColor.r) || std::isnan(testColor.g) || std::isnan(testColor.b)) {
                std::cout << "[FAIL] - Custom parameters produce NaN\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test color stops
     */
    static bool testColorStops() {
        std::cout << "Testing color stops... ";
        
        try {
            GradientTexture gradient(GradientType::ANGULAR);
            gradient.clearColorStops();
            gradient.addColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)); // Red
            gradient.addColorStop(0.5f, Color3(0.0f, 1.0f, 0.0f)); // Green
            gradient.addColorStop(1.0f, Color3(0.0f, 0.0f, 1.0f)); // Blue
            
            // Test different angles
            Color3 color1 = gradient.sample(Vec2(1.0f, 0.5f));
            Color3 color2 = gradient.sample(Vec2(0.5f, 1.0f));
            
            // Colors should be valid
            if (std::isnan(color1.r) || std::isnan(color2.r)) {
                std::cout << "[FAIL] - Color stops produce NaN\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test center point control
     */
    static bool testCenterPoint() {
        std::cout << "Testing center point... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.center = Vec2(0.2f, 0.8f);
            
            GradientTexture gradient(GradientType::ANGULAR, params, {
                ColorStop(0.0f, Color3(1.0f, 1.0f, 1.0f)),
                ColorStop(1.0f, Color3(0.0f, 0.0f, 0.0f))
            });
            
            // Test that gradient works with custom center
            Color3 testColor = gradient.sample(Vec2(0.5f, 0.5f));
            if (std::isnan(testColor.r) || std::isnan(testColor.g) || std::isnan(testColor.b)) {
                std::cout << "[FAIL] - Custom center produces NaN\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test angle control
     */
    static bool testAngleControl() {
        std::cout << "Testing angle control... ";
        
        try {
            GradientParams params1 = GradientParams::defaultParams();
            params1.angle = 0.0f; // No rotation
            
            GradientParams params2 = GradientParams::defaultParams();
            params2.angle = 1.57f; // 90 degrees rotation
            
            GradientTexture gradient1(GradientType::ANGULAR, params1, {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            });
            
            GradientTexture gradient2(GradientType::ANGULAR, params2, {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            });
            
            // Test same point with different rotations
            Color3 color1 = gradient1.sample(Vec2(1.0f, 0.5f));
            Color3 color2 = gradient2.sample(Vec2(1.0f, 0.5f));
            
            // Colors should be different due to rotation
            if (std::abs(color1.r - color2.r) < 0.1f && std::abs(color1.g - color2.g) < 0.1f) {
                std::cout << "[FAIL] - Angle rotation not working\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test repeat mode
     */
    static bool testRepeatMode() {
        std::cout << "Testing repeat mode... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.repeat = true;
            params.spread = 0.5f;
            
            GradientTexture gradient(GradientType::ANGULAR, params, {
                ColorStop(0.0f, Color3(0.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(1.0f, 1.0f, 1.0f))
            });
            
            // Test repeat pattern
            float value1 = gradient.sampleFloat(Vec2(1.0f, 0.5f));
            float value2 = gradient.sampleFloat(Vec2(0.0f, 0.5f));
            
            // With repeat, opposite angles should show pattern repetition
            if (std::abs(value1 - value2) > 0.5f) {
                std::cout << "[FAIL] - Repeat mode not working\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test performance
     */
    static bool testPerformance() {
        std::cout << "Testing performance... ";
        
        try {
            GradientTexture gradient(GradientType::ANGULAR, Color3(0.0f), Color3(1.0f));
            
            const int numSamples = 10000;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            float totalValue = 0.0f;
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                totalValue += gradient.sampleFloat(Vec2(u, v));
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            
            float avgTimeNs = float(duration.count()) / numSamples;
            
            // Performance target: < 1000ns per sample
            if (avgTimeNs > 1000.0f) {
                std::cout << "[FAIL] - Performance too slow: " << avgTimeNs << "ns per sample\n";
                return false;
            }
            
            std::cout << "[PASS] (" << std::fixed << std::setprecision(2) << avgTimeNs << "ns per sample)\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test edge cases
     */
    static bool testEdgeCases() {
        std::cout << "Testing edge cases... ";
        
        try {
            // Test with extreme angle values
            GradientParams extremeParams = GradientParams::defaultParams();
            extremeParams.angle = 100.0f; // Very large angle
            
            GradientTexture extremeGradient(GradientType::ANGULAR, extremeParams, {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            });
            
            Color3 extremeColor = extremeGradient.sample(Vec2(0.5f, 0.5f));
            if (std::isnan(extremeColor.r) || std::isnan(extremeColor.g) || std::isnan(extremeColor.b)) {
                std::cout << "[FAIL] - Extreme angle produces NaN\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    bool success = AngularGradientTest::runAllTests();
    return success ? 0 : 1;
}
