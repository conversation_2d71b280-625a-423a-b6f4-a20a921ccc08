// src/core/light/sphere_light.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Sphere Area Light implementation

#include "sphere_light.hpp"
#include "../sampler/sampler.hpp"
#include "../math/ray.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

namespace photon {

SphereLight::SphereLight(const Vec3& center, float radius,
                         const Color3& emission, float intensity)
    : AreaLightBase(emission, intensity, true), // Spheres are always two-sided
      m_center(center), m_radius(std::max(0.0f, radius)) {
    updateCachedValues();
}

void SphereLight::sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const {
    Vec2 u = sampler.next2D();
    
    if (m_samplingMode == AreaLightSampling::UNIFORM) {
        // Uniform sphere sampling
        point = sampleUniform(u);
    } else {
        // Uniform sampling fallback
        point = sampleUniform(u);
    }
    
    normal = getNormalAtPoint(point);
    pdf = 1.0f / m_area;
}

float SphereLight::getArea() const {
    return m_area;
}

bool SphereLight::intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const {
    float t0, t1;
    if (!intersectSphere(ray, t0, t1)) {
        return false;
    }
    
    // Choose the closest positive intersection
    if (t0 > 0.0f) {
        t = t0;
    } else if (t1 > 0.0f) {
        t = t1;
    } else {
        return false; // Both intersections behind ray origin
    }
    
    point = ray.o + t * ray.d;
    normal = getNormalAtPoint(point);
    
    return true;
}

void SphereLight::setGeometry(const Vec3& center, float radius) {
    m_center = center;
    m_radius = std::max(0.0f, radius);
    updateCachedValues();
}

Vec3 SphereLight::sampleUniform(const Vec2& u) const {
    Vec3 unitPoint = sampleUniformSphereSpherical(u);
    return m_center + m_radius * unitPoint;
}

std::pair<Vec3, float> SphereLight::sampleVisible(const Vec2& u, const Vec3& fromPoint) const {
    Vec3 toCenter = m_center - fromPoint;
    float distanceToCenter = toCenter.length();
    
    if (distanceToCenter <= m_radius) {
        // Point is inside sphere - sample uniformly
        Vec3 point = sampleUniform(u);
        float pdf = 1.0f / m_area;
        return std::make_pair(point, pdf);
    }
    
    // Point is outside sphere - sample visible hemisphere
    float sinThetaMax = m_radius / distanceToCenter;
    float cosThetaMax = std::sqrt(std::max(0.0f, 1.0f - sinThetaMax * sinThetaMax));
    
    // Sample cone towards sphere
    float cosTheta = (1.0f - u.x) + u.x * cosThetaMax;
    float sinTheta = std::sqrt(std::max(0.0f, 1.0f - cosTheta * cosTheta));
    float phi = u.y * 2.0f * M_PI;
    
    // Create coordinate system
    Vec3 w = toCenter.normalized();
    Vec3 u_vec, v_vec;
    if (std::abs(w.x) > 0.1f) {
        u_vec = Vec3(0, 1, 0).cross(w).normalized();
    } else {
        u_vec = Vec3(1, 0, 0).cross(w).normalized();
    }
    v_vec = w.cross(u_vec);
    
    // Sample direction
    Vec3 direction = sinTheta * std::cos(phi) * u_vec + 
                    sinTheta * std::sin(phi) * v_vec + 
                    cosTheta * w;
    
    // Find intersection with sphere
    Ray ray(fromPoint, direction);
    float t0, t1;
    if (!intersectSphere(ray, t0, t1)) {
        // Fallback to uniform sampling
        Vec3 point = sampleUniform(u);
        float pdf = 1.0f / m_area;
        return std::make_pair(point, pdf);
    }
    
    float t = (t0 > 0.0f) ? t0 : t1;
    Vec3 point = ray.origin + t * ray.direction;
    
    // Compute PDF
    float solidAngle = 2.0f * M_PI * (1.0f - cosThetaMax);
    float pdf = 1.0f / solidAngle;
    
    return std::make_pair(point, pdf);
}

float SphereLight::getSolidAngle(const Vec3& point) const {
    float distanceToCenter = (point - m_center).length();
    
    if (distanceToCenter <= m_radius) {
        return 4.0f * M_PI; // Point is inside sphere
    }
    
    float sinThetaMax = m_radius / distanceToCenter;
    float cosThetaMax = std::sqrt(std::max(0.0f, 1.0f - sinThetaMax * sinThetaMax));
    
    return 2.0f * M_PI * (1.0f - cosThetaMax);
}

float SphereLight::getDistanceToSurface(const Vec3& point) const {
    float distanceToCenter = (point - m_center).length();
    return distanceToCenter - m_radius;
}

bool SphereLight::isInsideSphere(const Vec3& point) const {
    float distanceToCenter = (point - m_center).length();
    return distanceToCenter < m_radius;
}

Vec3 SphereLight::getNormalAtPoint(const Vec3& point) const {
    return (point - m_center).normalized();
}

Vec2 SphereLight::directionToSpherical(const Vec3& direction) const {
    float theta = std::acos(std::clamp(direction.y, -1.0f, 1.0f));
    float phi = std::atan2(direction.z, direction.x);
    if (phi < 0.0f) phi += 2.0f * M_PI;
    
    return Vec2(theta, phi);
}

Vec3 SphereLight::sphericalToDirection(const Vec2& spherical) const {
    float theta = spherical.x;
    float phi = spherical.y;
    
    float sinTheta = std::sin(theta);
    float cosTheta = std::cos(theta);
    float sinPhi = std::sin(phi);
    float cosPhi = std::cos(phi);
    
    return Vec3(sinTheta * cosPhi, cosTheta, sinTheta * sinPhi);
}

void SphereLight::updateCachedValues() {
    m_area = 4.0f * M_PI * m_radius * m_radius;
}

Vec3 SphereLight::sampleUniformSphereRejection(const Vec2& u) const {
    // Rejection sampling (less efficient)
    Vec3 point;
    do {
        point = Vec3(2.0f * u.x - 1.0f, 2.0f * u.y - 1.0f, 2.0f * u.x - 1.0f);
    } while (point.lengthSquared() > 1.0f);
    
    return point.normalized();
}

Vec3 SphereLight::sampleUniformSphereSpherical(const Vec2& u) const {
    float z = 1.0f - 2.0f * u.x;
    float r = std::sqrt(std::max(0.0f, 1.0f - z * z));
    float phi = 2.0f * M_PI * u.y;
    
    return Vec3(r * std::cos(phi), z, r * std::sin(phi));
}

bool SphereLight::intersectSphere(const Ray& ray, float& t0, float& t1) const {
    Vec3 oc = ray.origin - m_center;
    float a = ray.direction.dot(ray.direction);
    float b = 2.0f * oc.dot(ray.direction);
    float c = oc.dot(oc) - m_radius * m_radius;
    
    float discriminant = b * b - 4.0f * a * c;
    if (discriminant < 0.0f) {
        return false; // No intersection
    }
    
    float sqrtDiscriminant = std::sqrt(discriminant);
    t0 = (-b - sqrtDiscriminant) / (2.0f * a);
    t1 = (-b + sqrtDiscriminant) / (2.0f * a);
    
    return true;
}

// Factory functions
namespace SphereLightFactory {

std::shared_ptr<SphereLight> create(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    return std::make_shared<SphereLight>(center, radius, emission, intensity);
}

std::shared_ptr<SphereLight> createBulb(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    return create(center, radius, emission, intensity);
}

std::shared_ptr<SphereLight> createSun(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    // Sun typically has high intensity
    return create(center, radius, emission, intensity * 100.0f);
}

std::shared_ptr<SphereLight> createMoon(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    // Moon typically has lower intensity and cooler color
    Color3 moonEmission = emission * Color3(0.8f, 0.9f, 1.0f);
    return create(center, radius, moonEmission, intensity * 0.1f);
}

std::shared_ptr<SphereLight> createPendant(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    return create(center, radius, emission, intensity);
}

} // namespace SphereLightFactory

} // namespace photon
