// src/test_area_lights.cpp
// PhotonRender - Test for Area Lights implementation
// Phase 3.2.2 Task 2 validation

#include "core/light/area_light_base.hpp"
#include "core/light/rectangle_light.hpp"
#include "core/light/disk_light.hpp"
#include "core/light/sphere_light.hpp"
#include "core/scene/intersection.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/math/vec3.hpp"
#include "core/math/color3.hpp"
#include "core/math/ray.hpp"

#include <iostream>
#include <vector>
#include <chrono>
#include <memory>

using namespace photon;

/**
 * @brief Area Lights Test Suite
 */
class AreaLightsTest {
public:
    /**
     * @brief Run complete test suite
     */
    bool runTests() {
        std::cout << "\n💡 Area Lights Test Suite" << std::endl;
        std::cout << "Phase 3.2.2 Task 2 Validation" << std::endl;
        std::cout << "=============================" << std::endl;
        
        bool allPassed = true;
        
        // Test 1: Rectangle Light
        allPassed &= testRectangleLight();
        
        // Test 2: Disk Light
        allPassed &= testDiskLight();
        
        // Test 3: Sphere Light
        allPassed &= testSphereLight();
        
        // Test 4: Area Light Sampling
        allPassed &= testAreaLightSampling();
        
        // Test 5: Soft Shadow Validation
        allPassed &= testSoftShadows();
        
        // Test 6: Performance Benchmark
        allPassed &= testPerformance();
        
        // Final report
        printFinalReport(allPassed);
        
        return allPassed;
    }

private:
    int m_totalTests = 0;
    int m_passedTests = 0;
    
    void recordTest(const std::string& testName, bool passed) {
        m_totalTests++;
        if (passed) {
            m_passedTests++;
            std::cout << "✅ " << testName << " - PASSED" << std::endl;
        } else {
            std::cout << "❌ " << testName << " - FAILED" << std::endl;
        }
    }
    
    /**
     * @brief Test rectangle light functionality
     */
    bool testRectangleLight() {
        std::cout << "\n📐 Rectangle Light Tests" << std::endl;
        std::cout << "-----------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Create rectangle light
            Vec3 center(0, 5, 0);
            Vec3 normal(0, -1, 0);
            float width = 2.0f;
            float height = 1.0f;
            Color3 emission(1.0f, 0.8f, 0.6f);
            
            auto rectLight = RectangleLightFactory::fromCenterAndSize(
                center, normal, width, height, emission, 1.0f, false);
            
            bool creationTest = (rectLight != nullptr);
            recordTest("Rectangle Light Creation", creationTest);
            allPassed &= creationTest;
            
            if (creationTest) {
                // Test basic properties
                bool nameTest = (rectLight->getName() == "Rectangle");
                recordTest("Rectangle Light Name", nameTest);
                allPassed &= nameTest;
                
                bool shapeTest = (rectLight->getShape() == AreaLightShape::RECTANGLE);
                recordTest("Rectangle Light Shape", shapeTest);
                allPassed &= shapeTest;
                
                // Test area calculation
                float expectedArea = width * height;
                float actualArea = rectLight->getArea();
                bool areaTest = (std::abs(actualArea - expectedArea) < 0.01f);
                recordTest("Rectangle Light Area", areaTest);
                allPassed &= areaTest;
                
                // Test surface sampling
                ExtendedRandomSampler sampler(12345);
                Vec3 point, normal_out;
                float pdf;
                rectLight->sampleSurface(sampler, point, normal_out, pdf);
                
                bool samplingTest = (pdf > 0.0f && normal_out.length() > 0.9f);
                recordTest("Rectangle Light Sampling", samplingTest);
                allPassed &= samplingTest;
                
                // Test ray intersection
                Ray testRay(Vec3(0, 0, 0), Vec3(0, 1, 0));
                float t;
                Vec3 hitPoint, hitNormal;
                bool intersectionTest = rectLight->intersect(testRay, t, hitPoint, hitNormal);
                recordTest("Rectangle Light Intersection", intersectionTest);
                allPassed &= intersectionTest;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Exception in rectangle light tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test disk light functionality
     */
    bool testDiskLight() {
        std::cout << "\n🔵 Disk Light Tests" << std::endl;
        std::cout << "------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Create disk light
            Vec3 center(0, 3, 0);
            Vec3 normal(0, -1, 0);
            float radius = 1.5f;
            Color3 emission(0.8f, 0.9f, 1.0f);
            
            auto diskLight = DiskLightFactory::create(
                center, normal, radius, emission, 1.0f, false);
            
            bool creationTest = (diskLight != nullptr);
            recordTest("Disk Light Creation", creationTest);
            allPassed &= creationTest;
            
            if (creationTest) {
                // Test basic properties
                bool nameTest = (diskLight->getName() == "Disk");
                recordTest("Disk Light Name", nameTest);
                allPassed &= nameTest;
                
                bool shapeTest = (diskLight->getShape() == AreaLightShape::DISK);
                recordTest("Disk Light Shape", shapeTest);
                allPassed &= shapeTest;
                
                // Test area calculation
                float expectedArea = M_PI * radius * radius;
                float actualArea = diskLight->getArea();
                bool areaTest = (std::abs(actualArea - expectedArea) < 0.01f);
                recordTest("Disk Light Area", areaTest);
                allPassed &= areaTest;
                
                // Test surface sampling
                ExtendedRandomSampler sampler(12345);
                Vec3 point, normal_out;
                float pdf;
                diskLight->sampleSurface(sampler, point, normal_out, pdf);
                
                bool samplingTest = (pdf > 0.0f && normal_out.length() > 0.9f);
                recordTest("Disk Light Sampling", samplingTest);
                allPassed &= samplingTest;
                
                // Test point inside disk
                Vec3 testPoint = center + Vec3(0.5f, 0, 0.5f);
                bool insideTest = diskLight->isInsideDisk(testPoint);
                recordTest("Disk Light Inside Test", insideTest);
                allPassed &= insideTest;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Exception in disk light tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test sphere light functionality
     */
    bool testSphereLight() {
        std::cout << "\n🌕 Sphere Light Tests" << std::endl;
        std::cout << "--------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Create sphere light
            Vec3 center(0, 4, 0);
            float radius = 0.8f;
            Color3 emission(1.0f, 1.0f, 0.8f);
            
            auto sphereLight = SphereLightFactory::create(
                center, radius, emission, 1.0f);
            
            bool creationTest = (sphereLight != nullptr);
            recordTest("Sphere Light Creation", creationTest);
            allPassed &= creationTest;
            
            if (creationTest) {
                // Test basic properties
                bool nameTest = (sphereLight->getName() == "Sphere");
                recordTest("Sphere Light Name", nameTest);
                allPassed &= nameTest;
                
                bool shapeTest = (sphereLight->getShape() == AreaLightShape::SPHERE);
                recordTest("Sphere Light Shape", shapeTest);
                allPassed &= shapeTest;
                
                // Test area calculation
                float expectedArea = 4.0f * M_PI * radius * radius;
                float actualArea = sphereLight->getArea();
                bool areaTest = (std::abs(actualArea - expectedArea) < 0.01f);
                recordTest("Sphere Light Area", areaTest);
                allPassed &= areaTest;
                
                // Test surface sampling
                ExtendedRandomSampler sampler(12345);
                Vec3 point, normal_out;
                float pdf;
                sphereLight->sampleSurface(sampler, point, normal_out, pdf);
                
                bool samplingTest = (pdf > 0.0f && normal_out.length() > 0.9f);
                recordTest("Sphere Light Sampling", samplingTest);
                allPassed &= samplingTest;
                
                // Test sphere intersection
                Ray testRay(Vec3(0, 0, 0), Vec3(0, 1, 0));
                float t;
                Vec3 hitPoint, hitNormal;
                bool intersectionTest = sphereLight->intersect(testRay, t, hitPoint, hitNormal);
                recordTest("Sphere Light Intersection", intersectionTest);
                allPassed &= intersectionTest;
                
                // Test solid angle calculation
                Vec3 testPoint(0, 0, 0);
                float solidAngle = sphereLight->getSolidAngle(testPoint);
                bool solidAngleTest = (solidAngle > 0.0f && solidAngle <= 4.0f * M_PI);
                recordTest("Sphere Light Solid Angle", solidAngleTest);
                allPassed &= solidAngleTest;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Exception in sphere light tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test area light sampling consistency
     */
    bool testAreaLightSampling() {
        std::cout << "\n🎯 Area Light Sampling Tests" << std::endl;
        std::cout << "----------------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Test sampling consistency for all light types
            std::vector<std::shared_ptr<AreaLightBase>> lights = {
                RectangleLightFactory::createSquare(Vec3(0, 5, 0), Vec3(0, -1, 0), 2.0f, Color3(1.0f)),
                DiskLightFactory::create(Vec3(0, 5, 0), Vec3(0, -1, 0), 1.0f, Color3(1.0f)),
                SphereLightFactory::create(Vec3(0, 5, 0), 0.5f, Color3(1.0f))
            };
            
            for (size_t i = 0; i < lights.size(); ++i) {
                auto light = lights[i];
                ExtendedRandomSampler sampler(12345);
                
                // Test multiple samples
                const int numSamples = 1000;
                int validSamples = 0;
                float totalPDF = 0.0f;
                
                for (int j = 0; j < numSamples; ++j) {
                    Vec3 point, normal;
                    float pdf;
                    light->sampleSurface(sampler, point, normal, pdf);
                    
                    if (pdf > 0.0f && normal.length() > 0.9f) {
                        validSamples++;
                        totalPDF += pdf;
                    }
                }
                
                float validRatio = static_cast<float>(validSamples) / numSamples;
                bool samplingTest = (validRatio > 0.95f); // At least 95% valid samples
                recordTest("Sampling Consistency " + light->getName(), samplingTest);
                allPassed &= samplingTest;
                
                // Test PDF consistency
                float avgPDF = totalPDF / validSamples;
                float expectedPDF = 1.0f / light->getArea();
                bool pdfTest = (std::abs(avgPDF - expectedPDF) < expectedPDF * 0.1f); // Within 10%
                recordTest("PDF Consistency " + light->getName(), pdfTest);
                allPassed &= pdfTest;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Exception in sampling tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test soft shadow validation
     */
    bool testSoftShadows() {
        std::cout << "\n🌫️ Soft Shadow Tests" << std::endl;
        std::cout << "-------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            // Create area light for soft shadows
            auto areaLight = RectangleLightFactory::fromCenterAndSize(
                Vec3(0, 5, 0), Vec3(0, -1, 0), 2.0f, 2.0f, Color3(1.0f));
            
            // Test intersection from multiple points (simulating soft shadow sampling)
            std::vector<Vec3> testPoints = {
                Vec3(-1, 0, -1), Vec3(0, 0, 0), Vec3(1, 0, 1),
                Vec3(-0.5f, 0, 0.5f), Vec3(0.5f, 0, -0.5f)
            };
            
            int visibleCount = 0;
            for (const auto& point : testPoints) {
                Ray ray(point, (areaLight->getCenter() - point).normalized());
                float t;
                Vec3 hitPoint, hitNormal;
                
                if (areaLight->intersect(ray, t, hitPoint, hitNormal)) {
                    visibleCount++;
                }
            }
            
            // Some points should see the light (for soft shadows)
            bool softShadowTest = (visibleCount > 0 && visibleCount < testPoints.size());
            recordTest("Soft Shadow Visibility", softShadowTest);
            allPassed &= softShadowTest;
            
        } catch (const std::exception& e) {
            std::cout << "Exception in soft shadow tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    /**
     * @brief Test performance benchmarks
     */
    bool testPerformance() {
        std::cout << "\n⚡ Performance Benchmark Tests" << std::endl;
        std::cout << "-----------------------------" << std::endl;
        
        bool allPassed = true;
        
        try {
            auto areaLight = RectangleLightFactory::createSquare(
                Vec3(0, 5, 0), Vec3(0, -1, 0), 2.0f, Color3(1.0f));
            
            ExtendedRandomSampler sampler(12345);
            
            // Benchmark surface sampling
            const int numSamples = 10000;
            auto start = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < numSamples; ++i) {
                Vec3 point, normal;
                float pdf;
                areaLight->sampleSurface(sampler, point, normal, pdf);
                // Prevent optimization
                volatile float dummy = point.x + normal.y + pdf;
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
            
            float nsPerSample = static_cast<float>(duration.count()) / numSamples;
            std::cout << "    Area light sampling: " << nsPerSample << " ns/sample" << std::endl;
            
            // Target: < 500 ns per sample
            bool performanceTest = (nsPerSample < 500.0f);
            recordTest("Area Light Sampling Performance", performanceTest);
            allPassed &= performanceTest;
            
        } catch (const std::exception& e) {
            std::cout << "Exception in performance tests: " << e.what() << std::endl;
            allPassed = false;
        }
        
        return allPassed;
    }
    
    void printFinalReport(bool allPassed) {
        std::cout << "\n🎊 Area Lights Test Report" << std::endl;
        std::cout << "==========================" << std::endl;
        std::cout << "Total Tests: " << m_totalTests << std::endl;
        std::cout << "Passed: " << m_passedTests << std::endl;
        std::cout << "Failed: " << (m_totalTests - m_passedTests) << std::endl;
        std::cout << "Success Rate: " << (100.0f * m_passedTests / m_totalTests) << "%" << std::endl;
        
        if (allPassed) {
            std::cout << "\n🎉 ALL TESTS PASSED!" << std::endl;
            std::cout << "✅ Area Lights System Working!" << std::endl;
            std::cout << "🚀 Task 2 Phase 3.2.2 COMPLETE!" << std::endl;
            std::cout << "💡 Ready for Task 3: Multiple Importance Sampling!" << std::endl;
        } else {
            std::cout << "\n❌ Some tests failed." << std::endl;
        }
    }
};

int main() {
    std::cout << "PhotonRender Area Lights Test" << std::endl;
    std::cout << "Phase 3.2.2 Task 2 Validation" << std::endl;
    
    AreaLightsTest test;
    bool success = test.runTests();
    
    return success ? 0 : 1;
}
