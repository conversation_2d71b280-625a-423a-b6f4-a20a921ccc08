// src/core/scene/scene_loader.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Scene loading implementation

#include "scene_loader.hpp"
#include "../geometry/mesh.hpp"
#include "../material/material.hpp"
#include "light.hpp"
#include "camera.hpp"
#include <fstream>
#include <sstream>
#include <chrono>
#include <iostream>
#include <algorithm>

namespace photon {

std::shared_ptr<Scene> SceneLoader::loadFromFile(const std::string& filename,
                                                const SceneLoadConfig& config,
                                                SceneLoadStats* stats) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    SceneLoadStats localStats;
    if (!stats) stats = &localStats;
    
    // Check if file exists
    if (!fileExists(filename)) {
        stats->addError("File not found: " + filename);
        return nullptr;
    }
    
    // Detect format
    SceneFormat format = detectFormat(filename);
    if (format == SceneFormat::AUTO_DETECT) {
        stats->addError("Unknown file format: " + filename);
        return nullptr;
    }
    
    // Read file content
    std::string content = readFileToString(filename);
    if (content.empty()) {
        stats->addError("Failed to read file: " + filename);
        return nullptr;
    }
    
    // Set base path for relative references
    SceneLoadConfig configWithPath = config;
    if (configWithPath.basePath.empty()) {
        configWithPath.basePath = getBasePath(filename);
    }
    
    std::shared_ptr<Scene> scene;
    
    switch (format) {
        case SceneFormat::JSON:
            scene = loadFromJSON(content, configWithPath, stats);
            break;
        case SceneFormat::XML:
            stats->addError("XML format not yet implemented");
            return nullptr;
        default:
            stats->addError("Unsupported format");
            return nullptr;
    }
    
    // Calculate load time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    stats->loadTimeMs = duration.count() / 1000.0f;
    
    if (scene && config.validateScene) {
        // TODO: Add scene validation
        stats->addWarning("Scene validation not yet implemented");
    }
    
    return scene;
}

std::shared_ptr<Scene> SceneLoader::loadFromJSON(const std::string& jsonString,
                                                const SceneLoadConfig& config,
                                                SceneLoadStats* stats) {
    SceneLoadStats localStats;
    if (!stats) stats = &localStats;
    
    try {
        return parseJSONScene(jsonString, config, stats);
    } catch (const std::exception& e) {
        stats->addError("JSON parsing error: " + std::string(e.what()));
        return nullptr;
    }
}

bool SceneLoader::saveToFile(const Scene& scene, const std::string& filename, SceneFormat format) {
    if (format == SceneFormat::AUTO_DETECT) {
        format = detectFormat(filename);
    }
    
    std::string content;
    switch (format) {
        case SceneFormat::JSON:
            content = saveToJSON(scene);
            break;
        case SceneFormat::XML:
            // TODO: Implement XML export
            std::cerr << "XML export not yet implemented" << std::endl;
            return false;
        default:
            std::cerr << "Unsupported export format" << std::endl;
            return false;
    }
    
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return false;
    }
    
    file << content;
    file.close();
    
    std::cout << "Scene saved to: " << filename << std::endl;
    return true;
}

std::string SceneLoader::saveToJSON(const Scene& scene) {
    std::ostringstream json;
    json << "{\n";
    json << "  \"version\": \"1.0\",\n";
    json << "  \"description\": \"PhotonRender Scene\",\n";
    
    // Camera
    json << "  \"camera\": " << generateCameraJSON(scene) << ",\n";
    
    // Materials
    json << "  \"materials\": " << generateMaterialsJSON(scene) << ",\n";
    
    // Lights
    json << "  \"lights\": " << generateLightsJSON(scene) << ",\n";
    
    // Geometry
    json << "  \"geometry\": " << generateGeometryJSON(scene) << "\n";
    
    json << "}";
    return json.str();
}

SceneFormat SceneLoader::detectFormat(const std::string& filename) {
    std::string ext = getFileExtension(filename);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    if (ext == ".json") return SceneFormat::JSON;
    if (ext == ".xml") return SceneFormat::XML;
    
    return SceneFormat::AUTO_DETECT;
}

bool SceneLoader::validateFile(const std::string& filename) {
    if (!fileExists(filename)) return false;
    
    SceneFormat format = detectFormat(filename);
    if (format == SceneFormat::AUTO_DETECT) return false;
    
    std::string content = readFileToString(filename);
    if (content.empty()) return false;
    
    // Basic validation
    switch (format) {
        case SceneFormat::JSON: {
            auto json = SimpleJSONParser::parse(content);
            return json != nullptr;
        }
        case SceneFormat::XML:
            // TODO: Implement XML validation
            return false;
        default:
            return false;
    }
}

std::shared_ptr<Scene> SceneLoader::createTestScene() {
    auto scene = std::make_shared<Scene>();
    
    // Create Cornell Box geometry
    auto mesh = std::make_shared<Mesh>();
    
    // Floor
    mesh->addQuad(
        Point3(-1, -1, -1), Point3(1, -1, -1),
        Point3(1, -1, 1), Point3(-1, -1, 1)
    );

    // Ceiling
    mesh->addQuad(
        Point3(-1, 1, -1), Point3(-1, 1, 1),
        Point3(1, 1, 1), Point3(1, 1, -1)
    );

    // Back wall
    mesh->addQuad(
        Point3(-1, -1, 1), Point3(1, -1, 1),
        Point3(1, 1, 1), Point3(-1, 1, 1)
    );

    // Left wall (red)
    mesh->addQuad(
        Point3(-1, -1, -1), Point3(-1, -1, 1),
        Point3(-1, 1, 1), Point3(-1, 1, -1)
    );

    // Right wall (green)
    mesh->addQuad(
        Point3(1, -1, -1), Point3(1, 1, -1),
        Point3(1, 1, 1), Point3(1, -1, 1)
    );
    
    scene->addMesh(mesh);
    
    // Create default camera
    auto camera = std::make_shared<PerspectiveCamera>();
    // TODO: Implement setPosition, setTarget, setCamera methods
    // camera->setPosition(Vec3(0, 0, -3));
    // camera->setTarget(Vec3(0, 0, 0));
    camera->setFOV(45.0f);
    // scene->setCamera(camera);
    
    // Create default light
    auto light = std::make_shared<PointLight>();
    light->setPosition(Vec3(0, 0.8f, 0));
    // TODO: Implement setColor method
    // light->setColor(Color3(1, 1, 1));
    light->setIntensity(10.0f);
    scene->addLight(light);
    
    return scene;
}

std::shared_ptr<Scene> SceneLoader::createEmptyScene() {
    auto scene = std::make_shared<Scene>();
    
    // Create default camera
    auto camera = std::make_shared<PerspectiveCamera>();
    // TODO: Implement setPosition, setTarget, setCamera methods
    // camera->setPosition(Vec3(0, 0, -5));
    // camera->setTarget(Vec3(0, 0, 0));
    camera->setFOV(45.0f);
    // scene->setCamera(camera);
    
    return scene;
}

// Private helper functions
std::shared_ptr<Scene> SceneLoader::parseJSONScene(const std::string& jsonString,
                                                   const SceneLoadConfig& config,
                                                   SceneLoadStats* stats) {
    auto json = SimpleJSONParser::parse(jsonString);
    if (!json || !json->isObject()) {
        stats->addError("Invalid JSON format");
        return nullptr;
    }
    
    auto scene = std::make_shared<Scene>();
    
    // Load camera
    if (config.loadCamera) {
        auto cameraData = json->get("camera");
        if (cameraData) {
            // TODO: Implement camera loading
            stats->addWarning("Camera loading not yet implemented");
        }
    }
    
    // Load materials
    if (config.loadMaterials) {
        auto materialsData = json->get("materials");
        if (materialsData) {
            // TODO: Implement materials loading
            stats->addWarning("Materials loading not yet implemented");
        }
    }
    
    // Load lights
    if (config.loadLights) {
        auto lightsData = json->get("lights");
        if (lightsData) {
            // TODO: Implement lights loading
            stats->addWarning("Lights loading not yet implemented");
        }
    }
    
    // Load geometry
    if (config.loadGeometry) {
        auto geometryData = json->get("geometry");
        if (geometryData) {
            // TODO: Implement geometry loading
            stats->addWarning("Geometry loading not yet implemented");
        }
    }
    
    // For now, return a test scene
    return createTestScene();
}

// Utility functions
std::string SceneLoader::readFileToString(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) return "";
    
    std::ostringstream content;
    content << file.rdbuf();
    return content.str();
}

std::string SceneLoader::getFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos == std::string::npos) return "";
    return filename.substr(pos);
}

std::string SceneLoader::getBasePath(const std::string& filename) {
    size_t pos = filename.find_last_of("/\\");
    if (pos == std::string::npos) return "";
    return filename.substr(0, pos + 1);
}

bool SceneLoader::fileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

// JSON generation helpers (stubs for now)
std::string SceneLoader::generateCameraJSON(const Scene& scene) {
    return "{\n    \"type\": \"perspective\",\n    \"position\": [0, 0, -5],\n    \"target\": [0, 0, 0],\n    \"fov\": 45\n  }";
}

std::string SceneLoader::generateMaterialsJSON(const Scene& scene) {
    return "[]";
}

std::string SceneLoader::generateLightsJSON(const Scene& scene) {
    return "[]";
}

std::string SceneLoader::generateGeometryJSON(const Scene& scene) {
    return "[]";
}

// Simple JSON Parser Implementation
std::shared_ptr<SimpleJSONParser::JSONValue> SimpleJSONParser::parse(const std::string& json) {
    size_t pos = 0;
    skipWhitespace(json, pos);
    return parseValue(json, pos);
}

std::shared_ptr<SimpleJSONParser::JSONValue> SimpleJSONParser::parseValue(const std::string& json, size_t& pos) {
    skipWhitespace(json, pos);

    if (pos >= json.length()) return nullptr;

    char c = json[pos];

    if (c == '{') {
        return parseObject(json, pos);
    } else if (c == '[') {
        return parseArray(json, pos);
    } else if (c == '"') {
        return parseString(json, pos);
    } else if (c == 't' || c == 'f') {
        // Boolean
        auto value = std::make_shared<JSONValue>(JSONValue::BOOLEAN);
        if (json.substr(pos, 4) == "true") {
            value->boolValue = true;
            pos += 4;
        } else if (json.substr(pos, 5) == "false") {
            value->boolValue = false;
            pos += 5;
        }
        return value;
    } else if (c == 'n') {
        // Null
        if (json.substr(pos, 4) == "null") {
            pos += 4;
            return std::make_shared<JSONValue>(JSONValue::NULL_VALUE);
        }
    } else if (c == '-' || (c >= '0' && c <= '9')) {
        return parseNumber(json, pos);
    }

    return nullptr;
}

std::shared_ptr<SimpleJSONParser::JSONValue> SimpleJSONParser::parseObject(const std::string& json, size_t& pos) {
    auto obj = std::make_shared<JSONValue>(JSONValue::OBJECT);

    pos++; // Skip '{'
    skipWhitespace(json, pos);

    if (pos < json.length() && json[pos] == '}') {
        pos++; // Empty object
        return obj;
    }

    while (pos < json.length()) {
        skipWhitespace(json, pos);

        // Parse key
        auto key = parseString(json, pos);
        if (!key) break;

        skipWhitespace(json, pos);
        if (pos >= json.length() || json[pos] != ':') break;
        pos++; // Skip ':'

        // Parse value
        auto value = parseValue(json, pos);
        if (!value) break;

        obj->objectValue[key->stringValue] = value;

        skipWhitespace(json, pos);
        if (pos >= json.length()) break;

        if (json[pos] == '}') {
            pos++;
            break;
        } else if (json[pos] == ',') {
            pos++;
        } else {
            break;
        }
    }

    return obj;
}

std::shared_ptr<SimpleJSONParser::JSONValue> SimpleJSONParser::parseArray(const std::string& json, size_t& pos) {
    auto arr = std::make_shared<JSONValue>(JSONValue::ARRAY);

    pos++; // Skip '['
    skipWhitespace(json, pos);

    if (pos < json.length() && json[pos] == ']') {
        pos++; // Empty array
        return arr;
    }

    while (pos < json.length()) {
        auto value = parseValue(json, pos);
        if (!value) break;

        arr->arrayValue.push_back(value);

        skipWhitespace(json, pos);
        if (pos >= json.length()) break;

        if (json[pos] == ']') {
            pos++;
            break;
        } else if (json[pos] == ',') {
            pos++;
        } else {
            break;
        }
    }

    return arr;
}

std::shared_ptr<SimpleJSONParser::JSONValue> SimpleJSONParser::parseString(const std::string& json, size_t& pos) {
    auto str = std::make_shared<JSONValue>(JSONValue::STRING);

    pos++; // Skip opening '"'
    size_t start = pos;

    while (pos < json.length() && json[pos] != '"') {
        if (json[pos] == '\\') pos++; // Skip escaped character
        pos++;
    }

    if (pos < json.length()) {
        str->stringValue = json.substr(start, pos - start);
        pos++; // Skip closing '"'
    }

    return str;
}

std::shared_ptr<SimpleJSONParser::JSONValue> SimpleJSONParser::parseNumber(const std::string& json, size_t& pos) {
    auto num = std::make_shared<JSONValue>(JSONValue::NUMBER);

    size_t start = pos;
    if (json[pos] == '-') pos++;

    while (pos < json.length() && ((json[pos] >= '0' && json[pos] <= '9') || json[pos] == '.')) {
        pos++;
    }

    std::string numStr = json.substr(start, pos - start);
    num->numberValue = std::stod(numStr);

    return num;
}

void SimpleJSONParser::skipWhitespace(const std::string& json, size_t& pos) {
    while (pos < json.length() && (json[pos] == ' ' || json[pos] == '\t' ||
           json[pos] == '\n' || json[pos] == '\r')) {
        pos++;
    }
}

std::string SimpleJSONParser::stringify(const JSONValue& value, int indent) {
    // TODO: Implement JSON stringification
    return "{}";
}

} // namespace photon
