# PhotonRender Phase 3.2.2 Technical Specification
**Data**: 2025-01-20  
**Fase**: 3.2.2 Advanced Lighting System  
**Status**: 🚀 **INIZIATA** - Specifica Tecnica Completa

## 🎯 Obiettivo Fase 3.2.2

Implementare un sistema di illuminazione avanzato che porta PhotonRender al livello dei renderer professionali, con supporto per HDRI environment lighting, area lights, multiple importance sampling e controlli avanzati di illuminazione.

## 📋 Task Overview (6 Task Principali)

### **Task 1: HDRI Environment Lighting**
**Obiettivo**: Implementare illuminazione basata su environment maps HDRI
**Priorità**: ALTA
**Durata Stimata**: 3-4 ore

#### Componenti da Implementare:
- **Environment Map Loading**: Support per HDR, EXR, RGBE formats
- **Spherical Mapping**: UV mapping per environment sphere
- **Importance Sampling**: Efficient sampling basato su luminanza
- **Environment Light Class**: Integrazione nel sistema light esistente

#### File da Creare/Modificare:
```cpp
// src/core/light/environment_light.hpp - Environment light implementation
// src/core/light/environment_light.cpp - Environment light implementation
// src/core/texture/hdr_texture.hpp - HDR texture loading
// src/core/texture/hdr_texture.cpp - HDR texture implementation
// src/core/integrator/environment_integrator.hpp - Environment integration
```

#### Funzionalità Chiave:
- **HDR Image Loading**: PNG, EXR, RGBE, Radiance HDR
- **Spherical Projection**: Equirectangular → sphere mapping
- **Importance Sampling**: Luminance-based sampling distribution
- **Rotation Control**: Environment rotation per artistic control
- **Intensity Scaling**: Environment intensity multiplier

---

### **Task 2: Area Lights Implementation**
**Obiettivo**: Implementare area lights per soft lighting realistico
**Priorità**: ALTA
**Durata Stimata**: 3-4 ore

#### Componenti da Implementare:
- **Geometric Area Lights**: Rectangle, disk, sphere shapes
- **Soft Shadow Sampling**: Multiple samples per soft shadows
- **Area Light Sampling**: Uniform + cosine-weighted sampling
- **Integration**: Seamless con sistema light esistente

#### File da Creare/Modificare:
```cpp
// src/core/light/area_light.hpp - Area light base class
// src/core/light/area_light.cpp - Area light implementation
// src/core/light/rectangle_light.hpp - Rectangle area light
// src/core/light/rectangle_light.cpp - Rectangle implementation
// src/core/light/disk_light.hpp - Disk area light
// src/core/light/sphere_light.hpp - Sphere area light
```

#### Funzionalità Chiave:
- **Rectangle Lights**: Width/height parametric control
- **Disk Lights**: Radius-based circular lights
- **Sphere Lights**: Volumetric sphere lighting
- **Soft Shadows**: Multiple sampling per realistic shadows
- **Two-sided Emission**: Front/back emission control

---

### **Task 3: Multiple Importance Sampling (MIS)**
**Obiettivo**: Implementare MIS per riduzione noise e convergenza veloce
**Priorità**: MEDIA-ALTA
**Durata Stimata**: 4-5 ore

#### Componenti da Implementare:
- **MIS Framework**: Power heuristic + balance heuristic
- **BSDF + Light Sampling**: Combined sampling strategies
- **Variance Reduction**: Optimal sample combination
- **Performance Optimization**: Efficient MIS computation

#### File da Creare/Modificare:
```cpp
// src/core/integrator/mis_integrator.hpp - MIS integrator
// src/core/integrator/mis_integrator.cpp - MIS implementation
// src/core/sampling/mis_sampling.hpp - MIS sampling utilities
// src/core/sampling/mis_sampling.cpp - MIS sampling implementation
```

#### Funzionalità Chiave:
- **Power Heuristic**: Optimal sample weighting
- **Balance Heuristic**: Alternative weighting strategy
- **BSDF Sampling**: Material-based importance sampling
- **Light Sampling**: Light-based importance sampling
- **Adaptive Sampling**: Dynamic sample allocation

---

### **Task 4: Light Linking System**
**Obiettivo**: Sistema di controllo selettivo illuminazione
**Priorità**: MEDIA
**Durata Stimata**: 2-3 ore

#### Componenti da Implementare:
- **Light Groups**: Grouping lights per controllo
- **Object Exclusion**: Exclude objects da specific lights
- **Layer-based Lighting**: Layer-specific illumination
- **UI Integration**: SketchUp layer integration

#### File da Creare/Modificare:
```cpp
// src/core/light/light_group.hpp - Light grouping system
// src/core/light/light_group.cpp - Light group implementation
// src/core/scene/light_linking.hpp - Light linking management
// src/core/scene/light_linking.cpp - Light linking implementation
```

#### Funzionalità Chiave:
- **Light Groups**: Named light collections
- **Inclusion/Exclusion**: Per-object light control
- **Layer Integration**: SketchUp layer-based lighting
- **Performance**: Efficient light culling

---

### **Task 5: Advanced Light Types**
**Obiettivo**: Spot lights con falloff + IES profiles
**Priorità**: MEDIA
**Durata Stimata**: 3-4 ore

#### Componenti da Implementare:
- **Spot Lights**: Cone angle + falloff control
- **IES Profiles**: Industry standard light profiles
- **Photometric Lights**: Real-world light data
- **Artistic Controls**: Falloff curves, intensity

#### File da Creare/Modificare:
```cpp
// src/core/light/spot_light.hpp - Spot light implementation
// src/core/light/spot_light.cpp - Spot light implementation
// src/core/light/ies_light.hpp - IES profile light
// src/core/light/ies_light.cpp - IES implementation
// src/core/texture/ies_profile.hpp - IES profile loading
```

#### Funzionalità Chiave:
- **Spot Light**: Inner/outer cone angles
- **Falloff Control**: Linear, quadratic, custom curves
- **IES Profiles**: Standard photometric data
- **Barn Doors**: Rectangular light shaping
- **Gobo Textures**: Light pattern projection

---

### **Task 6: Lighting Performance Optimization**
**Obiettivo**: Ottimizzazione performance per scene complesse
**Priorità**: MEDIA
**Durata Stimata**: 2-3 ore

#### Componenti da Implementare:
- **Light BVH**: Spatial acceleration per lights
- **Light Culling**: Frustum + distance culling
- **Adaptive Sampling**: Dynamic light sampling
- **Memory Optimization**: Efficient light storage

#### File da Creare/Modificare:
```cpp
// src/core/accelerator/light_bvh.hpp - Light acceleration
// src/core/accelerator/light_bvh.cpp - Light BVH implementation
// src/core/light/light_manager.hpp - Light management
// src/core/light/light_manager.cpp - Light manager implementation
```

#### Funzionalità Chiave:
- **Spatial Acceleration**: BVH per light queries
- **Culling**: Distance + frustum culling
- **LOD System**: Level-of-detail per lights
- **Memory Pool**: Efficient light allocation

## 🏗️ Architettura Sistema Lighting

### **Core Components**
```cpp
namespace photon {
    // Base light system
    class Light;                    // Base light class
    class LightSample;             // Light sampling result
    class LightManager;            // Light management
    
    // Environment lighting
    class EnvironmentLight;        // HDRI environment
    class HDRTexture;             // HDR texture loading
    
    // Area lights
    class AreaLight;              // Base area light
    class RectangleLight;         // Rectangle area light
    class DiskLight;              // Disk area light
    class SphereLight;            // Sphere area light
    
    // Advanced lights
    class SpotLight;              // Spot light with falloff
    class IESLight;               // IES profile light
    class IESProfile;             // IES data loading
    
    // MIS system
    class MISIntegrator;          // Multiple importance sampling
    class MISSampling;            // MIS utilities
    
    // Light linking
    class LightGroup;             // Light grouping
    class LightLinking;           // Link management
    
    // Acceleration
    class LightBVH;               // Light acceleration
}
```

### **Integration Points**
- **Integrator System**: MIS integration nel path tracer esistente
- **Material System**: BRDF sampling coordination con light sampling
- **Scene Management**: Light culling e spatial queries
- **SketchUp Plugin**: UI controls per advanced lighting

## 📊 Performance Targets

### **Rendering Performance**
- **HDRI Environment**: < 5% overhead vs point lights
- **Area Lights**: < 10% overhead per area light
- **MIS Integration**: 20-50% noise reduction
- **Light Culling**: 90%+ lights culled in complex scenes

### **Memory Usage**
- **HDR Textures**: Efficient compression + streaming
- **Light Storage**: < 1KB per light average
- **BVH Overhead**: < 10% memory overhead
- **IES Profiles**: Compressed lookup tables

## 🧪 Testing Strategy

### **Unit Tests**
- **Light Sampling**: Verify PDF correctness
- **MIS Weights**: Validate heuristic computation
- **Environment Mapping**: UV coordinate correctness
- **Area Light Geometry**: Shape sampling validation

### **Integration Tests**
- **Rendering Quality**: Reference image comparison
- **Performance**: Benchmark complex lighting scenes
- **Memory**: Leak detection + usage profiling
- **Convergence**: Noise reduction validation

### **Visual Tests**
- **HDRI Environments**: Various environment maps
- **Soft Shadows**: Area light shadow quality
- **Light Linking**: Selective illumination
- **IES Profiles**: Real-world light patterns

## 🎯 Success Criteria

### **Functional Requirements**
- ✅ HDRI environment lighting working
- ✅ Area lights (rectangle, disk, sphere) implemented
- ✅ MIS reducing noise by 20-50%
- ✅ Light linking system functional
- ✅ Spot lights + IES profiles working
- ✅ Performance targets met

### **Quality Requirements**
- ✅ Zero memory leaks
- ✅ Physically accurate lighting
- ✅ Professional-grade features
- ✅ SketchUp integration ready
- ✅ Comprehensive test coverage

---

**Fase 3.2.2 Target**: Sistema di illuminazione avanzato di livello professionale  
**Timeline**: 15-20 ore di sviluppo  
**Outcome**: PhotonRender con capacità lighting comparabili a renderer commerciali
