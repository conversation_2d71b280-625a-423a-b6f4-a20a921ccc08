// src/core/sampler/sampler.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Random sampling system

#pragma once

#include "../math/vec2.hpp"
#include "../math/vec3.hpp"
#include <random>
#include <memory>

namespace photon {

/**
 * @brief Abstract base sampler class
 * 
 * Provides random numbers for Monte Carlo sampling
 */
class Sampler {
public:
    Sampler() = default;
    virtual ~Sampler() = default;
    
    // Non-copyable
    Sampler(const Sampler&) = delete;
    Sampler& operator=(const Sampler&) = delete;
    
    /**
     * @brief Get 1D random sample [0,1)
     */
    virtual float get1D() = 0;
    
    /**
     * @brief Get 2D random sample [0,1)^2
     */
    virtual Vec3 get2D() { return Vec3(get1D(), get1D(), 0); }

    /**
     * @brief Get 2D random sample as Vec2 (compatibility method)
     */
    virtual Vec2 next2D() {
        Vec3 sample = get2D();
        return Vec2(sample.x, sample.y);
    }
    
    /**
     * @brief Clone sampler for multi-threading
     */
    virtual std::unique_ptr<Sampler> clone() const = 0;
    
    /**
     * @brief Seed the sampler
     */
    virtual void seed(uint32_t seed) = 0;
    
    /**
     * @brief Get sampler name
     */
    virtual std::string getName() const = 0;
};

/**
 * @brief Simple random sampler using std::mt19937
 */
class RandomSampler : public Sampler {
public:
    /**
     * @brief Constructor
     * 
     * @param seed Random seed (0 = use random device)
     */
    RandomSampler(uint32_t seed = 0);
    
    float get1D() override;
    Vec3 get2D() override;
    std::unique_ptr<Sampler> clone() const override;
    void seed(uint32_t seed) override;
    std::string getName() const override { return "Random"; }

private:
    mutable std::mt19937 m_rng;
    mutable std::uniform_real_distribution<float> m_dist;
};

/**
 * @brief Stratified sampler for better distribution
 */
class StratifiedSampler : public Sampler {
public:
    /**
     * @brief Constructor
     * 
     * @param xSamples Number of samples in X dimension
     * @param ySamples Number of samples in Y dimension
     * @param jitter Enable jittering
     */
    StratifiedSampler(int xSamples = 4, int ySamples = 4, bool jitter = true);
    
    float get1D() override;
    Vec3 get2D() override;
    std::unique_ptr<Sampler> clone() const override;
    void seed(uint32_t seed) override;
    std::string getName() const override { return "Stratified"; }
    
    /**
     * @brief Start new pixel (resets stratification)
     */
    void startPixel();

private:
    int m_xSamples, m_ySamples;
    bool m_jitter;
    int m_currentSample;
    
    std::mt19937 m_rng;
    std::uniform_real_distribution<float> m_dist;
    
    std::vector<float> m_samples1D;
    std::vector<Vec3> m_samples2D;
    size_t m_current1D, m_current2D;
    
    void generateSamples();
};

/**
 * @brief Halton sequence sampler (low-discrepancy)
 */
class HaltonSampler : public Sampler {
public:
    /**
     * @brief Constructor
     * 
     * @param samplesPerPixel Number of samples per pixel
     */
    HaltonSampler(int samplesPerPixel = 16);
    
    float get1D() override;
    Vec3 get2D() override;
    std::unique_ptr<Sampler> clone() const override;
    void seed(uint32_t seed) override;
    std::string getName() const override { return "Halton"; }
    
    /**
     * @brief Start new pixel
     */
    void startPixel(int pixelX, int pixelY);

private:
    int m_samplesPerPixel;
    int m_currentSample;
    int m_pixelX, m_pixelY;
    
    /**
     * @brief Compute Halton sequence value
     */
    float halton(int index, int base) const;
    
    /**
     * @brief Radical inverse function
     */
    float radicalInverse(int n, int base) const;
};

/**
 * @brief Utility functions for sampling
 */
namespace sampling {
    /**
     * @brief Sample uniform hemisphere
     */
    Vec3 sampleUniformHemisphere(float u1, float u2);
    
    /**
     * @brief Sample cosine-weighted hemisphere
     */
    Vec3 sampleCosineHemisphere(float u1, float u2);
    
    /**
     * @brief Sample uniform sphere
     */
    Vec3 sampleUniformSphere(float u1, float u2);
    
    /**
     * @brief Sample uniform disk
     */
    Vec3 sampleUniformDisk(float u1, float u2);
    
    /**
     * @brief Sample uniform triangle
     */
    Vec3 sampleUniformTriangle(float u1, float u2);
    
    /**
     * @brief Convert uniform samples to hemisphere direction
     */
    Vec3 uniformSampleToHemisphere(float u1, float u2, const Normal3& normal);
    
    /**
     * @brief Convert uniform samples to cosine hemisphere direction
     */
    Vec3 cosineSampleToHemisphere(float u1, float u2, const Normal3& normal);
    
    /**
     * @brief Probability density for uniform hemisphere sampling
     */
    float uniformHemispherePdf();
    
    /**
     * @brief Probability density for cosine hemisphere sampling
     */
    float cosineHemispherePdf(float cosTheta);
    
    /**
     * @brief Power heuristic for multiple importance sampling
     */
    float powerHeuristic(int nf, float fPdf, int ng, float gPdf);
    
    /**
     * @brief Balance heuristic for multiple importance sampling
     */
    float balanceHeuristic(int nf, float fPdf, int ng, float gPdf);
}

} // namespace photon
