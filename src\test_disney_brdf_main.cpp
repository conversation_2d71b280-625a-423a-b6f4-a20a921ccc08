// src/test_disney_brdf_main.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Disney BRDF Test Application

#include "core/material/disney_brdf.hpp"
#include "core/material/material.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/scene/intersection.hpp"
#include "core/image/image.hpp"
#include "core/renderer/mock_renderer.hpp"
#include <iostream>
#include <memory>
#include <chrono>

using namespace photon;

/**
 * @brief Test Disney BRDF basic functionality
 */
bool testDisneyBRDFBasic() {
    std::cout << "\n=== Testing Disney BRDF Basic Functionality ===" << std::endl;
    
    try {
        // Create Disney BRDF with default parameters
        DisneyBRDF brdf;
        std::cout << "✅ Disney BRDF created successfully" << std::endl;
        
        // Test parameter setting
        DisneyBRDFParams params = DisneyMaterialPresets::createMetal(Color3(0.8f, 0.6f, 0.4f));
        brdf.setParameters(params);
        std::cout << "✅ Disney BRDF parameters set successfully" << std::endl;
        
        // Test BRDF evaluation
        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);
        Vec3 wi(0.5f, 0.5f, 0.707f);
        wi = wi.normalized();
        
        Color3 f = brdf.eval(wo, wi, n);
        std::cout << "✅ BRDF evaluation: f = (" << f.r << ", " << f.g << ", " << f.b << ")" << std::endl;
        
        // Test BRDF sampling
        RandomSampler sampler;
        Vec3 sampledWi;
        float pdf;
        Color3 sampledF = brdf.sample(wo, n, sampler, sampledWi, pdf);
        std::cout << "✅ BRDF sampling: f = (" << sampledF.r << ", " << sampledF.g << ", " << sampledF.b << "), pdf = " << pdf << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "❌ Error: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test PBR Material presets
 */
bool testPBRMaterialPresets() {
    std::cout << "\n=== Testing PBR Material Presets ===" << std::endl;
    
    std::vector<std::string> presets = {"plastic", "metal", "glass", "wood", "fabric", "skin", "ceramic", "rubber"};
    
    for (const auto& preset : presets) {
        try {
            auto material = PBRMaterial::createPreset(preset, Color3(0.7f, 0.5f, 0.3f));
            
            // Test energy conservation
            bool energyOK = material->validateEnergyConservation();
            
            // Test basic functionality
            Intersection isect;
            isect.n = Vec3(0, 0, 1);
            isect.p = Vec3(0, 0, 0);
            
            Vec3 wo(0, 0, 1);
            RandomSampler sampler;
            
            BSDFSample sample = material->sample(isect, wo, sampler);
            
            std::cout << "✅ " << preset << " preset: Energy=" << (energyOK ? "OK" : "FAIL") 
                      << ", Sample valid=" << (sample.isValid() ? "OK" : "FAIL") << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "❌ " << preset << " preset failed: " << e.what() << std::endl;
            return false;
        }
    }
    
    return true;
}

/**
 * @brief Test Disney BRDF energy conservation
 */
bool testEnergyConservation() {
    std::cout << "\n=== Testing Energy Conservation ===" << std::endl;
    
    DisneyBRDF brdf;
    RandomSampler sampler;
    
    // Test different material configurations
    std::vector<std::pair<std::string, DisneyBRDFParams>> testMaterials = {
        {"Plastic", DisneyMaterialPresets::createPlastic()},
        {"Metal", DisneyMaterialPresets::createMetal()},
        {"Wood", DisneyMaterialPresets::createWood()},
        {"Fabric", DisneyMaterialPresets::createFabric()}
    };
    
    for (const auto& [name, params] : testMaterials) {
        brdf.setParameters(params);
        
        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);
        
        // Monte Carlo integration
        const int numSamples = 1000;
        Color3 totalReflectance(0.0f);
        
        for (int i = 0; i < numSamples; ++i) {
            Vec3 wi;
            float pdf;
            Color3 f = brdf.sample(wo, n, sampler, wi, pdf);
            
            if (pdf > 0.0f && wi.dot(n) > 0.0f) {
                totalReflectance += f * wi.dot(n) / pdf;
            }
        }
        
        totalReflectance = totalReflectance / float(numSamples);
        float maxReflectance = std::max({totalReflectance.r, totalReflectance.g, totalReflectance.b});
        
        bool energyOK = maxReflectance <= 1.1f; // Allow some Monte Carlo error
        std::cout << "✅ " << name << ": Max reflectance = " << maxReflectance 
                  << " (" << (energyOK ? "OK" : "FAIL") << ")" << std::endl;
    }
    
    return true;
}

/**
 * @brief Create a simple test render with PBR materials
 */
bool testPBRRendering() {
    std::cout << "\n=== Testing PBR Rendering ===" << std::endl;
    
    try {
        // Create a simple test image with different PBR materials
        const int width = 256;
        const int height = 256;
        Image image(width, height);
        
        // Create different PBR materials
        auto plasticMaterial = PBRMaterial::createPreset("plastic", Color3(0.8f, 0.2f, 0.2f));
        auto metalMaterial = PBRMaterial::createPreset("metal", Color3(0.8f, 0.6f, 0.4f));
        auto woodMaterial = PBRMaterial::createPreset("wood", Color3(0.6f, 0.4f, 0.2f));
        auto fabricMaterial = PBRMaterial::createPreset("fabric", Color3(0.2f, 0.4f, 0.8f));
        
        // Simple test pattern with different materials
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                Color3 color;
                
                // Divide image into quadrants
                if (x < width/2 && y < height/2) {
                    // Top-left: Plastic (red)
                    color = plasticMaterial->getBaseColor();
                } else if (x >= width/2 && y < height/2) {
                    // Top-right: Metal (gold)
                    color = metalMaterial->getBaseColor();
                } else if (x < width/2 && y >= height/2) {
                    // Bottom-left: Wood (brown)
                    color = woodMaterial->getBaseColor();
                } else {
                    // Bottom-right: Fabric (blue)
                    color = fabricMaterial->getBaseColor();
                }
                
                // Add some variation based on position
                float factor = 0.5f + 0.5f * std::sin(x * 0.1f) * std::cos(y * 0.1f);
                color = color * factor;
                
                image.setPixel(x, y, color);
            }
        }
        
        // Save the test image
        if (image.savePNG("disney_brdf_test.png")) {
            std::cout << "✅ PBR test image saved: disney_brdf_test.png" << std::endl;
            return true;
        } else {
            std::cout << "❌ Failed to save PBR test image" << std::endl;
            return false;
        }
        
    } catch (const std::exception& e) {
        std::cout << "❌ PBR rendering test failed: " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Main test function
 */
int main() {
    std::cout << "PhotonRender Disney BRDF Test Suite" << std::endl;
    std::cout << "====================================" << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    bool allPassed = true;
    
    // Run all tests
    allPassed &= testDisneyBRDFBasic();
    allPassed &= testPBRMaterialPresets();
    allPassed &= testEnergyConservation();
    allPassed &= testPBRRendering();
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    std::cout << "\n=== Test Results ===" << std::endl;
    if (allPassed) {
        std::cout << "🎉 All Disney BRDF tests PASSED!" << std::endl;
    } else {
        std::cout << "❌ Some Disney BRDF tests FAILED!" << std::endl;
    }
    std::cout << "Total test time: " << duration.count() << "ms" << std::endl;
    
    return allPassed ? 0 : 1;
}
