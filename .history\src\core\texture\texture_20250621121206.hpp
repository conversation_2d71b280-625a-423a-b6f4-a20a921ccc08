// src/core/texture/texture.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Texture system for PBR materials

#pragma once

#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"
#include "../common.hpp"
#include <memory>
#include <string>
#include <vector>

namespace photon {

/**
 * @brief Texture filtering modes
 */
enum class TextureFilter {
    NEAREST,        // Nearest neighbor
    BILINEAR,       // Bilinear interpolation
    TRILINEAR,      // Trilinear interpolation (with mipmaps)
    ANISOTROPIC     // Anisotropic filtering
};

/**
 * @brief Texture wrap modes
 */
enum class TextureWrap {
    REPEAT,         // Repeat texture
    CLAMP,          // Clamp to edge
    MIRROR,         // Mirror repeat
    BORDER          // Use border color
};

/**
 * @brief Texture format
 */
enum class TextureFormat {
    RGB8,           // 8-bit RGB
    RGBA8,          // 8-bit RGBA
    RGB16F,         // 16-bit float RGB
    RGBA16F,        // 16-bit float RGBA
    RGB32F,         // 32-bit float RGB
    RGBA32F,        // 32-bit float RGBA
    R8,             // 8-bit single channel
    R16F,           // 16-bit float single channel
    R32F            // 32-bit float single channel
};

/**
 * @brief Abstract base texture class
 */
class Texture {
public:
    /**
     * @brief Constructor
     */
    Texture() = default;
    
    /**
     * @brief Virtual destructor
     */
    virtual ~Texture() = default;
    
    /**
     * @brief Sample texture at UV coordinates
     * @param uv UV coordinates [0,1]
     * @return Sampled color
     */
    virtual Color3 sample(const Vec2& uv) const = 0;
    
    /**
     * @brief Sample texture with derivatives for filtering
     * @param uv UV coordinates
     * @param dudx Partial derivative du/dx
     * @param dudy Partial derivative du/dy
     * @param dvdx Partial derivative dv/dx
     * @param dvdy Partial derivative dv/dy
     * @return Sampled color
     */
    virtual Color3 sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const {
        // Default implementation ignores derivatives
        return sample(uv);
    }
    
    /**
     * @brief Sample single channel (for roughness, metallic, etc.)
     * @param uv UV coordinates
     * @return Single channel value
     */
    virtual float sampleFloat(const Vec2& uv) const {
        Color3 color = sample(uv);
        return color.luminance(); // Use luminance as default
    }
    
    /**
     * @brief Get texture width
     */
    virtual int getWidth() const = 0;
    
    /**
     * @brief Get texture height
     */
    virtual int getHeight() const = 0;
    
    /**
     * @brief Get texture format
     */
    virtual TextureFormat getFormat() const = 0;
    
    /**
     * @brief Check if texture has alpha channel
     */
    virtual bool hasAlpha() const = 0;
    
    /**
     * @brief Set texture filtering mode
     */
    virtual void setFilter(TextureFilter filter) { m_filter = filter; }
    
    /**
     * @brief Set texture wrap mode
     */
    virtual void setWrap(TextureWrap wrap) { m_wrap = wrap; }
    
    /**
     * @brief Get texture name/path
     */
    const std::string& getName() const { return m_name; }
    
    /**
     * @brief Set texture name/path
     */
    void setName(const std::string& name) { m_name = name; }

protected:
    std::string m_name;
    TextureFilter m_filter = TextureFilter::BILINEAR;
    TextureWrap m_wrap = TextureWrap::REPEAT;
    
    /**
     * @brief Apply wrap mode to UV coordinate
     */
    float applyWrap(float coord) const;
    
    /**
     * @brief Apply wrap mode to UV coordinates
     */
    Vec2 applyWrap(const Vec2& uv) const;
};

/**
 * @brief Image-based texture
 */
class ImageTexture : public Texture {
public:
    /**
     * @brief Constructor
     */
    ImageTexture() = default;
    
    /**
     * @brief Constructor with image data
     * @param width Image width
     * @param height Image height
     * @param channels Number of channels (1, 3, or 4)
     * @param data Image data (float array)
     */
    ImageTexture(int width, int height, int channels, const float* data);
    
    /**
     * @brief Load from file
     * @param filename Image file path
     * @return True if successful
     */
    bool loadFromFile(const std::string& filename);
    
    /**
     * @brief Create from color
     * @param color Solid color
     * @return Texture with solid color
     */
    static std::shared_ptr<ImageTexture> createSolid(const Color3& color);
    
    /**
     * @brief Create from single value (for roughness, metallic, etc.)
     * @param value Single channel value
     * @return Texture with solid value
     */
    static std::shared_ptr<ImageTexture> createSolid(float value);
    
    // Texture interface implementation
    Color3 sample(const Vec2& uv) const override;
    Color3 sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const override;
    float sampleFloat(const Vec2& uv) const override;
    
    int getWidth() const override { return m_width; }
    int getHeight() const override { return m_height; }
    TextureFormat getFormat() const override { return m_format; }
    bool hasAlpha() const override { return m_channels == 4; }
    
    /**
     * @brief Get raw pixel data
     */
    const std::vector<float>& getData() const { return m_data; }
    
    /**
     * @brief Set gamma correction
     */
    void setGamma(float gamma) { m_gamma = gamma; }

private:
    int m_width = 0;
    int m_height = 0;
    int m_channels = 0;
    TextureFormat m_format = TextureFormat::RGB8;
    std::vector<float> m_data;
    float m_gamma = 2.2f;
    
    /**
     * @brief Sample pixel at integer coordinates
     */
    Color3 samplePixel(int x, int y) const;
    
    /**
     * @brief Bilinear interpolation
     */
    Color3 sampleBilinear(float u, float v) const;
    
    /**
     * @brief Apply gamma correction
     */
    Color3 applyGamma(const Color3& color) const;
};

/**
 * @brief Procedural texture base class
 */
class ProceduralTexture : public Texture {
public:
    /**
     * @brief Constructor
     */
    ProceduralTexture() = default;
    
    // Default implementations
    int getWidth() const override { return 1024; }
    int getHeight() const override { return 1024; }
    TextureFormat getFormat() const override { return TextureFormat::RGB32F; }
    bool hasAlpha() const override { return false; }
};

/**
 * @brief Checkerboard texture
 */
class CheckerboardTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor
     * @param color1 First color
     * @param color2 Second color
     * @param scale Checkerboard scale
     */
    CheckerboardTexture(const Color3& color1 = Color3(1.0f), 
                       const Color3& color2 = Color3(0.0f), 
                       float scale = 8.0f);
    
    Color3 sample(const Vec2& uv) const override;

private:
    Color3 m_color1, m_color2;
    float m_scale;
};

/**
 * @brief Noise types for procedural texture generation
 */
enum class NoiseType {
    PERLIN,     ///< Classic Perlin noise (smooth gradients)
    SIMPLEX,    ///< Simplex noise (better performance, less artifacts)
    WORLEY      ///< Worley noise (cellular patterns)
};

/**
 * @brief Fractal noise parameters for multi-octave generation
 */
struct FractalParams {
    int octaves = 4;           ///< Number of octaves
    float lacunarity = 2.0f;   ///< Frequency multiplier between octaves
    float persistence = 0.5f;  ///< Amplitude multiplier between octaves
    float scale = 1.0f;        ///< Overall scale factor

    /**
     * @brief Default fractal parameters
     */
    static FractalParams defaultParams() {
        return FractalParams{4, 2.0f, 0.5f, 1.0f};
    }
};

/**
 * @brief Advanced noise texture with multiple algorithms
 */
class NoiseTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor with basic parameters
     * @param frequency Base noise frequency
     * @param amplitude Base noise amplitude
     * @param octaves Number of fractal octaves
     */
    NoiseTexture(float frequency = 4.0f, float amplitude = 1.0f, int octaves = 4);

    /**
     * @brief Constructor with advanced parameters
     * @param type Noise algorithm type
     * @param frequency Base frequency
     * @param amplitude Base amplitude
     * @param fractal Fractal noise parameters
     */
    NoiseTexture(NoiseType type, float frequency, float amplitude, const FractalParams& fractal);

    /**
     * @brief Set noise type
     */
    void setNoiseType(NoiseType type) { m_noiseType = type; }

    /**
     * @brief Set fractal parameters
     */
    void setFractalParams(const FractalParams& params) { m_fractal = params; }

    /**
     * @brief Set noise seed for variation
     */
    void setSeed(int seed) { m_seed = seed; }

    // Texture interface
    Color3 sample(const Vec2& uv) const override;
    float sampleFloat(const Vec2& uv) const override;

private:
    NoiseType m_noiseType = NoiseType::PERLIN;
    float m_frequency;
    float m_amplitude;
    FractalParams m_fractal;
    int m_seed = 0;

    /**
     * @brief Generate fractal noise using specified algorithm
     */
    float generateFractalNoise(float x, float y) const;

    /**
     * @brief Perlin noise implementation (improved)
     */
    float perlinNoise(float x, float y) const;

    /**
     * @brief Simplex noise implementation
     */
    float simplexNoise(float x, float y) const;

    /**
     * @brief Worley noise implementation (cellular)
     */
    float worleyNoise(float x, float y) const;

    /**
     * @brief Hash function for noise generation
     */
    float hash(float x, float y) const;

    /**
     * @brief Smooth interpolation function
     */
    float smoothstep(float t) const;

    /**
     * @brief Linear interpolation
     */
    float lerp(float a, float b, float t) const;
};

/**
 * @brief Pattern types for geometric pattern generation
 */
enum class PatternType {
    CHECKERBOARD,   ///< Checkerboard/chess pattern
    STRIPES,        ///< Linear stripe pattern
    DOTS,           ///< Dot/circle pattern
    GRID            ///< Grid line pattern
};

/**
 * @brief Pattern parameters for geometric pattern generation
 */
struct PatternParams {
    float scale = 1.0f;        ///< Overall pattern scale
    float rotation = 0.0f;     ///< Pattern rotation in radians
    Vec2 offset = Vec2(0.0f);  ///< Pattern offset
    float lineWidth = 0.1f;    ///< Line width for grid patterns
    float dotSize = 0.3f;      ///< Dot size for dot patterns
    bool antiAlias = true;     ///< Enable anti-aliasing

    /**
     * @brief Default pattern parameters
     */
    static PatternParams defaultParams() {
        return PatternParams{1.0f, 0.0f, Vec2(0.0f), 0.1f, 0.3f, true};
    }
};

/**
 * @brief Pattern texture for geometric pattern generation
 */
class PatternTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor with basic parameters
     * @param type Pattern type
     * @param scale Pattern scale
     * @param color1 Primary color
     * @param color2 Secondary color
     */
    PatternTexture(PatternType type, float scale = 1.0f,
                   const Color3& color1 = Color3(1.0f),
                   const Color3& color2 = Color3(0.0f));

    /**
     * @brief Constructor with advanced parameters
     * @param type Pattern type
     * @param params Pattern parameters
     * @param color1 Primary color
     * @param color2 Secondary color
     */
    PatternTexture(PatternType type, const PatternParams& params,
                   const Color3& color1 = Color3(1.0f),
                   const Color3& color2 = Color3(0.0f));

    /**
     * @brief Set pattern type
     */
    void setPatternType(PatternType type) { m_patternType = type; }

    /**
     * @brief Set pattern parameters
     */
    void setPatternParams(const PatternParams& params) { m_params = params; }

    /**
     * @brief Set pattern colors
     */
    void setColors(const Color3& color1, const Color3& color2) {
        m_color1 = color1;
        m_color2 = color2;
    }

    // Texture interface
    Color3 sample(const Vec2& uv) const override;
    float sampleFloat(const Vec2& uv) const override;

private:
    PatternType m_patternType;
    PatternParams m_params;
    Color3 m_color1;
    Color3 m_color2;

    /**
     * @brief Generate pattern value at given coordinates
     */
    float generatePattern(float x, float y) const;

    /**
     * @brief Checkerboard pattern implementation
     */
    float checkerboardPattern(float x, float y) const;

    /**
     * @brief Stripe pattern implementation
     */
    float stripePattern(float x, float y) const;

    /**
     * @brief Dot pattern implementation
     */
    float dotPattern(float x, float y) const;

    /**
     * @brief Grid pattern implementation
     */
    float gridPattern(float x, float y) const;

    /**
     * @brief Apply rotation transformation
     */
    Vec2 applyRotation(const Vec2& uv) const;

    /**
     * @brief Smooth step function for anti-aliasing
     */
    float smoothStep(float edge0, float edge1, float x) const;
};

} // namespace photon
