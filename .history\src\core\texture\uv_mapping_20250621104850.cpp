// src/core/texture/uv_mapping.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// UV Mapping Enhancement - Implementation

#include "uv_mapping.hpp"
#include <algorithm>
#include <cmath>

namespace photon {

// UVTransform implementation

Vec2 UVTransform::transform(const Vec2& uv) const {
    Vec2 result = uv;
    
    // Step 1: Apply flip
    if (flipU) result.x = 1.0f - result.x;
    if (flipV) result.y = 1.0f - result.y;
    
    // Step 2: Apply scale
    result.x *= scale.x;
    result.y *= scale.y;
    
    // Step 3: Apply rotation around center (0.5, 0.5)
    if (rotation != 0.0f) {
        float cos_r = std::cos(rotation);
        float sin_r = std::sin(rotation);
        
        // Translate to origin (center at 0.5, 0.5)
        float u = result.x - 0.5f;
        float v = result.y - 0.5f;
        
        // Rotate
        float new_u = u * cos_r - v * sin_r;
        float new_v = u * sin_r + v * cos_r;
        
        // Translate back
        result.x = new_u + 0.5f;
        result.y = new_v + 0.5f;
    }
    
    // Step 4: Apply offset
    result.x += offset.x;
    result.y += offset.y;
    
    return result;
}

Vec2 UVTransform::inverseTransform(const Vec2& uv) const {
    Vec2 result = uv;
    
    // Reverse order: offset -> rotation -> scale -> flip
    
    // Step 1: Remove offset
    result.x -= offset.x;
    result.y -= offset.y;
    
    // Step 2: Remove rotation
    if (rotation != 0.0f) {
        float cos_r = std::cos(-rotation); // Negative angle for inverse
        float sin_r = std::sin(-rotation);
        
        // Translate to origin
        float u = result.x - 0.5f;
        float v = result.y - 0.5f;
        
        // Rotate (inverse)
        float new_u = u * cos_r - v * sin_r;
        float new_v = u * sin_r + v * cos_r;
        
        // Translate back
        result.x = new_u + 0.5f;
        result.y = new_v + 0.5f;
    }
    
    // Step 3: Remove scale
    if (scale.x != 0.0f) result.x /= scale.x;
    if (scale.y != 0.0f) result.y /= scale.y;
    
    // Step 4: Remove flip
    if (flipU) result.x = 1.0f - result.x;
    if (flipV) result.y = 1.0f - result.y;
    
    return result;
}

bool UVTransform::isIdentity() const {
    return scale == Vec2(1.0f) && 
           offset == Vec2(0.0f) && 
           rotation == 0.0f && 
           !flipU && !flipV;
}

UVTransform UVTransform::combine(const UVTransform& other) const {
    // For now, simple combination - could be optimized for specific cases
    UVTransform result;
    result.scale = scale * other.scale;
    result.offset = offset + other.offset;
    result.rotation = rotation + other.rotation;
    result.flipU = flipU ^ other.flipU; // XOR for flip combination
    result.flipV = flipV ^ other.flipV;
    return result;
}

// UVMapping implementation

UVMapping::UVMapping(UVMappingMode mode, const UVTransform& transform)
    : m_mode(mode), m_transform(transform) {
}

Vec2 UVMapping::generateUV(const Vec3& position, const Vec3& normal) const {
    Vec2 uv;
    
    switch (m_mode) {
        case UVMappingMode::PLANAR_XY:
            uv = generatePlanarUV(position, 2); // Z axis (XY plane)
            break;
        case UVMappingMode::PLANAR_XZ:
            uv = generatePlanarUV(position, 1); // Y axis (XZ plane)
            break;
        case UVMappingMode::PLANAR_YZ:
            uv = generatePlanarUV(position, 0); // X axis (YZ plane)
            break;
        case UVMappingMode::CYLINDRICAL_Y:
            uv = generateCylindricalUV(position, 1); // Y axis
            break;
        case UVMappingMode::CYLINDRICAL_X:
            uv = generateCylindricalUV(position, 0); // X axis
            break;
        case UVMappingMode::CYLINDRICAL_Z:
            uv = generateCylindricalUV(position, 2); // Z axis
            break;
        case UVMappingMode::SPHERICAL:
            uv = generateSphericalUV(position);
            break;
        case UVMappingMode::CUBIC:
            uv = generateCubicUV(position, normal);
            break;
        case UVMappingMode::TRIPLANAR:
            uv = generateTriplanarUV(position, normal);
            break;
        case UVMappingMode::VERTEX_UV:
        default:
            // This should not be called for VERTEX_UV mode
            // Return default UV coordinates
            uv = Vec2(0.0f, 0.0f);
            break;
    }
    
    // Apply transformation
    return m_transform.transform(uv);
}

Vec2 UVMapping::transformUV(const Vec2& uv) const {
    return m_transform.transform(uv);
}

void UVMapping::setScale(float scaleU, float scaleV) {
    m_transform.scale = Vec2(scaleU, scaleV);
}

void UVMapping::setOffset(float offsetU, float offsetV) {
    m_transform.offset = Vec2(offsetU, offsetV);
}

void UVMapping::setRotation(float angleRadians) {
    m_transform.rotation = angleRadians;
}

const char* UVMapping::getMappingModeName() const {
    switch (m_mode) {
        case UVMappingMode::VERTEX_UV: return "Vertex UV";
        case UVMappingMode::PLANAR_XY: return "Planar XY";
        case UVMappingMode::PLANAR_XZ: return "Planar XZ";
        case UVMappingMode::PLANAR_YZ: return "Planar YZ";
        case UVMappingMode::CYLINDRICAL_Y: return "Cylindrical Y";
        case UVMappingMode::CYLINDRICAL_X: return "Cylindrical X";
        case UVMappingMode::CYLINDRICAL_Z: return "Cylindrical Z";
        case UVMappingMode::SPHERICAL: return "Spherical";
        case UVMappingMode::CUBIC: return "Cubic";
        case UVMappingMode::TRIPLANAR: return "Triplanar";
        default: return "Unknown";
    }
}

Vec2 UVMapping::generatePlanarUV(const Vec3& position, int axis) const {
    Vec2 uv;
    
    switch (axis) {
        case 0: // X axis (YZ plane)
            uv.x = position.y;
            uv.y = position.z;
            break;
        case 1: // Y axis (XZ plane)
            uv.x = position.x;
            uv.y = position.z;
            break;
        case 2: // Z axis (XY plane)
        default:
            uv.x = position.x;
            uv.y = position.y;
            break;
    }
    
    // Normalize to [0,1] range (assuming object is roughly in [-1,1] range)
    uv.x = (uv.x + 1.0f) * 0.5f;
    uv.y = (uv.y + 1.0f) * 0.5f;
    
    return uv;
}

Vec2 UVMapping::generateCylindricalUV(const Vec3& position, int axis) const {
    Vec2 uv;
    
    switch (axis) {
        case 0: // X axis
            {
                float angle = std::atan2(position.z, position.y);
                uv.x = (angle + M_PI) / (2.0f * M_PI); // [0,1]
                uv.y = (position.x + 1.0f) * 0.5f;     // [0,1]
            }
            break;
        case 1: // Y axis
        default:
            {
                float angle = std::atan2(position.z, position.x);
                uv.x = (angle + M_PI) / (2.0f * M_PI); // [0,1]
                uv.y = (position.y + 1.0f) * 0.5f;     // [0,1]
            }
            break;
        case 2: // Z axis
            {
                float angle = std::atan2(position.y, position.x);
                uv.x = (angle + M_PI) / (2.0f * M_PI); // [0,1]
                uv.y = (position.z + 1.0f) * 0.5f;     // [0,1]
            }
            break;
    }
    
    return uv;
}

Vec2 UVMapping::generateSphericalUV(const Vec3& position) const {
    Vec3 normalized = position.normalized();
    
    // Spherical coordinates
    float theta = std::atan2(normalized.z, normalized.x); // Azimuth angle
    float phi = std::acos(std::clamp(normalized.y, -1.0f, 1.0f)); // Polar angle
    
    Vec2 uv;
    uv.x = (theta + M_PI) / (2.0f * M_PI); // [0,1]
    uv.y = phi / M_PI;                     // [0,1]
    
    return uv;
}

Vec2 UVMapping::generateCubicUV(const Vec3& position, const Vec3& normal) const {
    // Determine dominant axis from normal
    Vec3 absNormal = Vec3(std::abs(normal.x), std::abs(normal.y), std::abs(normal.z));

    Vec2 uv;

    if (absNormal.x >= absNormal.y && absNormal.x >= absNormal.z) {
        // X-dominant face
        if (normal.x > 0) {
            // +X face
            uv.x = (-position.z + 1.0f) * 0.5f;
            uv.y = (position.y + 1.0f) * 0.5f;
        } else {
            // -X face
            uv.x = (position.z + 1.0f) * 0.5f;
            uv.y = (position.y + 1.0f) * 0.5f;
        }
    } else if (absNormal.y >= absNormal.z) {
        // Y-dominant face
        if (normal.y > 0) {
            // +Y face
            uv.x = (position.x + 1.0f) * 0.5f;
            uv.y = (-position.z + 1.0f) * 0.5f;
        } else {
            // -Y face
            uv.x = (position.x + 1.0f) * 0.5f;
            uv.y = (position.z + 1.0f) * 0.5f;
        }
    } else {
        // Z-dominant face
        if (normal.z > 0) {
            // +Z face
            uv.x = (position.x + 1.0f) * 0.5f;
            uv.y = (position.y + 1.0f) * 0.5f;
        } else {
            // -Z face
            uv.x = (-position.x + 1.0f) * 0.5f;
            uv.y = (position.y + 1.0f) * 0.5f;
        }
    }

    return uv;
}

Vec2 UVMapping::generateTriplanarUV(const Vec3& position, const Vec3& normal) const {
    // Triplanar mapping blends three planar projections based on normal
    Vec3 absNormal = Vec3(std::abs(normal.x), std::abs(normal.y), std::abs(normal.z));

    // Normalize weights
    float totalWeight = absNormal.x + absNormal.y + absNormal.z;
    if (totalWeight > 0.0f) {
        absNormal /= totalWeight;
    }

    // Generate UV for each plane
    Vec2 uvX = generatePlanarUV(position, 0); // YZ plane
    Vec2 uvY = generatePlanarUV(position, 1); // XZ plane
    Vec2 uvZ = generatePlanarUV(position, 2); // XY plane

    // Blend based on normal weights
    Vec2 uv = uvX * absNormal.x + uvY * absNormal.y + uvZ * absNormal.z;

    return uv;
}

} // namespace photon
