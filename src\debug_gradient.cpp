// src/debug_gradient.cpp
// Debug per capire il problema del gradiente

#include <iostream>
#include <iomanip>
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/color3.hpp"

using namespace photon;

int main() {
    std::cout << "=== Debug Linear Gradient ===\n\n";
    
    // Create basic linear gradient
    Color3 red(1.0f, 0.0f, 0.0f);
    Color3 blue(0.0f, 0.0f, 1.0f);
    
    GradientTexture gradient(GradientType::LINEAR, red, blue);
    
    std::cout << "Testing basic gradient from red to blue\n";
    std::cout << "Expected: Start=Red(1,0,0), End=Blue(0,0,1)\n\n";
    
    // Test sampling at various coordinates
    Vec2 testCoords[] = {
        Vec2(0.0f, 0.5f),   // Start
        Vec2(0.25f, 0.5f),  // Quarter
        Vec2(0.5f, 0.5f),   // Middle
        Vec2(0.75f, 0.5f),  // Three quarters
        Vec2(1.0f, 0.5f)    // End
    };
    
    for (const auto& coord : testCoords) {
        Color3 color = gradient.sample(coord);
        float value = gradient.sampleFloat(coord);

        std::cout << "UV(" << std::fixed << std::setprecision(2) << coord.x << ", " << coord.y << "): ";
        std::cout << "RGB(" << std::setprecision(3) << color.r << ", " << color.g << ", " << color.b << ") ";
        std::cout << "Value=" << value;

        // Expected analysis
        if (coord.x == 0.0f) {
            std::cout << "  ✅ Should be RED";
            if (color.r < 0.8f || color.b > 0.2f) std::cout << " ❌ WRONG!";
        } else if (coord.x == 1.0f) {
            std::cout << "  ✅ Should be BLUE";
            if (color.b < 0.8f || color.r > 0.2f) std::cout << " ❌ WRONG!";
        } else if (coord.x == 0.5f) {
            std::cout << "  ✅ Should be PURPLE";
            if (color.r < 0.3f || color.b < 0.3f) std::cout << " ❌ WRONG!";
        }

        std::cout << std::endl;
    }

    std::cout << "\n=== Debug Gradient Values ===\n";
    std::cout << "Testing gradient calculation step by step:\n";

    // Manual calculation for UV(1.0, 0.5)
    std::cout << "\nManual calculation for UV(1.0, 0.5):\n";
    std::cout << "Start: (0.0, 0.5), End: (1.0, 0.5)\n";
    std::cout << "Direction: (1.0, 0.0)\n";
    std::cout << "DirLength: 1.0\n";
    std::cout << "RelativePos: (1.0, 0.0)\n";
    std::cout << "Projection: 1.0 * 1.0 + 0.0 * 0.0 = 1.0\n";
    std::cout << "t = 1.0 / (1.0 * 1.0) = 1.0\n";
    std::cout << "Expected t = 1.0 -> Should give BLUE\n";
    
    std::cout << "\n=== Testing Color Stops ===\n";
    
    GradientTexture gradient2(GradientType::LINEAR);
    gradient2.clearColorStops();
    gradient2.addColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)); // Red
    gradient2.addColorStop(0.5f, Color3(0.0f, 1.0f, 0.0f)); // Green
    gradient2.addColorStop(1.0f, Color3(0.0f, 0.0f, 1.0f)); // Blue
    
    std::cout << "Testing 3-color gradient: Red -> Green -> Blue\n";
    
    for (const auto& coord : testCoords) {
        Color3 color = gradient2.sample(coord);
        
        std::cout << "UV(" << std::fixed << std::setprecision(2) << coord.x << ", " << coord.y << "): ";
        std::cout << "RGB(" << std::setprecision(3) << color.r << ", " << color.g << ", " << color.b << ")" << std::endl;
    }
    
    return 0;
}
