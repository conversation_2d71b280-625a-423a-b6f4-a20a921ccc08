// src/core/texture/texture.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Texture system for PBR materials

#pragma once

#include "../math/vec3.hpp"
#include "../math/color3.hpp"
#include "../common.hpp"
#include <memory>
#include <string>
#include <vector>

namespace photon {

// Forward declarations
struct Vec2;

/**
 * @brief Texture filtering modes
 */
enum class TextureFilter {
    NEAREST,        // Nearest neighbor
    BILINEAR,       // Bilinear interpolation
    TRILINEAR,      // Trilinear interpolation (with mipmaps)
    ANISOTROPIC     // Anisotropic filtering
};

/**
 * @brief Texture wrap modes
 */
enum class TextureWrap {
    REPEAT,         // Repeat texture
    CLAMP,          // Clamp to edge
    MIRROR,         // Mirror repeat
    BORDER          // Use border color
};

/**
 * @brief Texture format
 */
enum class TextureFormat {
    RGB8,           // 8-bit RGB
    RGBA8,          // 8-bit RGBA
    RGB16F,         // 16-bit float RGB
    RGBA16F,        // 16-bit float RGBA
    RGB32F,         // 32-bit float RGB
    RGBA32F,        // 32-bit float RGBA
    R8,             // 8-bit single channel
    R16F,           // 16-bit float single channel
    R32F            // 32-bit float single channel
};

/**
 * @brief Abstract base texture class
 */
class Texture {
public:
    /**
     * @brief Constructor
     */
    Texture() = default;
    
    /**
     * @brief Virtual destructor
     */
    virtual ~Texture() = default;
    
    /**
     * @brief Sample texture at UV coordinates
     * @param uv UV coordinates [0,1]
     * @return Sampled color
     */
    virtual Color3 sample(const Vec2& uv) const = 0;
    
    /**
     * @brief Sample texture with derivatives for filtering
     * @param uv UV coordinates
     * @param dudx Partial derivative du/dx
     * @param dudy Partial derivative du/dy
     * @param dvdx Partial derivative dv/dx
     * @param dvdy Partial derivative dv/dy
     * @return Sampled color
     */
    virtual Color3 sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const {
        // Default implementation ignores derivatives
        return sample(uv);
    }
    
    /**
     * @brief Sample single channel (for roughness, metallic, etc.)
     * @param uv UV coordinates
     * @return Single channel value
     */
    virtual float sampleFloat(const Vec2& uv) const {
        Color3 color = sample(uv);
        return color.luminance(); // Use luminance as default
    }
    
    /**
     * @brief Get texture width
     */
    virtual int getWidth() const = 0;
    
    /**
     * @brief Get texture height
     */
    virtual int getHeight() const = 0;
    
    /**
     * @brief Get texture format
     */
    virtual TextureFormat getFormat() const = 0;
    
    /**
     * @brief Check if texture has alpha channel
     */
    virtual bool hasAlpha() const = 0;
    
    /**
     * @brief Set texture filtering mode
     */
    virtual void setFilter(TextureFilter filter) { m_filter = filter; }
    
    /**
     * @brief Set texture wrap mode
     */
    virtual void setWrap(TextureWrap wrap) { m_wrap = wrap; }
    
    /**
     * @brief Get texture name/path
     */
    const std::string& getName() const { return m_name; }
    
    /**
     * @brief Set texture name/path
     */
    void setName(const std::string& name) { m_name = name; }

protected:
    std::string m_name;
    TextureFilter m_filter = TextureFilter::BILINEAR;
    TextureWrap m_wrap = TextureWrap::REPEAT;
    
    /**
     * @brief Apply wrap mode to UV coordinate
     */
    float applyWrap(float coord) const;
    
    /**
     * @brief Apply wrap mode to UV coordinates
     */
    Vec2 applyWrap(const Vec2& uv) const;
};

/**
 * @brief Image-based texture
 */
class ImageTexture : public Texture {
public:
    /**
     * @brief Constructor
     */
    ImageTexture() = default;
    
    /**
     * @brief Constructor with image data
     * @param width Image width
     * @param height Image height
     * @param channels Number of channels (1, 3, or 4)
     * @param data Image data (float array)
     */
    ImageTexture(int width, int height, int channels, const float* data);
    
    /**
     * @brief Load from file
     * @param filename Image file path
     * @return True if successful
     */
    bool loadFromFile(const std::string& filename);
    
    /**
     * @brief Create from color
     * @param color Solid color
     * @return Texture with solid color
     */
    static std::shared_ptr<ImageTexture> createSolid(const Color3& color);
    
    /**
     * @brief Create from single value (for roughness, metallic, etc.)
     * @param value Single channel value
     * @return Texture with solid value
     */
    static std::shared_ptr<ImageTexture> createSolid(float value);
    
    // Texture interface implementation
    Color3 sample(const Vec2& uv) const override;
    Color3 sample(const Vec2& uv, float dudx, float dudy, float dvdx, float dvdy) const override;
    float sampleFloat(const Vec2& uv) const override;
    
    int getWidth() const override { return m_width; }
    int getHeight() const override { return m_height; }
    TextureFormat getFormat() const override { return m_format; }
    bool hasAlpha() const override { return m_channels == 4; }
    
    /**
     * @brief Get raw pixel data
     */
    const std::vector<float>& getData() const { return m_data; }
    
    /**
     * @brief Set gamma correction
     */
    void setGamma(float gamma) { m_gamma = gamma; }

private:
    int m_width = 0;
    int m_height = 0;
    int m_channels = 0;
    TextureFormat m_format = TextureFormat::RGB8;
    std::vector<float> m_data;
    float m_gamma = 2.2f;
    
    /**
     * @brief Sample pixel at integer coordinates
     */
    Color3 samplePixel(int x, int y) const;
    
    /**
     * @brief Bilinear interpolation
     */
    Color3 sampleBilinear(float u, float v) const;
    
    /**
     * @brief Apply gamma correction
     */
    Color3 applyGamma(const Color3& color) const;
};

/**
 * @brief Procedural texture base class
 */
class ProceduralTexture : public Texture {
public:
    /**
     * @brief Constructor
     */
    ProceduralTexture() = default;
    
    // Default implementations
    int getWidth() const override { return 1024; }
    int getHeight() const override { return 1024; }
    TextureFormat getFormat() const override { return TextureFormat::RGB32F; }
    bool hasAlpha() const override { return false; }
};

/**
 * @brief Checkerboard texture
 */
class CheckerboardTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor
     * @param color1 First color
     * @param color2 Second color
     * @param scale Checkerboard scale
     */
    CheckerboardTexture(const Color3& color1 = Color3(1.0f), 
                       const Color3& color2 = Color3(0.0f), 
                       float scale = 8.0f);
    
    Color3 sample(const Vec2& uv) const override;

private:
    Color3 m_color1, m_color2;
    float m_scale;
};

/**
 * @brief Noise texture
 */
class NoiseTexture : public ProceduralTexture {
public:
    /**
     * @brief Constructor
     * @param frequency Noise frequency
     * @param amplitude Noise amplitude
     * @param octaves Number of octaves
     */
    NoiseTexture(float frequency = 4.0f, float amplitude = 1.0f, int octaves = 4);
    
    Color3 sample(const Vec2& uv) const override;

private:
    float m_frequency;
    float m_amplitude;
    int m_octaves;
    
    /**
     * @brief Perlin noise function
     */
    float perlinNoise(float x, float y) const;
};

} // namespace photon
