// src/test_lighting_performance.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Lighting Performance Test Suite - Task 6.5 validation

#include "core/accelerator/light_bvh.hpp"
#include "core/light/light_manager.hpp"
#include "core/light/adaptive_light_sampler.hpp"
#include "core/light/light_memory_pool.hpp"
#include "core/scene/scene.hpp"
#include "core/scene/light.hpp"
#include "core/light/spot_light.hpp"
#include "core/light/rectangle_light.hpp"
#include "core/light/disk_light.hpp"
#include "core/light/sphere_light.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/math/vec3.hpp"
#include "core/math/color3.hpp"
#include <iostream>
#include <chrono>
#include <vector>
#include <memory>
#include <random>
#include <cassert>

using namespace photon;

/**
 * @brief Performance test results
 */
struct PerformanceTestResult {
    std::string testName;
    double executionTime;       // milliseconds
    size_t memoryUsed;         // bytes
    bool passed;               // did test pass?
    std::string details;       // additional details
    
    PerformanceTestResult(const std::string& name) 
        : testName(name), executionTime(0.0), memoryUsed(0), passed(false) {}
};

/**
 * @brief Performance test suite
 */
class LightingPerformanceTestSuite {
public:
    LightingPerformanceTestSuite() : m_totalTests(0), m_passedTests(0) {}
    
    void runAllTests() {
        std::cout << "\n=== PhotonRender Lighting Performance Test Suite ===" << std::endl;
        std::cout << "Task 6: Lighting Performance Optimization Validation\n" << std::endl;
        
        // Test 1: Light BVH Performance
        testLightBVHPerformance();
        
        // Test 2: Light Manager Culling Performance
        testLightManagerCulling();
        
        // Test 3: Adaptive Sampling Performance
        testAdaptiveSamplingPerformance();
        
        // Test 4: Memory Pool Performance
        testMemoryPoolPerformance();
        
        // Test 5: Scalability Test
        testScalabilityWithManyLights();
        
        // Test 6: Integration Performance
        testIntegrationPerformance();
        
        printSummary();
    }

private:
    std::vector<PerformanceTestResult> m_results;
    int m_totalTests;
    int m_passedTests;
    
    void testLightBVHPerformance() {
        PerformanceTestResult result("Light BVH Performance");
        std::cout << "Test 1: " << result.testName << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            // Create test lights
            std::vector<std::shared_ptr<Light>> lights = createTestLights(1000);
            
            // Build BVH
            LightBVH bvh;
            auto buildStart = std::chrono::high_resolution_clock::now();
            bvh.build(lights, 4);
            auto buildEnd = std::chrono::high_resolution_clock::now();
            
            double buildTime = std::chrono::duration<double, std::milli>(buildEnd - buildStart).count();
            
            // Test queries
            auto queryStart = std::chrono::high_resolution_clock::now();
            
            int numQueries = 1000;
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<float> dis(-50.0f, 50.0f);
            
            for (int i = 0; i < numQueries; i++) {
                Point3 queryPos(dis(gen), dis(gen), dis(gen));
                LightQuery query(queryPos, 25.0f, 0.01f);
                auto queryResults = bvh.queryLights(query);
            }
            
            auto queryEnd = std::chrono::high_resolution_clock::now();
            double queryTime = std::chrono::duration<double, std::milli>(queryEnd - queryStart).count();
            
            // Get statistics
            auto stats = bvh.getStatistics();
            
            result.passed = true;
            result.details = "Build: " + std::to_string(buildTime) + "ms, " +
                           "Query: " + std::to_string(queryTime/numQueries) + "ms/query, " +
                           "Nodes: " + std::to_string(stats.nodeCount) + ", " +
                           "Max Depth: " + std::to_string(stats.maxDepth);
            
            // Performance targets
            if (buildTime > 100.0) { // Should build in <100ms
                result.passed = false;
                result.details += " [BUILD TOO SLOW]";
            }
            if (queryTime/numQueries > 0.1) { // Should query in <0.1ms
                result.passed = false;
                result.details += " [QUERY TOO SLOW]";
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.details = "Exception: " + std::string(e.what());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        recordResult(result);
    }
    
    void testLightManagerCulling() {
        PerformanceTestResult result("Light Manager Culling");
        std::cout << "Test 2: " << result.testName << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            // Create light manager
            LightManager lightManager;
            std::vector<std::shared_ptr<Light>> lights = createTestLights(500);
            lightManager.initialize(lights, true);
            
            // Setup culling parameters
            LightCullingParams params;
            params.maxDistance = 50.0f;
            params.enableFrustumCulling = true;
            params.enableDistanceCulling = true;
            params.enableLOD = true;
            
            // Create frustum planes (simplified)
            std::array<Vec4, 6> frustumPlanes = {
                Vec4(1, 0, 0, 10),   // Left
                Vec4(-1, 0, 0, 10),  // Right
                Vec4(0, 1, 0, 10),   // Bottom
                Vec4(0, -1, 0, 10),  // Top
                Vec4(0, 0, 1, 1),    // Near
                Vec4(0, 0, -1, 100)  // Far
            };
            
            // Test culling performance
            auto cullingStart = std::chrono::high_resolution_clock::now();
            
            int numUpdates = 100;
            for (int i = 0; i < numUpdates; i++) {
                Point3 cameraPos(i * 0.1f, 0, 0);
                lightManager.update(cameraPos, frustumPlanes, params);
            }
            
            auto cullingEnd = std::chrono::high_resolution_clock::now();
            double cullingTime = std::chrono::duration<double, std::milli>(cullingEnd - cullingStart).count();
            
            // Get statistics
            auto stats = lightManager.getStatistics();
            
            result.passed = true;
            result.details = "Culling: " + std::to_string(cullingTime/numUpdates) + "ms/update, " +
                           "Efficiency: " + std::to_string(stats.getCullingEfficiency()) + "%, " +
                           "Visible: " + std::to_string(stats.visibleLights) + "/" + std::to_string(stats.totalLights);
            
            // Performance targets
            if (cullingTime/numUpdates > 1.0) { // Should update in <1ms
                result.passed = false;
                result.details += " [CULLING TOO SLOW]";
            }
            if (stats.getCullingEfficiency() < 50.0f) { // Should cull at least 50%
                result.passed = false;
                result.details += " [CULLING INEFFICIENT]";
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.details = "Exception: " + std::string(e.what());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        recordResult(result);
    }
    
    void testAdaptiveSamplingPerformance() {
        PerformanceTestResult result("Adaptive Sampling Performance");
        std::cout << "Test 3: " << result.testName << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            // Create adaptive sampler
            AdaptiveLightSampler sampler;
            auto lightManager = std::make_shared<LightManager>();
            
            std::vector<std::shared_ptr<Light>> lights = createTestLights(200);
            lightManager->initialize(lights, true);
            sampler.initialize(lightManager);
            
            // Create test intersection
            Intersection isect;
            isect.p = Point3(0, 0, 0);
            isect.n = Normal3(0, 1, 0);
            
            RandomSampler randomSampler;
            
            // Test sampling performance
            auto samplingStart = std::chrono::high_resolution_clock::now();
            
            int numSamples = 1000;
            int totalLightSamples = 0;
            
            for (int i = 0; i < numSamples; i++) {
                auto lightSamples = sampler.sampleLights(isect, randomSampler);
                totalLightSamples += static_cast<int>(lightSamples.size());
            }
            
            auto samplingEnd = std::chrono::high_resolution_clock::now();
            double samplingTime = std::chrono::duration<double, std::milli>(samplingEnd - samplingStart).count();
            
            // Get statistics
            auto stats = sampler.getStatistics();
            
            result.passed = true;
            result.details = "Sampling: " + std::to_string(samplingTime/numSamples) + "ms/sample, " +
                           "Avg Lights: " + std::to_string(static_cast<float>(totalLightSamples)/numSamples) + ", " +
                           "Valid: " + std::to_string(stats.validSamples) + "/" + std::to_string(stats.totalSamples);
            
            // Performance targets
            if (samplingTime/numSamples > 0.5) { // Should sample in <0.5ms
                result.passed = false;
                result.details += " [SAMPLING TOO SLOW]";
            }
            if (stats.totalSamples > 0 && static_cast<float>(stats.validSamples)/stats.totalSamples < 0.8f) {
                result.passed = false;
                result.details += " [LOW SAMPLE EFFICIENCY]";
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.details = "Exception: " + std::string(e.what());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        recordResult(result);
    }
    
    void testMemoryPoolPerformance() {
        PerformanceTestResult result("Memory Pool Performance");
        std::cout << "Test 4: " << result.testName << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            // Create memory manager
            LightMemoryManager memoryManager;
            memoryManager.initialize(1024 * 1024, true); // 1MB pool
            
            // Test memory allocation performance
            auto allocStart = std::chrono::high_resolution_clock::now();
            
            std::vector<std::shared_ptr<Light>> lights = createTestLights(1000);
            std::vector<uint32_t> lightHandles;
            
            for (const auto& light : lights) {
                uint32_t handle = memoryManager.registerLight(light);
                lightHandles.push_back(handle);
            }
            
            auto allocEnd = std::chrono::high_resolution_clock::now();
            double allocTime = std::chrono::duration<double, std::milli>(allocEnd - allocStart).count();
            
            // Test memory access performance
            auto accessStart = std::chrono::high_resolution_clock::now();
            
            int numAccesses = 10000;
            for (int i = 0; i < numAccesses; i++) {
                uint32_t handle = lightHandles[i % lightHandles.size()];
                const CompressedLightData* data = memoryManager.getLightData(handle);
                (void)data; // Suppress unused variable warning
            }
            
            auto accessEnd = std::chrono::high_resolution_clock::now();
            double accessTime = std::chrono::duration<double, std::milli>(accessEnd - accessStart).count();
            
            // Get statistics
            auto stats = memoryManager.getStatistics();
            
            result.passed = true;
            result.memoryUsed = stats.totalMemoryUsed;
            result.details = "Alloc: " + std::to_string(allocTime/lights.size()) + "ms/light, " +
                           "Access: " + std::to_string(accessTime/numAccesses*1000) + "μs/access, " +
                           "Memory: " + std::to_string(stats.totalMemoryUsed/1024) + "KB, " +
                           "Efficiency: " + std::to_string(stats.overallEfficiency*100) + "%";
            
            // Performance targets
            if (allocTime/lights.size() > 0.01) { // Should allocate in <0.01ms per light
                result.passed = false;
                result.details += " [ALLOCATION TOO SLOW]";
            }
            if (accessTime/numAccesses > 0.001) { // Should access in <1μs
                result.passed = false;
                result.details += " [ACCESS TOO SLOW]";
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.details = "Exception: " + std::string(e.what());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        recordResult(result);
    }
    
    void testScalabilityWithManyLights() {
        PerformanceTestResult result("Scalability Test (10K Lights)");
        std::cout << "Test 5: " << result.testName << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            // Test with increasing number of lights
            std::vector<int> lightCounts = {100, 500, 1000, 5000, 10000};
            std::vector<double> buildTimes;
            std::vector<double> queryTimes;
            
            for (int lightCount : lightCounts) {
                std::vector<std::shared_ptr<Light>> lights = createTestLights(lightCount);
                
                // Test BVH build time
                LightBVH bvh;
                auto buildStart = std::chrono::high_resolution_clock::now();
                bvh.build(lights);
                auto buildEnd = std::chrono::high_resolution_clock::now();
                
                double buildTime = std::chrono::duration<double, std::milli>(buildEnd - buildStart).count();
                buildTimes.push_back(buildTime);
                
                // Test query time
                auto queryStart = std::chrono::high_resolution_clock::now();
                LightQuery query(Point3(0, 0, 0), 25.0f);
                auto queryResults = bvh.queryLights(query);
                auto queryEnd = std::chrono::high_resolution_clock::now();
                
                double queryTime = std::chrono::duration<double, std::milli>(queryEnd - queryStart).count();
                queryTimes.push_back(queryTime);
            }
            
            result.passed = true;
            result.details = "10K Lights - Build: " + std::to_string(buildTimes.back()) + "ms, " +
                           "Query: " + std::to_string(queryTimes.back()) + "ms";
            
            // Performance targets for 10K lights
            if (buildTimes.back() > 1000.0) { // Should build 10K lights in <1s
                result.passed = false;
                result.details += " [BUILD TOO SLOW]";
            }
            if (queryTimes.back() > 1.0) { // Should query 10K lights in <1ms
                result.passed = false;
                result.details += " [QUERY TOO SLOW]";
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.details = "Exception: " + std::string(e.what());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        recordResult(result);
    }
    
    void testIntegrationPerformance() {
        PerformanceTestResult result("Integration Performance");
        std::cout << "Test 6: " << result.testName << std::endl;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            // Create integrated system
            auto memoryManager = std::make_shared<LightMemoryManager>();
            memoryManager->initialize(2 * 1024 * 1024); // 2MB
            
            auto lightManager = std::make_shared<LightManager>();
            std::vector<std::shared_ptr<Light>> lights = createTestLights(1000);
            lightManager->initialize(lights, true);
            
            AdaptiveLightSampler sampler;
            sampler.initialize(lightManager);
            
            // Test integrated workflow
            auto workflowStart = std::chrono::high_resolution_clock::now();
            
            int numFrames = 100;
            for (int frame = 0; frame < numFrames; frame++) {
                // Update light manager (simulate camera movement)
                Point3 cameraPos(frame * 0.1f, 0, 0);
                std::array<Vec4, 6> frustumPlanes = {
                    Vec4(1, 0, 0, 10), Vec4(-1, 0, 0, 10),
                    Vec4(0, 1, 0, 10), Vec4(0, -1, 0, 10),
                    Vec4(0, 0, 1, 1), Vec4(0, 0, -1, 100)
                };
                lightManager->update(cameraPos, frustumPlanes);
                
                // Sample lights for rendering
                Intersection isect;
                isect.p = cameraPos;
                isect.n = Normal3(0, 1, 0);
                
                RandomSampler randomSampler;
                auto lightSamples = sampler.sampleLights(isect, randomSampler);
            }
            
            auto workflowEnd = std::chrono::high_resolution_clock::now();
            double workflowTime = std::chrono::duration<double, std::milli>(workflowEnd - workflowStart).count();
            
            result.passed = true;
            result.details = "Workflow: " + std::to_string(workflowTime/numFrames) + "ms/frame, " +
                           "Target: 16.67ms (60fps)";
            
            // Performance target: should handle 1000 lights at 60fps
            if (workflowTime/numFrames > 16.67) {
                result.passed = false;
                result.details += " [TOO SLOW FOR 60FPS]";
            }
            
        } catch (const std::exception& e) {
            result.passed = false;
            result.details = "Exception: " + std::string(e.what());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        result.executionTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        recordResult(result);
    }
    
    std::vector<std::shared_ptr<Light>> createTestLights(int count) {
        std::vector<std::shared_ptr<Light>> lights;
        lights.reserve(count);
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<float> posDis(-100.0f, 100.0f);
        std::uniform_real_distribution<float> colorDis(0.1f, 1.0f);
        std::uniform_int_distribution<int> typeDis(0, 3);
        
        for (int i = 0; i < count; i++) {
            Vec3 position(posDis(gen), posDis(gen), posDis(gen));
            Color3 color(colorDis(gen), colorDis(gen), colorDis(gen));
            
            int lightType = typeDis(gen);
            
            switch (lightType) {
                case 0: {
                    // Point light (using PointLight from scene/light.hpp)
                    auto light = std::make_shared<PointLight>(Point3(position.x, position.y, position.z), color);
                    lights.push_back(light);
                    break;
                }
                case 1: {
                    // Spot light
                    Vec3 direction(0, -1, 0);
                    auto light = std::make_shared<SpotLight>(position, direction, color, 0.5f, 1.0f);
                    lights.push_back(light);
                    break;
                }
                case 2: {
                    // Rectangle light
                    Vec3 normal(0, 1, 0);
                    auto light = std::make_shared<RectangleLight>(position, normal, 2.0f, 2.0f, color);
                    lights.push_back(light);
                    break;
                }
                case 3: {
                    // Sphere light
                    auto light = std::make_shared<SphereLight>(position, 1.0f, color);
                    lights.push_back(light);
                    break;
                }
            }
        }
        
        return lights;
    }
    
    void recordResult(const PerformanceTestResult& result) {
        m_results.push_back(result);
        m_totalTests++;
        
        if (result.passed) {
            m_passedTests++;
            std::cout << "  ✓ PASSED - " << result.details << " (" << result.executionTime << "ms)" << std::endl;
        } else {
            std::cout << "  ✗ FAILED - " << result.details << " (" << result.executionTime << "ms)" << std::endl;
        }
        std::cout << std::endl;
    }
    
    void printSummary() {
        std::cout << "=== Test Summary ===" << std::endl;
        std::cout << "Total Tests: " << m_totalTests << std::endl;
        std::cout << "Passed: " << m_passedTests << std::endl;
        std::cout << "Failed: " << (m_totalTests - m_passedTests) << std::endl;
        std::cout << "Success Rate: " << (100.0 * m_passedTests / m_totalTests) << "%" << std::endl;
        
        double totalTime = 0.0;
        size_t totalMemory = 0;
        
        for (const auto& result : m_results) {
            totalTime += result.executionTime;
            totalMemory += result.memoryUsed;
        }
        
        std::cout << "Total Execution Time: " << totalTime << "ms" << std::endl;
        std::cout << "Total Memory Used: " << (totalMemory / 1024) << "KB" << std::endl;
        
        if (m_passedTests == m_totalTests) {
            std::cout << "\n🎉 ALL TESTS PASSED! Task 6 Lighting Performance Optimization is COMPLETE!" << std::endl;
        } else {
            std::cout << "\n❌ Some tests failed. Performance optimization needs improvement." << std::endl;
        }
    }
};

// Main test function
int main() {
    try {
        LightingPerformanceTestSuite testSuite;
        testSuite.runAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test suite failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
