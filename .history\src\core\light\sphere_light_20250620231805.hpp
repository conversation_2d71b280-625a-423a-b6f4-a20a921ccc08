// src/core/light/sphere_light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Sphere Area Light implementation

#pragma once

#include "area_light_base.hpp"
#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"

namespace photon {

/**
 * @brief Sphere Area Light
 */
class SphereLight : public AreaLightBase {
public:
    /**
     * @brief Constructor
     * @param center Sphere center
     * @param radius Sphere radius
     * @param emission Emission color
     * @param intensity Intensity multiplier
     */
    SphereLight(const Vec3& center, float radius,
                const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Destructor
     */
    virtual ~SphereLight() = default;
    
    // AreaLightBase interface implementation
    void sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const override;
    float getArea() const override;
    AreaLightShape getShape() const override { return AreaLightShape::SPHERE; }
    bool intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const override;
    std::string getName() const override { return "Sphere"; }
    
    /**
     * @brief Get sphere center
     */
    const Vec3& getCenter() const { return m_center; }
    
    /**
     * @brief Get sphere radius
     */
    float getRadius() const { return m_radius; }
    
    /**
     * @brief Set sphere geometry
     * @param center New center
     * @param radius New radius
     */
    void setGeometry(const Vec3& center, float radius);
    
    /**
     * @brief Set sphere center
     */
    void setCenter(const Vec3& center) { m_center = center; }
    
    /**
     * @brief Set sphere radius
     */
    void setRadius(float radius) { 
        m_radius = std::max(0.0f, radius); 
        m_area = 4.0f * M_PI * m_radius * m_radius;
    }
    
    /**
     * @brief Sample point using uniform distribution
     * @param u Random sample [0,1]^2
     * @return Sampled point on sphere
     */
    Vec3 sampleUniform(const Vec2& u) const;
    
    /**
     * @brief Sample point visible from given point
     * @param u Random sample [0,1]^2
     * @param fromPoint Point from which sphere is viewed
     * @return Sampled point and PDF
     */
    std::pair<Vec3, float> sampleVisible(const Vec2& u, const Vec3& fromPoint) const;
    
    /**
     * @brief Get solid angle subtended by sphere from point
     * @param point Point from which to compute solid angle
     * @return Solid angle in steradians
     */
    float getSolidAngle(const Vec3& point) const;
    
    /**
     * @brief Get distance from point to sphere surface
     * @param point Query point
     * @return Distance to surface (negative if inside)
     */
    float getDistanceToSurface(const Vec3& point) const;
    
    /**
     * @brief Check if point is inside sphere
     * @param point Point to test
     * @return True if inside sphere
     */
    bool isInsideSphere(const Vec3& point) const;
    
    /**
     * @brief Get normal at point on sphere surface
     * @param point Point on sphere surface
     * @return Outward normal
     */
    Vec3 getNormalAtPoint(const Vec3& point) const;
    
    /**
     * @brief Convert direction to spherical coordinates
     * @param direction Direction vector
     * @return Spherical coordinates (theta, phi)
     */
    Vec2 directionToSpherical(const Vec3& direction) const;
    
    /**
     * @brief Convert spherical coordinates to direction
     * @param spherical Spherical coordinates (theta, phi)
     * @return Direction vector
     */
    Vec3 sphericalToDirection(const Vec2& spherical) const;

private:
    Vec3 m_center;      // Sphere center
    float m_radius;     // Sphere radius
    float m_area;       // Cached area
    
    /**
     * @brief Update cached values
     */
    void updateCachedValues();
    
    /**
     * @brief Sample uniform sphere using rejection sampling
     * @param u Random sample [0,1]^2
     * @return Point on unit sphere
     */
    Vec3 sampleUniformSphereRejection(const Vec2& u) const;
    
    /**
     * @brief Sample uniform sphere using spherical coordinates
     * @param u Random sample [0,1]^2
     * @return Point on unit sphere
     */
    Vec3 sampleUniformSphereSpherical(const Vec2& u) const;
    
    /**
     * @brief Compute sphere-ray intersection
     * @param ray Ray to intersect
     * @param t0 Output near intersection
     * @param t1 Output far intersection
     * @return True if intersection found
     */
    bool intersectSphere(const Ray& ray, float& t0, float& t1) const;
};

/**
 * @brief Sphere Light factory functions
 */
namespace SphereLightFactory {
    /**
     * @brief Create sphere light
     */
    std::shared_ptr<SphereLight> create(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create small sphere light (like a light bulb)
     */
    std::shared_ptr<SphereLight> createBulb(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create large sphere light (like a sun)
     */
    std::shared_ptr<SphereLight> createSun(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create moon-like sphere light
     */
    std::shared_ptr<SphereLight> createMoon(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create pendant sphere light
     */
    std::shared_ptr<SphereLight> createPendant(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
}

} // namespace photon
