// src/core/scene/scene.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Scene management system

#pragma once

#include <unordered_map>
#include "../common.hpp"
#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/ray.hpp"
#include "intersection.hpp"
#include "../geometry/mesh.hpp"
#include "light_linking.hpp"

namespace photon {

// Forward declarations
class Material;
class Light;
class Transform;

/**
 * @brief Intersection information structure
 */
struct Intersection {
    bool hit = false;           ///< Whether intersection occurred
    float t = 0.0f;            ///< Ray parameter at intersection
    Point3 p;                  ///< Intersection point
    Normal3 n;                 ///< Surface normal at intersection
    Vec3 dpdu, dpdv;          ///< Partial derivatives for texture coordinates
    float u = 0.0f, v = 0.0f; ///< Barycentric coordinates
    int primitiveId = -1;      ///< Primitive ID
    int geometryId = -1;       ///< Geometry ID
    
    std::shared_ptr<Material> material; ///< Material at intersection
    
    /**
     * @brief Get shading normal (may differ from geometric normal)
     */
    Normal3 getShadingNormal() const { return n; }

    /**
     * @brief Check if intersection has valid UV coordinates
     */
    bool hasUV() const { return u >= 0.0f && v >= 0.0f; }

    /**
     * @brief Get UV coordinates as Vec2
     */
    Vec2 getUV() const { return Vec2(u, v); }

    /**
     * @brief Check if intersection has tangent vectors
     */
    bool hasTangents() const { return dpdu.lengthSquared() > 0.0f && dpdv.lengthSquared() > 0.0f; }

    /**
     * @brief Get tangent vector (dpdu normalized)
     */
    Vec3 getTangent() const { return dpdu.normalized(); }

    /**
     * @brief Get bitangent vector (dpdv normalized)
     */
    Vec3 getBitangent() const { return dpdv.normalized(); }
    
    /**
     * @brief Check if intersection is valid
     */
    bool isValid() const { return hit && t > 0.0f; }
};



/**
 * @brief Scene class for managing geometry, materials, and lights
 */
class Scene {
public:
    Scene();
    ~Scene();
    
    // Non-copyable
    Scene(const Scene&) = delete;
    Scene& operator=(const Scene&) = delete;
    
    /**
     * @brief Add mesh to scene
     */
    void addMesh(std::shared_ptr<Mesh> mesh);
    
    /**
     * @brief Add material to scene
     */
    void addMaterial(const std::string& name, std::shared_ptr<Material> material);
    
    /**
     * @brief Add light to scene
     */
    void addLight(std::shared_ptr<Light> light);
    
    /**
     * @brief Get material by name
     */
    std::shared_ptr<Material> getMaterial(const std::string& name) const;
    
    /**
     * @brief Ray-scene intersection
     */
    bool intersect(const Ray& ray, Intersection& isect) const;
    
    /**
     * @brief Shadow ray test (faster, no intersection details)
     */
    bool intersectShadow(const Ray& ray) const;
    
    /**
     * @brief Build acceleration structure
     */
    void buildAccelerationStructure(RTCDevice device);
    
    /**
     * @brief Set Embree scene (called by renderer)
     */
    void setEmbreeScene(RTCScene scene) { m_embreeScene = scene; }
    
    /**
     * @brief Get Embree scene
     */
    RTCScene getEmbreeScene() const { return m_embreeScene; }
    
    /**
     * @brief Get all meshes
     */
    const std::vector<std::shared_ptr<Mesh>>& getMeshes() const { return m_meshes; }
    
    /**
     * @brief Get all lights
     */
    const std::vector<std::shared_ptr<Light>>& getLights() const { return m_lights; }

    /**
     * @brief Get effective lights for intersection (with light linking)
     *
     * @param isect Intersection information
     * @return Vector of lights that should illuminate the intersection
     */
    std::vector<std::shared_ptr<Light>> getEffectiveLights(const Intersection& isect) const;
    
    /**
     * @brief Load scene from file
     */
    bool loadFromFile(const std::string& filename);
    
    /**
     * @brief Save scene to file
     */
    bool saveToFile(const std::string& filename) const;
    
    /**
     * @brief Get scene bounds
     */
    struct Bounds {
        Point3 min, max;
        bool isEmpty() const { return min.x > max.x; }
        Point3 center() const { return (min + max) * 0.5f; }
        Vec3 diagonal() const { return max - min; }
        float maxExtent() const { return diagonal().maxComponent(); }
    };
    
    Bounds getBounds() const;
    
    /**
     * @brief Clear scene
     */
    void clear();

    /**
     * @brief Get light linking manager
     */
    LightLinkingManager& getLightLinkingManager() { return m_lightLinkingManager; }

    /**
     * @brief Get light linking manager (const)
     */
    const LightLinkingManager& getLightLinkingManager() const { return m_lightLinkingManager; }
    
    /**
     * @brief Get scene statistics
     */
    struct Statistics {
        size_t meshCount = 0;
        size_t triangleCount = 0;
        size_t vertexCount = 0;
        size_t materialCount = 0;
        size_t lightCount = 0;
    };
    
    Statistics getStatistics() const;

private:
    std::vector<std::shared_ptr<Mesh>> m_meshes;
    std::unordered_map<std::string, std::shared_ptr<Material>> m_materials;
    std::vector<std::shared_ptr<Light>> m_lights;

    RTCScene m_embreeScene = nullptr;
    bool m_accelerationBuilt = false;

    // Light linking system
    LightLinkingManager m_lightLinkingManager;

    // Helper methods
    void updateBounds();
    Bounds m_bounds;
};

} // namespace photon
