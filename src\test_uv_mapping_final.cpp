// src/test_uv_mapping_final.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Final comprehensive test suite for UV Mapping Enhancement System
// Task 1.4 - UV Mapping Tests (Complete System Validation)

#include "core/texture/uv_mapping.hpp"
#include "core/geometry/mesh.hpp"
#include "core/scene/intersection.hpp"
#include "core/math/vec2.hpp"
#include "core/math/vec3.hpp"
#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include <chrono>
#include <vector>
#include <string>

// Define M_PI if not available (Windows)
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

using namespace photon;

// Test results tracking
struct TestResult {
    std::string name;
    bool passed;
    std::string details;
    double performance_ns = 0.0;
};

std::vector<TestResult> test_results;

void addTestResult(const std::string& name, bool passed, const std::string& details = "", double perf_ns = 0.0) {
    test_results.push_back({name, passed, details, perf_ns});
    std::cout << "[" << (passed ? "PASS" : "FAIL") << "] " << name;
    if (!details.empty()) {
        std::cout << " - " << details;
    }
    if (perf_ns > 0.0) {
        std::cout << " (" << std::fixed << std::setprecision(2) << perf_ns << "ns)";
    }
    std::cout << std::endl;
}

bool isNear(float a, float b, float epsilon = 1e-6f) {
    return std::abs(a - b) < epsilon;
}

bool isNear(const Vec2& a, const Vec2& b, float epsilon = 1e-6f) {
    return isNear(a.x, b.x, epsilon) && isNear(a.y, b.y, epsilon);
}

// Test 1: UVTransform System Validation
void test_uv_transform_system() {
    std::cout << "\n=== Test 1: UVTransform System Validation ===" << std::endl;
    
    // Test 1.1: Identity transform
    {
        UVTransform identity = UVTransform::identity();
        Vec2 uv(0.5f, 0.5f);
        Vec2 result = identity.transform(uv);
        bool passed = isNear(result, uv) && identity.isIdentity();
        addTestResult("1.1 Identity Transform", passed);
    }
    
    // Test 1.2: Scale transform precision
    {
        UVTransform scale = UVTransform::createScale(2.0f, 0.5f);
        Vec2 uv(0.25f, 0.8f);
        Vec2 result = scale.transform(uv);
        Vec2 expected(0.5f, 0.4f);
        bool passed = isNear(result, expected);
        addTestResult("1.2 Scale Transform Precision", passed);
    }
    
    // Test 1.3: Rotation transform accuracy
    {
        UVTransform rotate = UVTransform::createRotation(M_PI / 2.0f);
        // Use a point that rotates around center (0.5, 0.5)
        Vec2 uv(1.0f, 0.5f); // Point on right edge
        Vec2 result = rotate.transform(uv);
        Vec2 expected(0.5f, 1.0f); // Should rotate to top edge
        // Use more relaxed tolerance for rotation due to floating point precision
        bool passed = isNear(result, expected, 1e-4f);
        addTestResult("1.3 Rotation Transform (90°)", passed);
    }
    
    // Test 1.4: Complex transform chain
    {
        UVTransform transform = UVTransform::createScale(2.0f, 0.5f);
        transform.offset = Vec2(0.1f, -0.2f);
        transform.rotation = M_PI / 4.0f;
        
        Vec2 original(0.3f, 0.7f);
        Vec2 transformed = transform.transform(original);
        Vec2 restored = transform.inverseTransform(transformed);
        
        bool passed = isNear(original, restored, 1e-4f);
        addTestResult("1.4 Complex Transform Chain", passed);
    }
    
    // Test 1.5: Transform performance
    {
        const int NUM_TRANSFORMS = 100000;
        UVTransform transform = UVTransform::createScale(1.5f, 2.0f);
        transform.rotation = M_PI / 6.0f;
        transform.offset = Vec2(0.1f, 0.2f);
        
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < NUM_TRANSFORMS; i++) {
            Vec2 uv(static_cast<float>(i % 100) / 100.0f, static_cast<float>((i / 100) % 100) / 100.0f);
            Vec2 result = transform.transform(uv);
            (void)result; // Suppress unused warning
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        double avgTime = duration.count() / (double)NUM_TRANSFORMS;
        
        bool passed = avgTime < 50.0; // Target: under 50ns per transform
        addTestResult("1.5 Transform Performance", passed, "", avgTime);
    }
}

// Test 2: UV Mapping Modes Comprehensive
void test_uv_mapping_modes() {
    std::cout << "\n=== Test 2: UV Mapping Modes Comprehensive ===" << std::endl;
    
    // Test 2.1: All mapping modes functional
    {
        std::vector<UVMappingMode> modes = {
            UVMappingMode::VERTEX_UV,
            UVMappingMode::PLANAR_XY,
            UVMappingMode::PLANAR_XZ,
            UVMappingMode::PLANAR_YZ,
            UVMappingMode::CYLINDRICAL_X,
            UVMappingMode::CYLINDRICAL_Y,
            UVMappingMode::CYLINDRICAL_Z,
            UVMappingMode::SPHERICAL,
            UVMappingMode::CUBIC,
            UVMappingMode::TRIPLANAR
        };
        
        bool all_passed = true;
        for (auto mode : modes) {
            UVMapping mapping(mode);
            Vec3 position(0.5f, 0.5f, 0.5f);
            Vec3 normal(0.0f, 1.0f, 0.0f);
            Vec2 result = mapping.generateUV(position, normal);
            
            // Check if result is in valid range [0,1]
            if (result.x < 0.0f || result.x > 1.0f || result.y < 0.0f || result.y > 1.0f) {
                all_passed = false;
                break;
            }
        }
        addTestResult("2.1 All Mapping Modes Functional", all_passed);
    }
    
    // Test 2.2: Spherical mapping poles
    {
        UVMapping spherical(UVMappingMode::SPHERICAL);
        
        // North pole
        Vec3 north(0.0f, 1.0f, 0.0f);
        Vec2 result_north = spherical.generateUV(north);
        
        // South pole
        Vec3 south(0.0f, -1.0f, 0.0f);
        Vec2 result_south = spherical.generateUV(south);
        
        bool passed = isNear(result_north.y, 0.0f, 1e-5f) && isNear(result_south.y, 1.0f, 1e-5f);
        addTestResult("2.2 Spherical Mapping Poles", passed);
    }
    
    // Test 2.3: Cylindrical mapping consistency
    {
        UVMapping cylindrical(UVMappingMode::CYLINDRICAL_Y);
        
        Vec3 pos1(1.0f, 0.0f, 0.0f);
        Vec3 pos2(0.0f, 0.0f, 1.0f);
        Vec3 pos3(-1.0f, 0.0f, 0.0f);
        Vec3 pos4(0.0f, 0.0f, -1.0f);
        
        Vec2 uv1 = cylindrical.generateUV(pos1);
        Vec2 uv2 = cylindrical.generateUV(pos2);
        Vec2 uv3 = cylindrical.generateUV(pos3);
        Vec2 uv4 = cylindrical.generateUV(pos4);
        
        // Check that Y coordinates are consistent (same height)
        bool passed = isNear(uv1.y, uv2.y) && isNear(uv2.y, uv3.y) && isNear(uv3.y, uv4.y);
        addTestResult("2.3 Cylindrical Mapping Consistency", passed);
    }
    
    // Test 2.4: Triplanar blending
    {
        UVMapping triplanar(UVMappingMode::TRIPLANAR);

        // Test with different positions and normals to ensure variation
        Vec3 position1(1.0f, 0.0f, 0.0f);
        Vec3 position2(0.0f, 1.0f, 0.0f);
        Vec3 position3(0.0f, 0.0f, 1.0f);

        Vec3 normal_x(1.0f, 0.0f, 0.0f);
        Vec3 normal_y(0.0f, 1.0f, 0.0f);
        Vec3 normal_z(0.0f, 0.0f, 1.0f);

        Vec2 uv_x = triplanar.generateUV(position1, normal_x);
        Vec2 uv_y = triplanar.generateUV(position2, normal_y);
        Vec2 uv_z = triplanar.generateUV(position3, normal_z);

        // Check that triplanar produces valid UV coordinates
        bool valid_coords = (uv_x.x >= 0.0f && uv_x.x <= 1.0f && uv_x.y >= 0.0f && uv_x.y <= 1.0f) &&
                           (uv_y.x >= 0.0f && uv_y.x <= 1.0f && uv_y.y >= 0.0f && uv_y.y <= 1.0f) &&
                           (uv_z.x >= 0.0f && uv_z.x <= 1.0f && uv_z.y >= 0.0f && uv_z.y <= 1.0f);

        addTestResult("2.4 Triplanar Blending", valid_coords);
    }
}

// Test 3: Multiple UV Sets Integration
void test_multiple_uv_sets_integration() {
    std::cout << "\n=== Test 3: Multiple UV Sets Integration ===" << std::endl;
    
    // Test 3.1: Vertex-Mesh-Intersection pipeline
    {
        // Create mesh with multiple UV sets
        Mesh mesh;
        Point3 pos(1.0f, 2.0f, 3.0f);
        Normal3 norm(0.0f, 1.0f, 0.0f);
        Vec2 uv0(0.1f, 0.2f);
        Vec2 uv1(0.3f, 0.4f);
        Vec2 uv2(0.5f, 0.6f);
        Vec2 uv3(0.7f, 0.8f);
        
        mesh.addVertex(pos, norm, uv0, uv1, uv2, uv3);
        
        // Create intersection and populate from mesh
        Intersection isect;
        isect.setUV(0, mesh.getVertexUV(0, 0));
        isect.setUV(1, mesh.getVertexUV(0, 1));
        isect.setUV(2, mesh.getVertexUV(0, 2));
        isect.setUV(3, mesh.getVertexUV(0, 3));
        
        // Verify pipeline integrity
        bool passed = (isect.getUV(0) == uv0) && (isect.getUV(1) == uv1) && 
                     (isect.getUV(2) == uv2) && (isect.getUV(3) == uv3);
        addTestResult("3.1 Vertex-Mesh-Intersection Pipeline", passed);
    }
    
    // Test 3.2: MultiUVMapping with different modes
    {
        MultiUVMapping multiMapping;
        multiMapping.setMappingMode(0, UVMappingMode::PLANAR_XY);
        multiMapping.setMappingMode(1, UVMappingMode::CYLINDRICAL_Y);
        multiMapping.setMappingMode(2, UVMappingMode::SPHERICAL);
        multiMapping.setMappingMode(3, UVMappingMode::CUBIC);
        
        Vec3 position(0.5f, 0.5f, 0.5f);
        Vec3 normal(0.0f, 1.0f, 0.0f);
        
        Vec2 uv0 = multiMapping.generateUV(0, position, normal);
        Vec2 uv1 = multiMapping.generateUV(1, position, normal);
        Vec2 uv2 = multiMapping.generateUV(2, position, normal);
        Vec2 uv3 = multiMapping.generateUV(3, position, normal);
        
        // All should be different due to different mapping modes
        bool passed = !(uv0 == uv1) && !(uv1 == uv2) && !(uv2 == uv3) && !(uv0 == uv3);
        addTestResult("3.2 MultiUVMapping Different Modes", passed);
    }
    
    // Test 3.3: Mesh statistics accuracy
    {
        Mesh mesh;
        Point3 pos(0.0f, 0.0f, 0.0f);
        Normal3 norm(0.0f, 1.0f, 0.0f);
        
        // Add vertices with progressive UV sets
        mesh.addVertex(pos, norm, 0.1f, 0.2f); // Only UV0
        mesh.addVertex(pos, norm, Vec2(0.1f, 0.2f), Vec2(0.3f, 0.4f)); // UV0 + UV1
        mesh.addVertex(pos, norm, Vec2(0.1f, 0.2f), Vec2(0.3f, 0.4f), Vec2(0.5f, 0.6f)); // UV0 + UV1 + UV2
        mesh.addVertex(pos, norm, Vec2(0.1f, 0.2f), Vec2(0.3f, 0.4f), Vec2(0.5f, 0.6f), Vec2(0.7f, 0.8f)); // All UV sets
        
        auto stats = mesh.getStats();
        bool passed = stats.hasTexCoords && stats.hasUV1 && stats.hasUV2 && stats.hasUV3 && (stats.maxUVSets == 4);
        addTestResult("3.3 Mesh Statistics Accuracy", passed);
    }
}

// Test 4: Performance and Scalability
void test_performance_scalability() {
    std::cout << "\n=== Test 4: Performance and Scalability ===" << std::endl;

    // Test 4.1: UV generation performance
    {
        const int NUM_OPERATIONS = 50000;
        UVMapping mapping(UVMappingMode::SPHERICAL);

        auto start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < NUM_OPERATIONS; i++) {
            Vec3 position(
                static_cast<float>(i % 100) / 100.0f,
                static_cast<float>((i / 100) % 100) / 100.0f,
                static_cast<float>((i / 10000) % 100) / 100.0f
            );
            Vec2 result = mapping.generateUV(position);
            (void)result;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        double avgTime = duration.count() / (double)NUM_OPERATIONS;

        bool passed = avgTime < 100.0; // Target: under 100ns per generation
        addTestResult("4.1 UV Generation Performance", passed, "", avgTime);
    }

    // Test 4.2: Multiple UV sets performance
    {
        const int NUM_OPERATIONS = 25000;

        auto start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < NUM_OPERATIONS; i++) {
            Vertex v;
            v.setUV(0, Vec2(0.1f, 0.2f));
            v.setUV(1, Vec2(0.3f, 0.4f));
            v.setUV(2, Vec2(0.5f, 0.6f));
            v.setUV(3, Vec2(0.7f, 0.8f));

            Vec2 uv0 = v.getUV(0);
            Vec2 uv1 = v.getUV(1);
            Vec2 uv2 = v.getUV(2);
            Vec2 uv3 = v.getUV(3);
            (void)uv0; (void)uv1; (void)uv2; (void)uv3;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        double avgTime = duration.count() / (double)(NUM_OPERATIONS * 8); // 8 operations per iteration

        bool passed = avgTime < 10.0; // Target: under 10ns per UV operation
        addTestResult("4.2 Multiple UV Sets Performance", passed, "", avgTime);
    }

    // Test 4.3: Large mesh scalability
    {
        const int NUM_VERTICES = 10000;
        Mesh mesh;

        auto start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < NUM_VERTICES; i++) {
            Point3 pos(static_cast<float>(i), 0.0f, 0.0f);
            Normal3 norm(0.0f, 1.0f, 0.0f);
            Vec2 uv0(static_cast<float>(i % 100) / 100.0f, static_cast<float>((i / 100) % 100) / 100.0f);
            Vec2 uv1(uv0.x * 2.0f, uv0.y * 2.0f);

            mesh.addVertex(pos, norm, uv0, uv1);
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double avgTime = duration.count() / (double)NUM_VERTICES;

        bool passed = avgTime < 10.0; // Target: under 10μs per vertex
        addTestResult("4.3 Large Mesh Scalability", passed, "", avgTime * 1000.0); // Convert to ns for display
    }
}

// Test 5: Edge Cases and Robustness
void test_edge_cases_robustness() {
    std::cout << "\n=== Test 5: Edge Cases and Robustness ===" << std::endl;

    // Test 5.1: Invalid UV set indices
    {
        Vertex v;
        Vec2 original = v.getUV(0);

        // Test out-of-range indices
        Vec2 invalid1 = v.getUV(-1);
        Vec2 invalid2 = v.getUV(10);

        // Should return UV0 for invalid indices
        bool passed = (invalid1 == original) && (invalid2 == original);
        addTestResult("5.1 Invalid UV Set Indices", passed);
    }

    // Test 5.2: Zero vectors and degenerate cases
    {
        UVMapping spherical(UVMappingMode::SPHERICAL);

        // Zero vector
        Vec3 zero(0.0f, 0.0f, 0.0f);
        Vec2 result_zero = spherical.generateUV(zero);

        // Very small vector
        Vec3 tiny(1e-10f, 1e-10f, 1e-10f);
        Vec2 result_tiny = spherical.generateUV(tiny);

        // Should not crash and produce valid UV coordinates
        bool passed = (result_zero.x >= 0.0f && result_zero.x <= 1.0f &&
                      result_zero.y >= 0.0f && result_zero.y <= 1.0f) &&
                     (result_tiny.x >= 0.0f && result_tiny.x <= 1.0f &&
                      result_tiny.y >= 0.0f && result_tiny.y <= 1.0f);
        addTestResult("5.2 Zero Vectors and Degenerate Cases", passed);
    }

    // Test 5.3: Extreme transform values
    {
        UVTransform extreme;
        extreme.scale = Vec2(1000.0f, 0.001f);
        extreme.offset = Vec2(100.0f, -100.0f);
        extreme.rotation = 10.0f * M_PI;

        Vec2 uv(0.5f, 0.5f);
        Vec2 result = extreme.transform(uv);

        // Should not crash or produce NaN/Inf
        bool passed = std::isfinite(result.x) && std::isfinite(result.y);
        addTestResult("5.3 Extreme Transform Values", passed);
    }

    // Test 5.4: Mesh with no UV coordinates
    {
        Mesh mesh;
        Point3 pos(0.0f, 0.0f, 0.0f);
        Normal3 norm(0.0f, 1.0f, 0.0f);

        mesh.addVertex(pos, norm); // No UV coordinates

        auto stats = mesh.getStats();
        Vec2 uv = mesh.getVertexUV(0, 0);

        // Should handle gracefully
        bool passed = !stats.hasTexCoords && (uv == Vec2(0.0f, 0.0f));
        addTestResult("5.4 Mesh with No UV Coordinates", passed);
    }
}

// Test 6: Integration and Compatibility
void test_integration_compatibility() {
    std::cout << "\n=== Test 6: Integration and Compatibility ===" << std::endl;

    // Test 6.1: Backward compatibility
    {
        // Old-style UV usage should still work
        Vertex v;
        v.u = 0.3f;
        v.v = 0.7f;

        Vec2 uv_old = Vec2(v.u, v.v);
        Vec2 uv_new = v.getUV(0);

        bool passed = (uv_old == uv_new);
        addTestResult("6.1 Backward Compatibility", passed);
    }

    // Test 6.2: Intersection backward compatibility
    {
        Intersection isect;
        isect.setUV(Vec2(0.4f, 0.6f)); // Old method

        Vec2 uv_old = isect.getUV(); // Old method
        Vec2 uv_new = isect.getUV(0); // New method

        bool passed = (uv_old == uv_new);
        addTestResult("6.2 Intersection Backward Compatibility", passed);
    }

    // Test 6.3: Mixed UV set usage
    {
        Mesh mesh;
        Point3 pos(0.0f, 0.0f, 0.0f);
        Normal3 norm(0.0f, 1.0f, 0.0f);

        // Mix old and new style
        mesh.addVertex(pos, norm, 0.1f, 0.2f); // Old style
        mesh.addVertex(pos, norm, Vec2(0.3f, 0.4f), Vec2(0.5f, 0.6f)); // New style

        Vec2 uv1 = mesh.getVertexUV(0, 0);
        Vec2 uv2 = mesh.getVertexUV(1, 0);
        Vec2 uv3 = mesh.getVertexUV(1, 1);

        bool passed = (uv1 == Vec2(0.1f, 0.2f)) && (uv2 == Vec2(0.3f, 0.4f)) && (uv3 == Vec2(0.5f, 0.6f));
        addTestResult("6.3 Mixed UV Set Usage", passed);
    }
}

int main() {
    std::cout << "PhotonRender UV Mapping Enhancement - Final Test Suite" << std::endl;
    std::cout << "=======================================================" << std::endl;
    std::cout << "Task 1.4: Complete System Validation" << std::endl;

    try {
        test_uv_transform_system();
        test_uv_mapping_modes();
        test_multiple_uv_sets_integration();
        test_performance_scalability();
        test_edge_cases_robustness();
        test_integration_compatibility();

        // Calculate results
        int total_tests = test_results.size();
        int passed_tests = 0;
        double total_performance = 0.0;
        int performance_tests = 0;

        for (const auto& result : test_results) {
            if (result.passed) passed_tests++;
            if (result.performance_ns > 0.0) {
                total_performance += result.performance_ns;
                performance_tests++;
            }
        }

        double avg_performance = performance_tests > 0 ? total_performance / performance_tests : 0.0;

        std::cout << "\n=== FINAL TEST RESULTS ===" << std::endl;
        std::cout << "Tests Passed: " << passed_tests << "/" << total_tests << std::endl;
        std::cout << "Success Rate: " << std::fixed << std::setprecision(1)
                  << (100.0 * passed_tests / total_tests) << "%" << std::endl;

        if (performance_tests > 0) {
            std::cout << "Average Performance: " << std::fixed << std::setprecision(2)
                      << avg_performance << "ns per operation" << std::endl;
        }

        if (passed_tests == total_tests) {
            std::cout << "\n🎉 ALL TESTS PASSED! UV Mapping Enhancement System COMPLETE!" << std::endl;
            std::cout << "✅ Task 1.1: UVTransform System - VALIDATED" << std::endl;
            std::cout << "✅ Task 1.2: UVMapping Modes - VALIDATED" << std::endl;
            std::cout << "✅ Task 1.3: Multiple UV Sets - VALIDATED" << std::endl;
            std::cout << "✅ Task 1.4: UV Mapping Tests - COMPLETED" << std::endl;
            std::cout << "\n🚀 TASK 1 UV MAPPING ENHANCEMENT - 100% COMPLETE!" << std::endl;
            std::cout << "Ready for Task 2: Procedural Texture System" << std::endl;
            return 0;
        } else {
            std::cout << "\n❌ Some tests failed. System validation incomplete." << std::endl;
            return 1;
        }

    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
