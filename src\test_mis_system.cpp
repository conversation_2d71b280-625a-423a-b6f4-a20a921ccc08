// src/test_mis_system.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Multiple Importance Sampling system test suite

#include "core/sampling/mis_sampling.hpp"
#include "core/integrator/mis_integrator.hpp"
#include "core/scene/scene.hpp"
#include "core/scene/light.hpp"
#include "core/material/material.hpp"
#include "core/sampler/sampler.hpp"
#include "core/math/vec3.hpp"
#include "core/common.hpp"
#include <iostream>
#include <chrono>
#include <vector>
#include <memory>
#include <cassert>
#include <cmath>

using namespace photon;

/**
 * @brief Test utilities and helper functions
 */
namespace TestUtils {
    
    /**
     * @brief Create simple test scene with basic geometry and lights
     */
    std::unique_ptr<Scene> createTestScene() {
        auto scene = std::make_unique<Scene>();
        
        // Add a simple point light
        auto pointLight = std::make_shared<PointLight>(Point3(0, 5, 0), Color3(10.0f));
        scene->addLight(pointLight);
        
        // Add a directional light (sun)
        auto dirLight = std::make_shared<DirectionalLight>(Vec3(0, -1, -1).normalized(), Color3(3.0f));
        scene->addLight(dirLight);
        
        return scene;
    }
    
    /**
     * @brief Create test intersection with material
     */
    Intersection createTestIntersection() {
        Intersection isect;
        isect.p = Point3(0, 0, 0);
        isect.n = Normal3(0, 1, 0);
        isect.t = 1.0f;
        
        // Create simple diffuse material
        auto material = std::make_shared<DiffuseMaterial>(Color3(0.8f, 0.6f, 0.4f));
        isect.material = material;
        
        return isect;
    }
    
    /**
     * @brief Performance timer utility
     */
    class Timer {
    public:
        void start() { m_start = std::chrono::high_resolution_clock::now(); }
        
        float stop() {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<float, std::milli>(end - m_start).count();
        }
        
    private:
        std::chrono::high_resolution_clock::time_point m_start;
    };
    
    /**
     * @brief Statistical analysis utilities
     */
    struct Statistics {
        float mean = 0.0f;
        float variance = 0.0f;
        float stddev = 0.0f;
        float min = 0.0f;
        float max = 0.0f;
        
        void calculate(const std::vector<float>& values) {
            if (values.empty()) return;
            
            // Calculate mean
            float sum = 0.0f;
            for (float v : values) sum += v;
            mean = sum / values.size();
            
            // Calculate variance
            float varSum = 0.0f;
            for (float v : values) {
                float diff = v - mean;
                varSum += diff * diff;
            }
            variance = varSum / values.size();
            stddev = std::sqrt(variance);
            
            // Find min/max
            min = max = values[0];
            for (float v : values) {
                if (v < min) min = v;
                if (v > max) max = v;
            }
        }
    };
}

/**
 * @brief Test 1: MIS Framework Basic Functionality
 */
bool testMISFrameworkBasics() {
    std::cout << "Testing MIS Framework Basics..." << std::endl;
    
    try {
        // Test MIS sampling creation
        MISSampling misSampling(MISStrategy::POWER_HEURISTIC, 2, 2);
        
        // Test strategy changes
        misSampling.setStrategy(MISStrategy::BALANCE_HEURISTIC);
        assert(misSampling.getStrategy() == MISStrategy::BALANCE_HEURISTIC);
        
        misSampling.setStrategy(MISStrategy::OPTIMAL_HEURISTIC);
        assert(misSampling.getStrategy() == MISStrategy::OPTIMAL_HEURISTIC);
        
        // Test sample count changes
        misSampling.setLightSamples(4);
        misSampling.setBSDFSamples(3);
        
        // Test statistics
        auto stats = misSampling.getStatistics();
        assert(stats.totalSamples == 0); // Should be zero initially
        
        misSampling.resetStatistics();
        
        std::cout << "✅ MIS Framework Basics: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ MIS Framework Basics: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 2: MIS Heuristics Validation
 */
bool testMISHeuristics() {
    std::cout << "Testing MIS Heuristics..." << std::endl;
    
    try {
        // Test power heuristic
        float weight1 = MISSampling::powerHeuristic(1, 0.5f, 1, 0.3f);
        assert(weight1 > 0.0f && weight1 <= 1.0f);
        
        // Test balance heuristic
        float weight2 = MISSampling::balanceHeuristic(1, 0.5f, 1, 0.3f);
        assert(weight2 > 0.0f && weight2 <= 1.0f);
        
        // Test optimal heuristic
        float weight3 = MISSampling::optimalHeuristic(1, 0.5f, 1, 0.3f);
        assert(weight3 > 0.0f && weight3 <= 1.0f);
        
        // Test edge cases
        float weightZero = MISSampling::powerHeuristic(1, 0.0f, 1, 0.5f);
        assert(weightZero == 0.0f);
        
        float weightOne = MISSampling::powerHeuristic(1, 0.5f, 1, 0.0f);
        assert(weightOne == 1.0f);
        
        // Test that weights sum to 1 for complementary cases
        float pdf1 = 0.4f, pdf2 = 0.6f;
        float w1 = MISSampling::powerHeuristic(1, pdf1, 1, pdf2);
        float w2 = MISSampling::powerHeuristic(1, pdf2, 1, pdf1);
        float sum = w1 + w2;
        assert(std::abs(sum - 1.0f) < 0.001f); // Should sum to 1
        
        std::cout << "✅ MIS Heuristics: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ MIS Heuristics: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 3: MIS Integrator Functionality
 */
bool testMISIntegrator() {
    std::cout << "Testing MIS Integrator..." << std::endl;
    
    try {
        // Create MIS integrator
        auto integrator = std::make_unique<MISIntegrator>(8, 3, 2, 2, MISStrategy::POWER_HEURISTIC);
        
        // Test configuration
        integrator->setMaxDepth(10);
        integrator->setRussianRouletteDepth(4);
        integrator->setLightSamples(3);
        integrator->setBSDFSamples(2);
        integrator->setMISStrategy(MISStrategy::BALANCE_HEURISTIC);
        
        // Test modes
        integrator->setDirectLightingOnly(true);
        integrator->setVarianceReduction(true);
        
        // Test factory methods
        auto highQuality = MISIntegratorFactory::createHighQuality();
        assert(highQuality != nullptr);
        
        auto fast = MISIntegratorFactory::createFast();
        assert(fast != nullptr);
        
        auto balanced = MISIntegratorFactory::createBalanced();
        assert(balanced != nullptr);
        
        auto debug = MISIntegratorFactory::createDebug();
        assert(debug != nullptr);
        
        std::cout << "✅ MIS Integrator: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ MIS Integrator: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 4: Performance Benchmarks
 */
bool testPerformanceBenchmarks() {
    std::cout << "Testing Performance Benchmarks..." << std::endl;
    
    try {
        const int numSamples = 1000;
        std::vector<float> misTimes, standardTimes;
        
        // Create test scene and intersection
        auto scene = TestUtils::createTestScene();
        auto isect = TestUtils::createTestIntersection();
        auto sampler = std::make_unique<RandomSampler>();
        
        TestUtils::Timer timer;
        
        // Benchmark MIS sampling
        MISSampling misSampling(MISStrategy::POWER_HEURISTIC, 2, 2);
        
        for (int i = 0; i < numSamples; ++i) {
            timer.start();
            
            Vec3 wo = Vec3(0, 1, 0); // Outgoing direction
            MISSample sample = misSampling.sampleDirectLighting(isect, *scene, *sampler, wo);
            
            float time = timer.stop();
            misTimes.push_back(time);
        }
        
        // Calculate statistics
        TestUtils::Statistics misStats, standardStats;
        misStats.calculate(misTimes);
        
        // Validate performance targets
        assert(misStats.mean < 1.0f); // Should be less than 1ms per sample
        
        // Get MIS statistics
        auto misSystemStats = misSampling.getStatistics();
        assert(misSystemStats.totalSamples > 0);
        
        std::cout << "  MIS Average Time: " << misStats.mean << " ms" << std::endl;
        std::cout << "  MIS Std Dev: " << misStats.stddev << " ms" << std::endl;
        std::cout << "  MIS Overhead: " << misSystemStats.avgMISOverhead << " ns" << std::endl;
        
        // Validate overhead target (<200ns)
        assert(misSystemStats.avgMISOverhead < 200.0f);
        
        std::cout << "✅ Performance Benchmarks: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Performance Benchmarks: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 5: Integration Tests
 */
bool testIntegration() {
    std::cout << "Testing Integration..." << std::endl;
    
    try {
        // Create complete rendering setup
        auto scene = TestUtils::createTestScene();
        auto integrator = MISIntegratorFactory::createBalanced();
        auto sampler = std::make_unique<RandomSampler>();
        
        // Preprocess scene
        integrator->preprocess(*scene);
        
        // Test rendering a few rays
        const int numRays = 100;
        std::vector<Color3> results;
        
        for (int i = 0; i < numRays; ++i) {
            // Create test ray
            Ray ray(Point3(0, 1, 5), Vec3(0, 0, -1).normalized());
            
            // Render
            Color3 result = integrator->Li(ray, *scene, *sampler);
            results.push_back(result);
            
            // Validate result
            assert(result.isValid());
            assert(!result.hasNaN());
            assert(!result.hasInf());
        }
        
        // Check performance metrics
        auto metrics = integrator->getPerformanceMetrics();
        assert(metrics.totalSamples > 0);
        assert(metrics.avgRenderTime >= 0.0f);
        
        // Check MIS statistics
        auto misStats = integrator->getMISStatistics();
        assert(misStats.totalSamples > 0);
        
        std::cout << "  Rendered " << numRays << " rays successfully" << std::endl;
        std::cout << "  Average render time: " << metrics.avgRenderTime << " ms" << std::endl;
        std::cout << "  Noise reduction: " << metrics.noiseReduction << "%" << std::endl;
        
        std::cout << "✅ Integration Tests: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Integration Tests: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Main test runner
 */
int main() {
    std::cout << "=== PhotonRender MIS System Test Suite ===" << std::endl;
    std::cout << std::endl;
    
    int passed = 0;
    int total = 5;
    
    // Run all tests
    if (testMISFrameworkBasics()) passed++;
    if (testMISHeuristics()) passed++;
    if (testMISIntegrator()) passed++;
    if (testPerformanceBenchmarks()) passed++;
    if (testIntegration()) passed++;
    
    std::cout << std::endl;
    std::cout << "=== Test Results ===" << std::endl;
    std::cout << "Passed: " << passed << "/" << total << std::endl;
    
    if (passed == total) {
        std::cout << "🎉 All tests PASSED! MIS system is ready for production." << std::endl;
        return 0;
    } else {
        std::cout << "❌ Some tests FAILED. Please review and fix issues." << std::endl;
        return 1;
    }
}
