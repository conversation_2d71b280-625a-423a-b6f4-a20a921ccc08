# src/ruby/photon_render/triangle_converter.rb
# PhotonRender - Face to Triangle Conversion System
# Sistema avanzato per convertire face SketchUp in triangoli PhotonRender

module PhotonRender
  module TriangleConverter
    
    # Convert SketchUp face to triangulated mesh data
    def self.convert_face_to_triangles(face, transform = nil)
      puts "Converting face to triangles..."
      
      # Get face mesh with all data
      mesh = face.mesh(7) # 7 = points + normals + UVs
      
      return nil if mesh.count_points == 0
      
      # Initialize transform if not provided
      transform ||= Geom::Transformation.new
      
      # Extract mesh data
      mesh_data = {
        vertices: extract_vertices(mesh, transform),
        normals: extract_normals(mesh, transform),
        uvs: extract_uvs(mesh, face),
        triangles: extract_triangles(mesh),
        material: get_face_material(face),
        area: calculate_face_area(face, transform),
        bounds: calculate_bounds(mesh, transform)
      }
      
      puts "Face converted: #{mesh_data[:triangles].size} triangles, #{mesh_data[:vertices].size} vertices"
      mesh_data
    end
    
    # Convert multiple faces to optimized mesh
    def self.convert_faces_to_mesh(faces, transform = nil)
      puts "Converting #{faces.size} faces to optimized mesh..."
      
      all_vertices = []
      all_normals = []
      all_uvs = []
      all_triangles = []
      vertex_offset = 0
      
      faces.each_with_index do |face, face_index|
        face_data = convert_face_to_triangles(face, transform)
        next unless face_data
        
        # Add vertices with offset
        all_vertices.concat(face_data[:vertices])
        all_normals.concat(face_data[:normals])
        all_uvs.concat(face_data[:uvs])
        
        # Add triangles with vertex offset
        face_data[:triangles].each do |triangle|
          all_triangles << [
            triangle[0] + vertex_offset,
            triangle[1] + vertex_offset,
            triangle[2] + vertex_offset
          ]
        end
        
        vertex_offset += face_data[:vertices].size
      end
      
      optimized_mesh = {
        vertices: all_vertices,
        normals: all_normals,
        uvs: all_uvs,
        triangles: all_triangles,
        stats: {
          original_faces: faces.size,
          total_vertices: all_vertices.size,
          total_triangles: all_triangles.size,
          vertex_density: all_vertices.size.to_f / faces.size
        }
      }
      
      puts "Mesh optimized: #{optimized_mesh[:stats][:total_triangles]} triangles from #{faces.size} faces"
      optimized_mesh
    end
    
    # Optimize mesh by removing duplicate vertices
    def self.optimize_mesh(mesh_data, tolerance = 1e-6)
      puts "Optimizing mesh (removing duplicates)..."
      
      original_vertex_count = mesh_data[:vertices].size
      vertex_map = {}
      new_vertices = []
      new_normals = []
      new_uvs = []
      vertex_remap = {}
      
      # Find unique vertices
      mesh_data[:vertices].each_with_index do |vertex, i|
        # Create vertex key for comparison
        key = vertex.map { |v| (v / tolerance).round * tolerance }
        
        if vertex_map[key]
          # Vertex already exists, map to existing index
          vertex_remap[i] = vertex_map[key]
        else
          # New unique vertex
          new_index = new_vertices.size
          vertex_map[key] = new_index
          vertex_remap[i] = new_index
          
          new_vertices << vertex
          new_normals << mesh_data[:normals][i] if mesh_data[:normals][i]
          new_uvs << mesh_data[:uvs][i] if mesh_data[:uvs][i]
        end
      end
      
      # Remap triangle indices
      new_triangles = mesh_data[:triangles].map do |triangle|
        [
          vertex_remap[triangle[0]],
          vertex_remap[triangle[1]],
          vertex_remap[triangle[2]]
        ]
      end
      
      optimized = {
        vertices: new_vertices,
        normals: new_normals,
        uvs: new_uvs,
        triangles: new_triangles,
        optimization_stats: {
          original_vertices: original_vertex_count,
          optimized_vertices: new_vertices.size,
          vertices_removed: original_vertex_count - new_vertices.size,
          compression_ratio: (1.0 - new_vertices.size.to_f / original_vertex_count) * 100
        }
      }
      
      puts "Optimization complete: #{optimized[:optimization_stats][:vertices_removed]} vertices removed (#{optimized[:optimization_stats][:compression_ratio].round(1)}% reduction)"
      optimized
    end
    
    private
    
    # Extract vertex positions
    def self.extract_vertices(mesh, transform)
      vertices = []
      
      (1..mesh.count_points).each do |i|
        point = mesh.point_at(i)
        transformed_point = transform * point
        vertices << [transformed_point.x, transformed_point.y, transformed_point.z]
      end
      
      vertices
    end
    
    # Extract vertex normals
    def self.extract_normals(mesh, transform)
      normals = []
      
      (1..mesh.count_points).each do |i|
        normal = mesh.normal_at(i)
        if normal
          transformed_normal = transform.rotation * normal
          transformed_normal.normalize!
          normals << [transformed_normal.x, transformed_normal.y, transformed_normal.z]
        else
          normals << [0.0, 0.0, 1.0] # Default normal
        end
      end
      
      normals
    end
    
    # Extract UV coordinates
    def self.extract_uvs(mesh, face)
      uvs = []
      
      (1..mesh.count_points).each do |i|
        if face.material && face.material.texture && mesh.uv_at(i, true)
          uv = mesh.uv_at(i, true)
          # Flip V coordinate for PhotonRender convention
          uvs << [uv.x, 1.0 - uv.y]
        else
          uvs << [0.0, 0.0] # Default UV
        end
      end
      
      uvs
    end
    
    # Extract triangle indices
    def self.extract_triangles(mesh)
      triangles = []
      
      (1..mesh.count_polygons).each do |i|
        polygon = mesh.polygon_at(i)
        
        case polygon.size
        when 3
          # Already a triangle
          triangles << [polygon[0] - 1, polygon[1] - 1, polygon[2] - 1] # Convert to 0-based
        when 4
          # Quad - split into two triangles using optimal diagonal
          triangles << [polygon[0] - 1, polygon[1] - 1, polygon[2] - 1]
          triangles << [polygon[0] - 1, polygon[2] - 1, polygon[3] - 1]
        else
          # N-gon - fan triangulation from first vertex
          (1..polygon.size - 2).each do |j|
            triangles << [polygon[0] - 1, polygon[j] - 1, polygon[j + 1] - 1]
          end
        end
      end
      
      triangles
    end
    
    # Get face material name
    def self.get_face_material(face)
      if face.material
        face.material.name
      elsif face.back_material
        face.back_material.name
      else
        "default"
      end
    end
    
    # Calculate face area
    def self.calculate_face_area(face, transform)
      # Get face area and apply transform scaling
      area = face.area
      
      # Apply transform scaling (simplified)
      scale_factor = 1.0
      if transform
        # Extract scale from transform (simplified)
        scale_vector = transform * Geom::Vector3d.new(1, 0, 0)
        scale_factor = scale_vector.length
      end
      
      area * scale_factor * scale_factor
    end
    
    # Calculate bounding box
    def self.calculate_bounds(mesh, transform)
      return nil if mesh.count_points == 0
      
      min_x = min_y = min_z = Float::INFINITY
      max_x = max_y = max_z = -Float::INFINITY
      
      (1..mesh.count_points).each do |i|
        point = mesh.point_at(i)
        transformed_point = transform * point
        
        min_x = [min_x, transformed_point.x].min
        min_y = [min_y, transformed_point.y].min
        min_z = [min_z, transformed_point.z].min
        
        max_x = [max_x, transformed_point.x].max
        max_y = [max_y, transformed_point.y].max
        max_z = [max_z, transformed_point.z].max
      end
      
      {
        min: [min_x, min_y, min_z],
        max: [max_x, max_y, max_z],
        size: [max_x - min_x, max_y - min_y, max_z - min_z],
        center: [(min_x + max_x) / 2, (min_y + max_y) / 2, (min_z + max_z) / 2]
      }
    end
    
    # Validate triangle mesh
    def self.validate_mesh(mesh_data)
      errors = []
      
      # Check vertex count
      if mesh_data[:vertices].empty?
        errors << "No vertices found"
      end
      
      # Check triangle indices
      max_index = mesh_data[:vertices].size - 1
      mesh_data[:triangles].each_with_index do |triangle, i|
        triangle.each do |index|
          if index < 0 || index > max_index
            errors << "Triangle #{i} has invalid vertex index: #{index}"
          end
        end
      end
      
      # Check normal count
      if mesh_data[:normals].size != mesh_data[:vertices].size
        errors << "Normal count (#{mesh_data[:normals].size}) doesn't match vertex count (#{mesh_data[:vertices].size})"
      end
      
      # Check UV count
      if mesh_data[:uvs].size != mesh_data[:vertices].size
        errors << "UV count (#{mesh_data[:uvs].size}) doesn't match vertex count (#{mesh_data[:vertices].size})"
      end
      
      errors
    end
    
  end
end
