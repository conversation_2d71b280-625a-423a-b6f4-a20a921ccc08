// src/debug_repeat_mode.cpp - Debug program for repeat mode issues
// PhotonRender Repeat Mode Debug Tool

#include <iostream>
#include <iomanip>
#include "photon/core/texture/texture.hpp"

using namespace photon;

int main() {
    std::cout << "=== PhotonRender Repeat Mode Debug Tool ===\n\n";
    
    // Test repeat mode exactly like the failing test
    std::cout << "Testing repeat mode with spread=0.5, repeat=true\n";
    std::cout << "Expected: UV(0.25) and UV(0.75) should be similar\n\n";
    
    GradientParams params = GradientParams::defaultParams();
    params.repeat = true;
    params.spread = 0.5f; // Make gradient repeat twice
    
    std::vector<ColorStop> colorStops = {
        ColorStop(0.0f, Color3(0.0f, 0.0f, 0.0f)),  // Black
        ColorStop(1.0f, Color3(1.0f, 1.0f, 1.0f))   // White
    };
    
    GradientTexture repeatGradient(GradientType::LINEAR, params, colorStops);
    
    std::cout << "Gradient Parameters:\n";
    std::cout << "  repeat: " << (params.repeat ? "true" : "false") << "\n";
    std::cout << "  spread: " << params.spread << "\n";
    std::cout << "  startPoint: (" << params.startPoint.x << ", " << params.startPoint.y << ")\n";
    std::cout << "  endPoint: (" << params.endPoint.x << ", " << params.endPoint.y << ")\n\n";
    
    // Test multiple points to understand the pattern
    Vec2 testPoints[] = {
        Vec2(0.0f, 0.5f),   // Start
        Vec2(0.125f, 0.5f), // 1/8
        Vec2(0.25f, 0.5f),  // 1/4 - Test point 1
        Vec2(0.375f, 0.5f), // 3/8
        Vec2(0.5f, 0.5f),   // 1/2 - Middle
        Vec2(0.625f, 0.5f), // 5/8
        Vec2(0.75f, 0.5f),  // 3/4 - Test point 2
        Vec2(0.875f, 0.5f), // 7/8
        Vec2(1.0f, 0.5f)    // End
    };
    
    std::cout << "Sampling results:\n";
    std::cout << "UV(x,y)      : Value    Color(r,g,b)\n";
    std::cout << "----------------------------------------\n";
    
    for (const auto& uv : testPoints) {
        float value = repeatGradient.sampleFloat(uv);
        Color3 color = repeatGradient.sample(uv);
        
        std::cout << std::fixed << std::setprecision(3);
        std::cout << "UV(" << uv.x << ", " << uv.y << "): ";
        std::cout << std::setw(6) << value << "   ";
        std::cout << "RGB(" << color.r << ", " << color.g << ", " << color.b << ")";
        
        if (uv.x == 0.25f) {
            std::cout << "  ← Test point 1";
        } else if (uv.x == 0.75f) {
            std::cout << "  ← Test point 2";
        }
        
        std::cout << "\n";
    }
    
    // Calculate the specific test values
    float value1 = repeatGradient.sampleFloat(Vec2(0.25f, 0.5f));
    float value2 = repeatGradient.sampleFloat(Vec2(0.75f, 0.5f));
    float difference = std::abs(value1 - value2);
    
    std::cout << "\n=== Test Analysis ===\n";
    std::cout << "UV(0.25, 0.5): Value = " << value1 << "\n";
    std::cout << "UV(0.75, 0.5): Value = " << value2 << "\n";
    std::cout << "Difference: " << difference << "\n";
    std::cout << "Test threshold: < 0.1\n";
    
    if (difference > 0.1f) {
        std::cout << "❌ REPEAT MODE FAILING!\n";
        
        std::cout << "\n=== Expected Behavior Analysis ===\n";
        std::cout << "With spread=0.5, the gradient should complete a full cycle in 0.5 UV units\n";
        std::cout << "So the pattern should be:\n";
        std::cout << "  UV(0.0) → t=0.0 → Black\n";
        std::cout << "  UV(0.25) → t=0.5 → White (half cycle)\n";
        std::cout << "  UV(0.5) → t=1.0 → Black (full cycle, repeat)\n";
        std::cout << "  UV(0.75) → t=1.5 → White (1.5 cycles)\n";
        std::cout << "  UV(1.0) → t=2.0 → Black (2 full cycles)\n";
        std::cout << "\nWith repeat mode, t=1.5 should become t=0.5\n";
        std::cout << "Therefore UV(0.25) and UV(0.75) should both give White (similar values)\n";
        
    } else {
        std::cout << "✅ Repeat mode working correctly!\n";
    }
    
    return 0;
}
