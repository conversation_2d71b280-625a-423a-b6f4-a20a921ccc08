# CMakeLists.txt
# PhotonRender - Professional Rendering Engine for SketchUp
# Root CMake configuration file

cmake_minimum_required(VERSION 3.20)
project(PhotonRender VERSION 0.1.0 LANGUAGES CXX C)

# C++ Standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Build type" FORCE)
endif()

# Options
option(BUILD_TESTS "Build unit tests" ON)
option(BUILD_BENCHMARKS "Build benchmarks" ON)
option(USE_CUDA "Enable CUDA support" ON)
option(USE_OPTIX "Enable OptiX support" ON)
option(USE_OPENMP "Enable OpenMP" ON)
option(BUILD_RUBY_BINDINGS "Build Ruby bindings for SketchUp" ON)

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find packages
find_package(Threads REQUIRED)
find_package(OpenMP)

# External dependencies
include(FetchContent)

# Intel TBB
FetchContent_Declare(
    TBB
    GIT_REPOSITORY https://github.com/oneapi-src/oneTBB.git
    GIT_TAG v2021.12.0
)

# Google Test
if(BUILD_TESTS)
    FetchContent_Declare(
        googletest
        GIT_REPOSITORY https://github.com/google/googletest.git
        GIT_TAG v1.13.0
    )
endif()

# Embree
FetchContent_Declare(
    embree
    GIT_REPOSITORY https://github.com/embree/embree.git
    GIT_TAG v4.3.3
)

# STB Image
FetchContent_Declare(
    stb
    GIT_REPOSITORY https://github.com/nothings/stb.git
    GIT_TAG master
)

# Eigen
FetchContent_Declare(
    Eigen3
    GIT_REPOSITORY https://gitlab.com/libeigen/eigen.git
    GIT_TAG 3.4.0
)

# Configure dependencies
FetchContent_MakeAvailable(TBB stb embree)

# Configure Eigen3 as header-only (no targets, no conflicts)
FetchContent_GetProperties(Eigen3)
if(NOT eigen3_POPULATED)
    FetchContent_Populate(Eigen3)
    # Don't add subdirectory - use Eigen as header-only
    # This avoids the uninstall target conflict completely
endif()

# Create Eigen3::Eigen target manually for header-only usage
if(NOT TARGET Eigen3::Eigen)
    add_library(Eigen3::Eigen INTERFACE IMPORTED)
    target_include_directories(Eigen3::Eigen INTERFACE ${eigen3_SOURCE_DIR})
    target_compile_definitions(Eigen3::Eigen INTERFACE EIGEN_MPL2_ONLY)
endif()



if(BUILD_TESTS)
    FetchContent_MakeAvailable(googletest)
endif()

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
    ${embree_SOURCE_DIR}/include
    ${TBB_SOURCE_DIR}/include
    ${stb_SOURCE_DIR}
    ${eigen3_SOURCE_DIR}
)

# Compiler flags
if(MSVC)
    add_compile_options(/W4 /WX- /MP /permissive-)
    add_compile_options(/fp:fast /arch:AVX2)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    add_compile_options(-march=native -ffast-math)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0)
    else()
        add_compile_options(-O3)
    endif()
endif()

# Core library
file(GLOB_RECURSE CORE_SOURCES 
    "src/core/*.cpp"
    "src/core/*.hpp"
)

add_library(photon_core STATIC ${CORE_SOURCES})
target_link_libraries(photon_core 
    PUBLIC 
        embree
        TBB::tbb
        Threads::Threads
        Eigen3::Eigen
)

if(OpenMP_FOUND)
    target_link_libraries(photon_core PUBLIC OpenMP::OpenMP_CXX)
endif()

# CUDA support
if(USE_CUDA)
    enable_language(CUDA)
    find_package(CUDAToolkit REQUIRED)

    # CUDA sources
    set(CUDA_SOURCES
        src/gpu/cuda/cuda_renderer.cu
        src/gpu/cuda/cuda_integration.cpp
    )

    # Create CUDA library
    add_library(photon_cuda STATIC ${CUDA_SOURCES})

    # CUDA properties - use more compatible settings
    set_target_properties(photon_cuda PROPERTIES
        CUDA_SEPARABLE_COMPILATION OFF
        CUDA_ARCHITECTURES "75;80;86;89"  # Multiple architectures for compatibility
        CUDA_STANDARD 17
        POSITION_INDEPENDENT_CODE ON
    )

    # CUDA include directories
    target_include_directories(photon_cuda
        PUBLIC
            ${CMAKE_SOURCE_DIR}/include
            ${CMAKE_SOURCE_DIR}/src
    )

    # CUDA libraries
    target_link_libraries(photon_cuda
        PUBLIC
            CUDA::cudart
            CUDA::curand
    )

    # Link CUDA to core
    target_link_libraries(photon_core PUBLIC photon_cuda)
    target_compile_definitions(photon_core PUBLIC USE_CUDA)

    # OptiX support
    if(USE_OPTIX)
        # Set OptiX paths manually
        set(OPTIX_ROOT_DIR "C:/ProgramData/NVIDIA Corporation/OptiX SDK 9.0.0")
        set(OPTIX_INCLUDE_DIR "${OPTIX_ROOT_DIR}/include")

        # Check if OptiX is installed
        if(EXISTS "${OPTIX_INCLUDE_DIR}/optix.h")
            message(STATUS "OptiX 9.0.0 found at: ${OPTIX_ROOT_DIR}")
            target_include_directories(photon_cuda PUBLIC ${OPTIX_INCLUDE_DIR})
            target_compile_definitions(photon_cuda PUBLIC USE_OPTIX)
            target_compile_definitions(photon_cuda PUBLIC OPTIX_VERSION=90000)
        else()
            message(FATAL_ERROR "OptiX not found at ${OPTIX_ROOT_DIR}")
        endif()
    endif()

    # CUDA test executable (temporarily disabled - file missing)
    # add_executable(test_cuda src/test_cuda.cpp)
    # target_link_libraries(test_cuda photon_cuda)
    # target_include_directories(test_cuda PRIVATE include src)
endif()

# Ruby extension for SketchUp
# add_subdirectory(src/bindings)  # Temporarily disabled - directory missing

# Executable for testing
add_executable(photon_render src/main.cpp)
target_link_libraries(photon_render photon_core)

# Disney BRDF test executable
add_executable(test_disney_brdf src/test_disney_brdf_main.cpp)
target_link_libraries(test_disney_brdf photon_core)
target_include_directories(test_disney_brdf PRIVATE include src)

# PBR Validation test executable
add_executable(test_pbr_validation src/test_pbr_validation.cpp)
target_link_libraries(test_pbr_validation photon_core)
target_include_directories(test_pbr_validation PRIVATE include src)

# PBR Validation simple test executable (removed - obsolete)

# HDRI Environment test executable
add_executable(test_hdri_environment src/test_hdri_environment.cpp)
target_link_libraries(test_hdri_environment photon_core)
target_include_directories(test_hdri_environment PRIVATE include src)

# Area Lights test executable
add_executable(test_area_lights src/test_area_lights.cpp)
target_link_libraries(test_area_lights photon_core)
target_include_directories(test_area_lights PRIVATE include src)

# MIS System test executable
add_executable(test_mis_system src/test_mis_system.cpp)
target_link_libraries(test_mis_system photon_core)
target_include_directories(test_mis_system PRIVATE include src)

# Light Linking test executable
add_executable(test_light_linking src/test_light_linking.cpp)
target_link_libraries(test_light_linking photon_core)
target_include_directories(test_light_linking PRIVATE include src)

# Advanced Lights test executable (DISABLED - Architecture conflicts)
# add_executable(test_advanced_lights src/test_advanced_lights.cpp)
# target_link_libraries(test_advanced_lights photon_core)
# target_include_directories(test_advanced_lights PRIVATE include src)

# Lighting Performance test executable
add_executable(test_lighting_performance src/test_lighting_performance.cpp)
target_link_libraries(test_lighting_performance photon_core)
target_include_directories(test_lighting_performance PRIVATE include src)

# UV Mapping Enhancement test executable
add_executable(test_uv_mapping src/test_uv_mapping.cpp)
target_link_libraries(test_uv_mapping photon_core)
target_include_directories(test_uv_mapping PRIVATE include src)

# Multiple UV Sets test executable
add_executable(test_multiple_uv_sets src/test_multiple_uv_sets.cpp)
target_link_libraries(test_multiple_uv_sets photon_core)
target_include_directories(test_multiple_uv_sets PRIVATE include src)

# UV Mapping Final test executable (Task 1.4)
add_executable(test_uv_mapping_final src/test_uv_mapping_final.cpp)
target_link_libraries(test_uv_mapping_final photon_core)
target_include_directories(test_uv_mapping_final PRIVATE include src)

# Pattern Generators test executable (Task 2.2)
add_executable(test_pattern_generators src/test_pattern_generators.cpp)
target_link_libraries(test_pattern_generators photon_core)
target_include_directories(test_pattern_generators PRIVATE include src)

# Pattern Generators demo executable
add_executable(pattern_generators_demo examples/pattern_generators_demo.cpp)
target_link_libraries(pattern_generators_demo photon_core)
target_include_directories(pattern_generators_demo PRIVATE include src)

# Linear Gradients test executable (Task 2.3.1)
add_executable(test_linear_gradients src/test_linear_gradients.cpp)
target_link_libraries(test_linear_gradients photon_core)
target_include_directories(test_linear_gradients PRIVATE include src)

# Radial Gradients test executable (Task 2.3.2)
add_executable(test_radial_gradients src/test_radial_gradients.cpp)
target_link_libraries(test_radial_gradients photon_core)
target_include_directories(test_radial_gradients PRIVATE include src)

# Debug gradient executable
add_executable(debug_gradient src/debug_gradient.cpp)
target_link_libraries(debug_gradient photon_core)

# Debug repeat mode executable
add_executable(debug_repeat_mode src/debug_repeat_mode.cpp)
target_link_libraries(debug_repeat_mode photon_core)
target_include_directories(debug_gradient PRIVATE include src)

# Benchmark executable (temporarily disabled - file missing)
# add_executable(photon_benchmark src/benchmark_embree_vs_mock.cpp)
# target_link_libraries(photon_benchmark photon_core)
# target_include_directories(photon_benchmark PRIVATE include)

# Tests
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Benchmarks
if(BUILD_BENCHMARKS)
    add_subdirectory(benchmarks)
endif()

# Installation
install(TARGETS photon_core photon_render
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/ DESTINATION include)

# Package configuration
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/PhotonRenderConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

# Status messages
# Ruby bindings for SketchUp
if(BUILD_RUBY_BINDINGS)
    # Find Ruby
    set(RUBY_ROOT "C:/Ruby34-x64")
    set(RUBY_INCLUDE_DIR "${RUBY_ROOT}/include/ruby-3.4.0")
    set(RUBY_LIBRARY "${RUBY_ROOT}/lib/libx64-ucrt-ruby340.dll.a")

    # Check if Ruby is available
    if(EXISTS "${RUBY_INCLUDE_DIR}/ruby.h" AND EXISTS "${RUBY_LIBRARY}")
        message(STATUS "Ruby 3.4.0 found at: ${RUBY_ROOT}")

        # Create Ruby extension
        add_library(photon_ruby_ext SHARED
            src/bindings/ruby_bindings.cpp
        )

        # Set Ruby extension properties
        set_target_properties(photon_ruby_ext PROPERTIES
            PREFIX ""
            SUFFIX ".so"
            OUTPUT_NAME "photon_core"
        )

        # Include directories
        target_include_directories(photon_ruby_ext PRIVATE
            ${RUBY_INCLUDE_DIR}
            ${RUBY_INCLUDE_DIR}/x64-mingw-ucrt
            include
            src
        )

        # Link libraries
        target_link_libraries(photon_ruby_ext PRIVATE
            ${RUBY_LIBRARY}
            photon_core
        )

        # Compile definitions
        target_compile_definitions(photon_ruby_ext PRIVATE
            RUBY_EXTCONF_H
            _CRT_SECURE_NO_WARNINGS
        )

        message(STATUS "Ruby bindings will be built")
    else()
        message(WARNING "Ruby development files not found - skipping Ruby bindings")
        set(BUILD_RUBY_BINDINGS OFF)
    endif()
endif()

message(STATUS "")
message(STATUS "PhotonRender ${PROJECT_VERSION}")
message(STATUS "")
message(STATUS "Build type:        ${CMAKE_BUILD_TYPE}")
message(STATUS "CUDA support:      ${USE_CUDA}")
message(STATUS "OptiX support:     ${USE_OPTIX}")
message(STATUS "OpenMP support:    ${OpenMP_FOUND}")
message(STATUS "Ruby bindings:     ${BUILD_RUBY_BINDINGS}")
message(STATUS "Tests:             ${BUILD_TESTS}")
message(STATUS "Benchmarks:        ${BUILD_BENCHMARKS}")
message(STATUS "")