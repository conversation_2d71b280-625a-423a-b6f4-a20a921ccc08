// src/core/light/disk_light.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Disk Area Light implementation

#include "disk_light.hpp"
#include "../sampler/sampler.hpp"
#include "../math/ray.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

namespace photon {

DiskLight::DiskLight(const Vec3& center, const Vec3& normal, float radius,
                     const Color3& emission, float intensity, bool twoSided)
    : AreaLightBase(emission, intensity, twoSided), m_center(center), 
      m_normal(normal.normalized()), m_radius(std::max(0.0f, radius)) {
    updateCachedValues();
}

void DiskLight::sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const {
    Vec2 u = sampler.next2D();
    
    if (m_samplingMode == AreaLightSampling::UNIFORM) {
        // Use concentric disk sampling for better distribution
        point = sampleConcentric(u);
    } else {
        // Uniform sampling fallback
        point = sampleUniform(u);
    }
    
    normal = m_normal;
    pdf = 1.0f / m_area;
}

float DiskLight::getArea() const {
    return m_area;
}

bool DiskLight::intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const {
    // Intersect ray with disk plane
    float denom = ray.d.dot(m_normal);
    if (std::abs(denom) < 1e-6f) {
        return false; // Ray is parallel to plane
    }

    float t_plane = (m_center - ray.o).dot(m_normal) / denom;
    if (t_plane < 0.0f) {
        return false; // Intersection behind ray origin
    }

    // Compute intersection point
    Vec3 hitPoint = ray.o + t_plane * ray.d;
    
    // Check if point is inside disk
    if (!isInsideDisk(hitPoint)) {
        return false; // Outside disk bounds
    }
    
    t = t_plane;
    point = hitPoint;
    normal = m_normal;
    
    return true;
}

void DiskLight::setGeometry(const Vec3& center, const Vec3& normal, float radius) {
    m_center = center;
    m_normal = normal.normalized();
    m_radius = std::max(0.0f, radius);
    updateCachedValues();
}

Vec3 DiskLight::sampleUniform(const Vec2& u) const {
    Vec2 diskPoint = sampleUniformDiskRejection(u);
    return localToWorld(diskPoint * m_radius);
}

Vec3 DiskLight::sampleConcentric(const Vec2& u) const {
    Vec2 diskPoint = sampleConcentricDisk(u);
    return localToWorld(diskPoint * m_radius);
}

float DiskLight::getSolidAngle(const Vec3& point) const {
    return AreaLightUtils::diskSolidAngle(point, m_center, m_normal, m_radius);
}

float DiskLight::getDistanceToCenter(const Vec3& point) const {
    return (point - m_center).length();
}

bool DiskLight::isInsideDisk(const Vec3& point) const {
    // Project point onto disk plane
    Vec3 toPoint = point - m_center;
    float distanceAlongNormal = toPoint.dot(m_normal);
    
    // Check if point is on the plane (within tolerance)
    if (std::abs(distanceAlongNormal) > 1e-4f) {
        return false;
    }
    
    // Check if point is within disk radius
    Vec3 projectedPoint = point - distanceAlongNormal * m_normal;
    float distanceFromCenter = (projectedPoint - m_center).length();
    
    return distanceFromCenter <= m_radius;
}

void DiskLight::updateCachedValues() {
    m_area = M_PI * m_radius * m_radius;
    
    // Create orthonormal basis
    if (std::abs(m_normal.x) > 0.1f) {
        m_tangent = Vec3(0, 1, 0).cross(m_normal).normalized();
    } else {
        m_tangent = Vec3(1, 0, 0).cross(m_normal).normalized();
    }
    m_bitangent = m_normal.cross(m_tangent);
}

Vec2 DiskLight::worldToLocal(const Vec3& worldPoint) const {
    Vec3 relative = worldPoint - m_center;
    float x = relative.dot(m_tangent);
    float y = relative.dot(m_bitangent);
    return Vec2(x, y);
}

Vec3 DiskLight::localToWorld(const Vec2& localPoint) const {
    return m_center + localPoint.x * m_tangent + localPoint.y * m_bitangent;
}

Vec2 DiskLight::sampleUniformDiskRejection(const Vec2& u) const {
    // Simple rejection sampling (less efficient but straightforward)
    float r = std::sqrt(u.x);
    float theta = 2.0f * M_PI * u.y;
    return Vec2(r * std::cos(theta), r * std::sin(theta));
}

Vec2 DiskLight::sampleConcentricDisk(const Vec2& u) const {
    // Concentric disk sampling (Shirley & Chiu)
    Vec2 uOffset = 2.0f * u - Vec2(1.0f, 1.0f);
    
    if (uOffset.x == 0 && uOffset.y == 0) {
        return Vec2(0, 0);
    }
    
    float theta, r;
    if (std::abs(uOffset.x) > std::abs(uOffset.y)) {
        r = uOffset.x;
        theta = (M_PI / 4.0f) * (uOffset.y / uOffset.x);
    } else {
        r = uOffset.y;
        theta = (M_PI / 2.0f) - (M_PI / 4.0f) * (uOffset.x / uOffset.y);
    }
    
    return Vec2(r * std::cos(theta), r * std::sin(theta));
}

// Factory functions
namespace DiskLightFactory {

std::shared_ptr<DiskLight> create(
    const Vec3& center, const Vec3& normal, float radius,
    const Color3& emission, float intensity, bool twoSided) {
    return std::make_shared<DiskLight>(center, normal, radius, emission, intensity, twoSided);
}

std::shared_ptr<DiskLight> createCeiling(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    Vec3 normal(0, -1, 0); // Facing down
    return create(center, normal, radius, emission, intensity, false);
}

std::shared_ptr<DiskLight> createFloor(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    Vec3 normal(0, 1, 0); // Facing up
    return create(center, normal, radius, emission, intensity, false);
}

std::shared_ptr<DiskLight> createWall(
    const Vec3& center, const Vec3& wallNormal, float radius,
    const Color3& emission, float intensity) {
    return create(center, wallNormal, radius, emission, intensity, false);
}

std::shared_ptr<DiskLight> createBulb(
    const Vec3& center, float radius,
    const Color3& emission, float intensity) {
    Vec3 normal(0, 1, 0); // Default up direction
    return create(center, normal, radius, emission, intensity, true); // Two-sided for bulb
}

} // namespace DiskLightFactory

} // namespace photon
