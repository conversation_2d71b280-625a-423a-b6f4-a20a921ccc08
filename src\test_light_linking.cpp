// src/test_light_linking.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light Linking System test suite

#include "core/scene/light_linking.hpp"
#include "core/scene/scene.hpp"
#include "core/scene/light.hpp"
#include "core/integrator/light_linked_integrator.hpp"
#include "core/material/material.hpp"
#include "core/sampler/sampler.hpp"
#include "core/math/vec3.hpp"
#include "core/common.hpp"
#include <iostream>
#include <chrono>
#include <vector>
#include <memory>
#include <cassert>
#include <cmath>

using namespace photon;

/**
 * @brief Test utilities for light linking
 */
namespace TestUtils {
    
    /**
     * @brief Create test scene with multiple lights
     */
    std::unique_ptr<Scene> createTestScene() {
        auto scene = std::make_unique<Scene>();
        
        // Add multiple lights of different types
        auto pointLight1 = std::make_shared<PointLight>(Point3(2, 5, 0), Color3(20.0f));
        auto pointLight2 = std::make_shared<PointLight>(Point3(-2, 5, 0), Color3(15.0f));
        auto pointLight3 = std::make_shared<PointLight>(Point3(0, 5, 2), Color3(10.0f));
        
        auto dirLight = std::make_shared<DirectionalLight>(Vec3(0, -1, -1).normalized(), Color3(5.0f));
        auto envLight = std::make_shared<EnvironmentLight>(Color3(1.0f));
        
        scene->addLight(pointLight1);
        scene->addLight(pointLight2);
        scene->addLight(pointLight3);
        scene->addLight(dirLight);
        scene->addLight(envLight);
        
        return scene;
    }
    
    /**
     * @brief Create test intersection
     */
    Intersection createTestIntersection(uint32_t objectId = 0) {
        Intersection isect;
        isect.p = Point3(0, 0, 0);
        isect.n = Normal3(0, 1, 0);
        isect.t = 1.0f;
        isect.geometryId = objectId;
        
        // Create simple diffuse material
        auto material = std::make_shared<DiffuseMaterial>(Color3(0.8f, 0.6f, 0.4f));
        isect.material = material;
        
        return isect;
    }
    
    /**
     * @brief Performance timer
     */
    class Timer {
    public:
        void start() { m_start = std::chrono::high_resolution_clock::now(); }
        
        float stop() {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<float, std::milli>(end - m_start).count();
        }
        
    private:
        std::chrono::high_resolution_clock::time_point m_start;
    };
}

/**
 * @brief Test 1: Light Group Basic Functionality
 */
bool testLightGroupBasics() {
    std::cout << "Testing Light Group Basics..." << std::endl;
    
    try {
        auto scene = TestUtils::createTestScene();
        const auto& lights = scene->getLights();
        
        // Create light group
        LightGroup group("Test Group", "Test description");
        
        // Add lights to group
        group.addLight(lights[0]);
        group.addLight(lights[1]);
        
        // Test basic functionality
        assert(group.containsLight(lights[0]));
        assert(group.containsLight(lights[1]));
        assert(!group.containsLight(lights[2]));
        assert(group.getLights().size() == 2);
        assert(group.isEnabled());
        
        // Test enable/disable
        group.setEnabled(false);
        assert(!group.isEnabled());
        
        // Test statistics
        auto stats = group.getStatistics();
        assert(stats.lightCount == 2);
        assert(stats.totalPower > 0.0f);
        
        // Test remove light
        group.removeLight(lights[0]);
        assert(!group.containsLight(lights[0]));
        assert(group.getLights().size() == 1);
        
        std::cout << "✅ Light Group Basics: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Light Group Basics: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 2: Object Light Association
 */
bool testObjectLightAssociation() {
    std::cout << "Testing Object Light Association..." << std::endl;
    
    try {
        auto scene = TestUtils::createTestScene();
        const auto& lights = scene->getLights();
        
        // Test INCLUDE_ALL mode (default)
        ObjectLightAssociation assoc1(1, LightLinkingMode::INCLUDE_ALL);
        assert(assoc1.shouldIlluminate(lights[0]));
        assert(assoc1.shouldIlluminate(lights[1]));
        
        // Test EXCLUDE_ALL mode
        ObjectLightAssociation assoc2(2, LightLinkingMode::EXCLUDE_ALL);
        assert(!assoc2.shouldIlluminate(lights[0]));
        assert(!assoc2.shouldIlluminate(lights[1]));
        
        // Test SELECTIVE_INCLUDE mode
        ObjectLightAssociation assoc3(3, LightLinkingMode::SELECTIVE_INCLUDE);
        assoc3.includeLight(lights[0]);
        assoc3.includeLight(lights[2]);
        
        assert(assoc3.shouldIlluminate(lights[0]));
        assert(!assoc3.shouldIlluminate(lights[1]));
        assert(assoc3.shouldIlluminate(lights[2]));
        
        // Test SELECTIVE_EXCLUDE mode
        ObjectLightAssociation assoc4(4, LightLinkingMode::SELECTIVE_EXCLUDE);
        assoc4.excludeLight(lights[1]);
        
        assert(assoc4.shouldIlluminate(lights[0]));
        assert(!assoc4.shouldIlluminate(lights[1]));
        assert(assoc4.shouldIlluminate(lights[2]));
        
        // Test clear
        assoc3.clear();
        assert(assoc3.getMode() == LightLinkingMode::INCLUDE_ALL);
        assert(assoc3.getIncludedLights().empty());
        assert(assoc3.getExcludedLights().empty());
        
        std::cout << "✅ Object Light Association: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Object Light Association: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 3: Light Linking Manager
 */
bool testLightLinkingManager() {
    std::cout << "Testing Light Linking Manager..." << std::endl;
    
    try {
        auto scene = TestUtils::createTestScene();
        const auto& lights = scene->getLights();
        
        LightLinkingManager manager;
        
        // Test light group creation
        auto group1 = manager.createLightGroup("Key Lights", "Primary lights");
        auto group2 = manager.createLightGroup("Fill Lights", "Secondary lights");
        
        assert(group1 != nullptr);
        assert(group2 != nullptr);
        assert(manager.getLightGroup("Key Lights") == group1);
        assert(manager.getLightGroup("Nonexistent") == nullptr);
        
        // Add lights to groups
        group1->addLight(lights[0]);
        group1->addLight(lights[3]); // Directional light
        group2->addLight(lights[1]);
        group2->addLight(lights[2]);
        
        // Test object associations
        auto assoc1 = std::make_shared<ObjectLightAssociation>(1, LightLinkingMode::SELECTIVE_INCLUDE);
        assoc1->includeLight(lights[0]);
        assoc1->includeLight(lights[1]);
        
        manager.setObjectLightAssociation(1, assoc1);
        assert(manager.getObjectLightAssociation(1) == assoc1);
        
        // Test shouldIlluminate
        auto isect1 = TestUtils::createTestIntersection(1);
        assert(manager.shouldIlluminate(lights[0], isect1)); // Included
        assert(manager.shouldIlluminate(lights[1], isect1)); // Included
        assert(!manager.shouldIlluminate(lights[2], isect1)); // Not included
        
        // Test getEffectiveLights
        auto effectiveLights = manager.getEffectiveLights(lights, isect1);
        assert(effectiveLights.size() == 2); // Only lights[0] and lights[1]
        
        // Test with object without association (should get all lights)
        auto isect2 = TestUtils::createTestIntersection(999);
        auto allEffectiveLights = manager.getEffectiveLights(lights, isect2);
        assert(allEffectiveLights.size() == lights.size());
        
        // Test statistics
        auto stats = manager.getStatistics();
        assert(stats.lightGroupCount == 2);
        assert(stats.objectAssociationCount == 1);
        
        std::cout << "✅ Light Linking Manager: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Light Linking Manager: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 4: Light Linking Utilities
 */
bool testLightLinkingUtils() {
    std::cout << "Testing Light Linking Utilities..." << std::endl;
    
    try {
        auto scene = TestUtils::createTestScene();
        const auto& lights = scene->getLights();
        
        LightLinkingManager manager;
        
        // Test auto-categorization
        LightLinkingUtils::autoCategorizeLight(manager, lights);
        
        // Should have created groups for each light type
        assert(manager.getLightGroup("Point Lights") != nullptr);
        assert(manager.getLightGroup("Directional Lights") != nullptr);
        assert(manager.getLightGroup("Environment Lights") != nullptr);
        
        // Test default light groups
        LightLinkingManager manager2;
        LightLinkingUtils::createDefaultLightGroups(manager2, lights);
        
        assert(manager2.getLightGroup("Key Lights") != nullptr);
        assert(manager2.getLightGroup("Fill Lights") != nullptr);
        assert(manager2.getLightGroup("Environment") != nullptr);
        
        // Test object association patterns
        auto interiorAssoc = LightLinkingUtils::createObjectAssociation(1, "interior", lights);
        assert(interiorAssoc != nullptr);
        assert(interiorAssoc->getMode() == LightLinkingMode::SELECTIVE_INCLUDE);
        
        auto exteriorAssoc = LightLinkingUtils::createObjectAssociation(2, "exterior", lights);
        assert(exteriorAssoc != nullptr);
        assert(exteriorAssoc->getMode() == LightLinkingMode::SELECTIVE_INCLUDE);
        
        // Test optimization analysis
        auto suggestions = LightLinkingUtils::analyzeAndOptimize(manager, lights.size(), 100);
        assert(!suggestions.suggestions.empty());
        
        std::cout << "✅ Light Linking Utilities: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Light Linking Utilities: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Test 5: Performance Benchmarks
 */
bool testPerformanceBenchmarks() {
    std::cout << "Testing Performance Benchmarks..." << std::endl;
    
    try {
        auto scene = TestUtils::createTestScene();
        const auto& lights = scene->getLights();
        
        LightLinkingManager& manager = scene->getLightLinkingManager();
        
        // Setup complex light linking scenario
        auto group1 = manager.createLightGroup("Group1");
        auto group2 = manager.createLightGroup("Group2");
        
        group1->addLight(lights[0]);
        group1->addLight(lights[1]);
        group2->addLight(lights[2]);
        group2->addLight(lights[3]);
        
        // Create multiple object associations
        for (uint32_t i = 0; i < 50; ++i) {
            auto assoc = std::make_shared<ObjectLightAssociation>(i, LightLinkingMode::SELECTIVE_INCLUDE);
            assoc->includeLight(lights[i % lights.size()]);
            manager.setObjectLightAssociation(i, assoc);
        }
        
        // Benchmark light linking performance
        const int numQueries = 1000;
        TestUtils::Timer timer;
        
        timer.start();
        for (int i = 0; i < numQueries; ++i) {
            auto isect = TestUtils::createTestIntersection(i % 50);
            auto effectiveLights = manager.getEffectiveLights(lights, isect);
            // Prevent optimization
            volatile size_t count = effectiveLights.size();
            (void)count;
        }
        float totalTime = timer.stop();
        
        float avgTime = totalTime / numQueries;
        std::cout << "  Average light linking time: " << avgTime << " ms" << std::endl;
        
        // Performance should be reasonable (< 0.1ms per query)
        assert(avgTime < 0.1f);
        
        // Test statistics
        auto stats = manager.getStatistics();
        std::cout << "  Light groups: " << stats.lightGroupCount << std::endl;
        std::cout << "  Object associations: " << stats.objectAssociationCount << std::endl;
        std::cout << "  Linking overhead: " << stats.linkingOverhead << " ns" << std::endl;
        
        // Overhead should be reasonable (< 1000ns)
        assert(stats.linkingOverhead < 1000.0f);
        
        std::cout << "✅ Performance Benchmarks: PASSED" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Performance Benchmarks: FAILED - " << e.what() << std::endl;
        return false;
    }
}

/**
 * @brief Main test runner
 */
int main() {
    std::cout << "=== PhotonRender Light Linking System Test Suite ===" << std::endl;
    std::cout << std::endl;
    
    int passed = 0;
    int total = 5;
    
    // Run all tests
    if (testLightGroupBasics()) passed++;
    if (testObjectLightAssociation()) passed++;
    if (testLightLinkingManager()) passed++;
    if (testLightLinkingUtils()) passed++;
    if (testPerformanceBenchmarks()) passed++;
    
    std::cout << std::endl;
    std::cout << "=== Test Results ===" << std::endl;
    std::cout << "Passed: " << passed << "/" << total << std::endl;
    
    if (passed == total) {
        std::cout << "🎉 All tests PASSED! Light Linking system is ready for production." << std::endl;
        return 0;
    } else {
        std::cout << "❌ Some tests FAILED. Please review and fix issues." << std::endl;
        return 1;
    }
}
