// examples/noise_functions_demo.cpp
// PhotonRender - Advanced Noise Functions Demo
// Demonstrates usage of Perlin, Simplex, and Worley noise

#include <iostream>
#include <memory>
#include <vector>
#include <fstream>
#include "../src/core/texture/texture.hpp"
#include "../src/core/texture/uv_mapping.hpp"
#include "../src/core/math/vec2.hpp"
#include "../src/core/math/vec3.hpp"
#include "../src/core/math/color3.hpp"

using namespace photon;

// Function to save noise texture as simple text representation
void saveNoiseAsText(const NoiseTexture& noise, const std::string& filename, int width = 64, int height = 64) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return;
    }
    
    file << "Noise Texture (" << width << "x" << height << ")\n";
    file << "Legend: . = low, o = medium, O = high, # = very high\n\n";
    
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            float u = static_cast<float>(x) / (width - 1);
            float v = static_cast<float>(y) / (height - 1);
            
            float value = noise.sampleFloat(Vec2(u, v));
            
            char symbol;
            if (value < 0.25f) symbol = '.';
            else if (value < 0.5f) symbol = 'o';
            else if (value < 0.75f) symbol = 'O';
            else symbol = '#';
            
            file << symbol;
        }
        file << '\n';
    }
    
    file.close();
    std::cout << "Saved noise texture to: " << filename << std::endl;
}

// Function to demonstrate basic noise usage
void demonstrateBasicNoise() {
    std::cout << "\n=== Basic Noise Functions Demo ===\n";
    
    // Create different noise types
    NoiseTexture perlinNoise(NoiseType::PERLIN, 8.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture simplexNoise(NoiseType::SIMPLEX, 8.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture worleyNoise(NoiseType::WORLEY, 6.0f, 1.0f, FractalParams::defaultParams());
    
    // Sample at specific coordinates
    Vec2 testCoord(2.5f, 3.7f);
    
    float perlinValue = perlinNoise.sampleFloat(testCoord);
    float simplexValue = simplexNoise.sampleFloat(testCoord);
    float worleyValue = worleyNoise.sampleFloat(testCoord);
    
    std::cout << "Sampling at UV(" << testCoord.x << ", " << testCoord.y << "):\n";
    std::cout << "  Perlin Noise:  " << perlinValue << "\n";
    std::cout << "  Simplex Noise: " << simplexValue << "\n";
    std::cout << "  Worley Noise:  " << worleyValue << "\n";
    
    // Save visual representations
    saveNoiseAsText(perlinNoise, "perlin_noise.txt");
    saveNoiseAsText(simplexNoise, "simplex_noise.txt");
    saveNoiseAsText(worleyNoise, "worley_noise.txt");
}

// Function to demonstrate fractal parameters
void demonstrateFractalNoise() {
    std::cout << "\n=== Fractal Noise Demo ===\n";
    
    // Different fractal configurations
    std::vector<std::pair<std::string, FractalParams>> configs = {
        {"Simple (1 octave)", {1, 2.0f, 0.5f, 1.0f}},
        {"Standard (4 octaves)", {4, 2.0f, 0.5f, 1.0f}},
        {"Detailed (8 octaves)", {8, 2.0f, 0.3f, 1.0f}},
        {"Rough (6 octaves, high persistence)", {6, 2.0f, 0.8f, 1.0f}},
        {"Fine (5 octaves, high lacunarity)", {5, 3.0f, 0.4f, 1.0f}}
    };
    
    Vec2 testCoord(1.5f, 2.0f);
    
    for (const auto& config : configs) {
        NoiseTexture fractalNoise(NoiseType::PERLIN, 4.0f, 1.0f, config.second);
        float value = fractalNoise.sampleFloat(testCoord);
        
        std::cout << config.first << ": " << value << "\n";
        std::cout << "  Octaves: " << config.second.octaves 
                  << ", Lacunarity: " << config.second.lacunarity
                  << ", Persistence: " << config.second.persistence << "\n";
    }
}

// Function to demonstrate UV mapping integration
void demonstrateUVMapping() {
    std::cout << "\n=== UV Mapping Integration Demo ===\n";
    
    NoiseTexture noise(NoiseType::PERLIN, 6.0f, 1.0f, FractalParams::defaultParams());
    
    // Different UV mapping modes
    std::vector<std::pair<std::string, UVMappingMode>> mappings = {
        {"Planar XY", UVMappingMode::PLANAR_XY},
        {"Planar XZ", UVMappingMode::PLANAR_XZ},
        {"Cylindrical Y", UVMappingMode::CYLINDRICAL_Y},
        {"Spherical", UVMappingMode::SPHERICAL},
        {"Cubic", UVMappingMode::CUBIC}
    };
    
    Vec3 testPosition(2.0f, 1.5f, 3.0f);
    Vec3 normal(0.0f, 1.0f, 0.0f);
    
    std::cout << "3D Position: (" << testPosition.x << ", " << testPosition.y << ", " << testPosition.z << ")\n";
    
    for (const auto& mapping : mappings) {
        UVMapping uvMapper(mapping.second);
        Vec2 uv = uvMapper.generateUV(testPosition, normal);
        float noiseValue = noise.sampleFloat(uv);
        
        std::cout << mapping.first << " -> UV(" << uv.x << ", " << uv.y << ") -> Noise: " << noiseValue << "\n";
    }
}

// Function to demonstrate noise composition
void demonstrateNoiseComposition() {
    std::cout << "\n=== Noise Composition Demo ===\n";
    
    // Create different noise layers
    NoiseTexture baseNoise(NoiseType::PERLIN, 4.0f, 1.0f, FractalParams::defaultParams());
    NoiseTexture detailNoise(NoiseType::SIMPLEX, 16.0f, 0.5f, FractalParams::defaultParams());
    NoiseTexture maskNoise(NoiseType::WORLEY, 2.0f, 1.0f, FractalParams::defaultParams());
    
    Vec2 testCoord(3.2f, 1.8f);
    
    float base = baseNoise.sampleFloat(testCoord);
    float detail = detailNoise.sampleFloat(testCoord);
    float mask = maskNoise.sampleFloat(testCoord);
    
    std::cout << "Individual noise values:\n";
    std::cout << "  Base (Perlin):   " << base << "\n";
    std::cout << "  Detail (Simplex): " << detail << "\n";
    std::cout << "  Mask (Worley):   " << mask << "\n";
    
    // Composition techniques
    float additive = std::clamp(base + detail * 0.3f, 0.0f, 1.0f);
    float multiplicative = base * detail;
    float masked = base * mask + detail * (1.0f - mask);
    float layered = base * 0.6f + detail * 0.3f + mask * 0.1f;
    
    std::cout << "\nComposition results:\n";
    std::cout << "  Additive:       " << additive << "\n";
    std::cout << "  Multiplicative: " << multiplicative << "\n";
    std::cout << "  Masked:         " << masked << "\n";
    std::cout << "  Layered:        " << layered << "\n";
}

// Function to demonstrate seed variation
void demonstrateSeedVariation() {
    std::cout << "\n=== Seed Variation Demo ===\n";
    
    Vec2 testCoord(1.0f, 1.0f);
    std::vector<int> seeds = {0, 12345, 54321, 99999, 777};
    
    for (int seed : seeds) {
        NoiseTexture noise(NoiseType::PERLIN, 8.0f, 1.0f, FractalParams::defaultParams());
        noise.setSeed(seed);
        
        float value = noise.sampleFloat(testCoord);
        std::cout << "Seed " << seed << ": " << value << "\n";
    }
}

// Function to demonstrate performance characteristics
void demonstratePerformance() {
    std::cout << "\n=== Performance Demo ===\n";
    
    const int numSamples = 100000;
    std::vector<Vec2> coords;
    coords.reserve(numSamples);
    
    // Generate test coordinates
    for (int i = 0; i < numSamples; ++i) {
        coords.emplace_back(
            static_cast<float>(i % 1000) * 0.01f,
            static_cast<float>(i / 1000) * 0.01f
        );
    }
    
    // Test different noise types
    std::vector<std::pair<std::string, std::unique_ptr<NoiseTexture>>> noiseTypes;
    noiseTypes.emplace_back("Perlin", std::make_unique<NoiseTexture>(NoiseType::PERLIN, 8.0f, 1.0f, FractalParams::defaultParams()));
    noiseTypes.emplace_back("Simplex", std::make_unique<NoiseTexture>(NoiseType::SIMPLEX, 8.0f, 1.0f, FractalParams::defaultParams()));
    noiseTypes.emplace_back("Worley", std::make_unique<NoiseTexture>(NoiseType::WORLEY, 8.0f, 1.0f, FractalParams::defaultParams()));
    
    for (const auto& noiseType : noiseTypes) {
        auto start = std::chrono::high_resolution_clock::now();
        
        for (const auto& coord : coords) {
            volatile float value = noiseType.second->sampleFloat(coord);
            (void)value; // Prevent optimization
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
        float avgTime = static_cast<float>(duration.count()) / numSamples;
        
        std::cout << noiseType.first << " Noise: " << avgTime << "ns per sample\n";
    }
}

// Function to create material examples
void demonstrateMaterialExamples() {
    std::cout << "\n=== Material Examples ===\n";
    
    // Wood grain pattern
    FractalParams woodFractal{6, 2.0f, 0.6f, 1.0f};
    NoiseTexture woodGrain(NoiseType::PERLIN, 12.0f, 1.0f, woodFractal);
    
    // Marble pattern
    FractalParams marbleFractal{4, 2.0f, 0.5f, 1.0f};
    NoiseTexture marbleBase(NoiseType::SIMPLEX, 3.0f, 1.0f, marbleFractal);
    
    // Cellular pattern (like foam or organic structures)
    FractalParams cellularFractal{3, 2.0f, 0.7f, 1.0f};
    NoiseTexture cellular(NoiseType::WORLEY, 8.0f, 1.0f, cellularFractal);
    
    Vec2 testCoord(2.5f, 1.8f);
    
    std::cout << "Material pattern samples at UV(" << testCoord.x << ", " << testCoord.y << "):\n";
    std::cout << "  Wood Grain:     " << woodGrain.sampleFloat(testCoord) << "\n";
    std::cout << "  Marble Base:    " << marbleBase.sampleFloat(testCoord) << "\n";
    std::cout << "  Cellular:       " << cellular.sampleFloat(testCoord) << "\n";
    
    // Save pattern examples
    saveNoiseAsText(woodGrain, "wood_grain_pattern.txt", 80, 40);
    saveNoiseAsText(marbleBase, "marble_pattern.txt", 80, 40);
    saveNoiseAsText(cellular, "cellular_pattern.txt", 80, 40);
}

int main() {
    std::cout << "PhotonRender - Advanced Noise Functions Demo\n";
    std::cout << "============================================\n";
    
    try {
        demonstrateBasicNoise();
        demonstrateFractalNoise();
        demonstrateUVMapping();
        demonstrateNoiseComposition();
        demonstrateSeedVariation();
        demonstratePerformance();
        demonstrateMaterialExamples();
        
        std::cout << "\n=== Demo Complete ===\n";
        std::cout << "Check the generated .txt files for visual noise patterns.\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
