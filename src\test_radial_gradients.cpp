// src/test_radial_gradients.cpp - Test suite for radial gradients
// PhotonRender Radial Gradients Test Suite

#include <iostream>
#include <iomanip>
#include <chrono>
#include <cmath>
#include "core/texture/texture.hpp"

using namespace photon;

/**
 * @brief Test suite for radial gradients
 */
class RadialGradientTest {
public:
    /**
     * @brief Run all radial gradient tests
     */
    static bool runAllTests() {
        std::cout << "=== PhotonRender Radial Gradients Test Suite ===\n\n";
        
        int passed = 0;
        int total = 0;
        
        // Basic gradient tests
        if (testBasicRadialGradient()) passed++; total++;
        if (testGradientParameters()) passed++; total++;
        if (testColorStops()) passed++; total++;
        if (testCenterPoint()) passed++; total++;
        if (testRadiusControl()) passed++; total++;
        if (testRepeatMode()) passed++; total++;
        if (testPerformance()) passed++; total++;
        if (testEdgeCases()) passed++; total++;
        
        std::cout << "\n=== Test Results ===\n";
        std::cout << "Passed: " << passed << "/" << total << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Radial Gradients working perfectly!\n";
            return true;
        } else {
            std::cout << "❌ Some tests failed. Check implementation.\n";
            return false;
        }
    }

private:
    /**
     * @brief Test basic radial gradient generation
     */
    static bool testBasicRadialGradient() {
        std::cout << "Testing basic radial gradient... ";
        
        try {
            // Create basic radial gradient (red to blue)
            Color3 red(1.0f, 0.0f, 0.0f);
            Color3 blue(0.0f, 0.0f, 1.0f);
            GradientTexture gradient(GradientType::RADIAL, red, blue);
            
            // Test center point (should be red)
            Color3 centerColor = gradient.sample(Vec2(0.5f, 0.5f));
            if (centerColor.r < 0.8f || centerColor.b > 0.2f) {
                std::cout << "[FAIL] - Center should be red\n";
                return false;
            }
            
            // Test edge point (should be blue or close to blue)
            Color3 edgeColor = gradient.sample(Vec2(1.0f, 0.5f));
            if (edgeColor.b < 0.3f) {
                std::cout << "[FAIL] - Edge should be blue-ish\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test gradient parameters
     */
    static bool testGradientParameters() {
        std::cout << "Testing gradient parameters... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.center = Vec2(0.3f, 0.7f);
            params.radius = 0.3f;
            
            std::vector<ColorStop> colorStops = {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            };
            
            GradientTexture gradient(GradientType::RADIAL, params, colorStops);
            
            // Test custom center
            Color3 centerColor = gradient.sample(Vec2(0.3f, 0.7f));
            if (centerColor.r < 0.8f) {
                std::cout << "[FAIL] - Custom center incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test color stops
     */
    static bool testColorStops() {
        std::cout << "Testing color stops... ";
        
        try {
            GradientTexture gradient(GradientType::RADIAL);
            gradient.clearColorStops();
            gradient.addColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)); // Red
            gradient.addColorStop(0.5f, Color3(0.0f, 1.0f, 0.0f)); // Green
            gradient.addColorStop(1.0f, Color3(0.0f, 0.0f, 1.0f)); // Blue
            
            // Test center (should be red)
            Color3 centerColor = gradient.sample(Vec2(0.5f, 0.5f));
            if (centerColor.r < 0.8f) {
                std::cout << "[FAIL] - Center color incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test center point control
     */
    static bool testCenterPoint() {
        std::cout << "Testing center point... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.center = Vec2(0.2f, 0.8f);
            
            GradientTexture gradient(GradientType::RADIAL, params, {
                ColorStop(0.0f, Color3(1.0f, 1.0f, 1.0f)),
                ColorStop(1.0f, Color3(0.0f, 0.0f, 0.0f))
            });
            
            // Test that center point gives white
            Color3 centerColor = gradient.sample(Vec2(0.2f, 0.8f));
            if (centerColor.r < 0.8f || centerColor.g < 0.8f || centerColor.b < 0.8f) {
                std::cout << "[FAIL] - Center point incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test radius control
     */
    static bool testRadiusControl() {
        std::cout << "Testing radius control... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.radius = 0.2f; // Small radius
            
            GradientTexture gradient(GradientType::RADIAL, params, {
                ColorStop(0.0f, Color3(1.0f, 1.0f, 1.0f)),
                ColorStop(1.0f, Color3(0.0f, 0.0f, 0.0f))
            });
            
            // Test that point at radius distance gives black
            Color3 radiusColor = gradient.sample(Vec2(0.7f, 0.5f)); // 0.2 units from center
            if (radiusColor.r > 0.5f || radiusColor.g > 0.5f || radiusColor.b > 0.5f) {
                std::cout << "[FAIL] - Radius control incorrect\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test repeat mode
     */
    static bool testRepeatMode() {
        std::cout << "Testing repeat mode... ";
        
        try {
            GradientParams params = GradientParams::defaultParams();
            params.repeat = true;
            params.spread = 0.5f;
            
            GradientTexture gradient(GradientType::RADIAL, params, {
                ColorStop(0.0f, Color3(0.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(1.0f, 1.0f, 1.0f))
            });
            
            // Test repeat pattern
            float value1 = gradient.sampleFloat(Vec2(0.6f, 0.5f));
            float value2 = gradient.sampleFloat(Vec2(0.8f, 0.5f));
            
            // With repeat, these should show pattern repetition
            if (std::abs(value1 - value2) > 0.5f) {
                std::cout << "[FAIL] - Repeat mode not working\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test performance
     */
    static bool testPerformance() {
        std::cout << "Testing performance... ";
        
        try {
            GradientTexture gradient(GradientType::RADIAL, Color3(0.0f), Color3(1.0f));
            
            const int numSamples = 10000;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            float totalValue = 0.0f;
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                totalValue += gradient.sampleFloat(Vec2(u, v));
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            
            float avgTimeNs = float(duration.count()) / numSamples;
            
            // Performance target: < 1000ns per sample
            if (avgTimeNs > 1000.0f) {
                std::cout << "[FAIL] - Performance too slow: " << avgTimeNs << "ns per sample\n";
                return false;
            }
            
            std::cout << "[PASS] (" << std::fixed << std::setprecision(2) << avgTimeNs << "ns per sample)\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test edge cases
     */
    static bool testEdgeCases() {
        std::cout << "Testing edge cases... ";
        
        try {
            // Test with zero radius
            GradientParams zeroParams = GradientParams::defaultParams();
            zeroParams.radius = 0.0f;
            
            GradientTexture zeroGradient(GradientType::RADIAL, zeroParams, {
                ColorStop(0.0f, Color3(1.0f, 0.0f, 0.0f)),
                ColorStop(1.0f, Color3(0.0f, 1.0f, 0.0f))
            });
            
            Color3 zeroColor = zeroGradient.sample(Vec2(0.5f, 0.5f));
            if (std::isnan(zeroColor.r) || std::isnan(zeroColor.g) || std::isnan(zeroColor.b)) {
                std::cout << "[FAIL] - Zero radius produces NaN\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    bool success = RadialGradientTest::runAllTests();
    return success ? 0 : 1;
}
