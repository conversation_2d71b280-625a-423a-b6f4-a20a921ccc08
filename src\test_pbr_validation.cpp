/**
 * @file test_pbr_validation.cpp
 * @brief Comprehensive PBR validation test suite for PhotonRender Phase 3.2.1 completion
 * @path src/test_pbr_validation.cpp
 * 
 * This test suite validates:
 * 1. Energy conservation for all Disney BRDF materials
 * 2. Physical accuracy of material parameters
 * 3. Performance benchmarks for PBR system
 * 4. Reference image generation for visual validation
 * 5. Integration tests for complete PBR workflow
 */

#include "core/material/disney_brdf.hpp"
#include "core/material/material.hpp"
#include "core/texture/texture.hpp"
#include "core/math/vec3.hpp"
#include "core/math/vec2.hpp"
#include "core/sampler/random_sampler.hpp"
#include "core/scene/intersection.hpp"

#include <iostream>
#include <vector>
#include <string>
#include <chrono>
#include <fstream>
#include <memory>
#include <algorithm>
#include <cmath>

using namespace photon;

namespace photon {

/**
 * @brief PBR Validation Test Suite
 */
class PBRValidator {
public:
    /**
     * @brief Constructor
     */
    PBRValidator() : m_totalTests(0), m_passedTests(0) {}
    
    /**
     * @brief Run complete validation suite
     * @return True if all tests pass
     */
    bool runCompleteValidation() {
        std::cout << "\n🎯 PhotonRender PBR Validation Suite - Phase 3.2.1 Completion" << std::endl;
        std::cout << "=================================================================" << std::endl;
        
        bool allPassed = true;
        
        // 1. Energy Conservation Tests
        allPassed &= runEnergyConservationTests();
        
        // 2. Physical Accuracy Tests
        allPassed &= runPhysicalAccuracyTests();
        
        // 3. Performance Benchmarks
        allPassed &= runPerformanceBenchmarks();
        
        // 4. Reference Image Tests
        allPassed &= runReferenceImageTests();
        
        // 5. Integration Tests
        allPassed &= runIntegrationTests();
        
        // Final report
        printFinalReport(allPassed);
        
        return allPassed;
    }

private:
    int m_totalTests;
    int m_passedTests;
    std::vector<std::string> m_failedTests;
    
    /**
     * @brief Record test result
     */
    void recordTest(const std::string& testName, bool passed) {
        m_totalTests++;
        if (passed) {
            m_passedTests++;
            std::cout << "✅ " << testName << " - PASSED" << std::endl;
        } else {
            m_failedTests.push_back(testName);
            std::cout << "❌ " << testName << " - FAILED" << std::endl;
        }
    }
    
    /**
     * @brief Test energy conservation for all material presets
     */
    bool runEnergyConservationTests() {
        std::cout << "\n📊 Energy Conservation Tests" << std::endl;
        std::cout << "-----------------------------" << std::endl;
        
        bool allPassed = true;
        
        // Test all material presets
        std::vector<std::string> presets = {
            "plastic", "metal", "glass", "wood", "fabric", 
            "skin", "ceramic", "rubber", "wax", "marble", "jade"
        };
        
        for (const auto& preset : presets) {
            bool passed = testMaterialEnergyConservation(preset);
            recordTest("Energy Conservation - " + preset, passed);
            allPassed &= passed;
        }
        
        // Test extreme parameter combinations
        bool extremeTest = testExtremeParameterEnergyConservation();
        recordTest("Energy Conservation - Extreme Parameters", extremeTest);
        allPassed &= extremeTest;
        
        return allPassed;
    }
    
    /**
     * @brief Test energy conservation for specific material preset
     */
    bool testMaterialEnergyConservation(const std::string& preset) {
        try {
            auto material = PBRMaterial::createPreset(preset, Color3(0.8f, 0.6f, 0.4f));
            
            // Monte Carlo integration to check energy conservation
            const int numSamples = 1000;
            const int numDirections = 10;
            
            ExtendedRandomSampler sampler;
            
            for (int d = 0; d < numDirections; ++d) {
                // Random outgoing direction
                Vec3 wo = sampleHemisphere(sampler);
                
                Color3 totalReflectance(0.0f);
                
                for (int i = 0; i < numSamples; ++i) {
                    Intersection isect;
                    isect.n = Vec3(0, 0, 1);
                    isect.p = Vec3(0, 0, 0);
                    
                    BSDFSample sample = material->sample(isect, wo, sampler);
                    
                    if (sample.isValid() && sample.wi.dot(isect.n) > 0.0f) {
                        totalReflectance += sample.f * sample.wi.dot(isect.n) / sample.pdf;
                    }
                }
                
                totalReflectance = totalReflectance / float(numSamples);
                float maxReflectance = std::max({totalReflectance.r, totalReflectance.g, totalReflectance.b});
                
                // Energy conservation: reflectance should not exceed 1.0
                if (maxReflectance > 1.05f) { // Allow small Monte Carlo error
                    std::cout << "    Energy violation: " << maxReflectance << " > 1.0" << std::endl;
                    return false;
                }
            }
            
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "    Exception: " << e.what() << std::endl;
            return false;
        }
    }
    
    /**
     * @brief Sample hemisphere direction
     */
    Vec3 sampleHemisphere(ExtendedRandomSampler& sampler) {
        float u1 = sampler.next1D();
        float u2 = sampler.next1D();
        
        float cosTheta = u1;
        float sinTheta = std::sqrt(1.0f - cosTheta * cosTheta);
        float phi = 2.0f * M_PI * u2;
        
        return Vec3(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);
    }
    
    /**
     * @brief Test extreme parameter combinations
     */
    bool testExtremeParameterEnergyConservation() {
        DisneyBRDF brdf;
        RandomSampler sampler;
        
        // Test extreme combinations
        std::vector<DisneyBRDFParams> extremeParams = {
            // High metallic + high roughness
            {Color3(1.0f), 1.0f, 1.0f, 0.5f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f},
            // High subsurface + high roughness
            {Color3(0.9f), 0.0f, 1.0f, 0.5f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 1.0f},
            // High clearcoat + high sheen
            {Color3(0.8f), 0.0f, 0.3f, 0.5f, 0.0f, 0.0f, 1.0f, 0.5f, 1.0f, 0.0f, 0.0f}
        };
        
        for (size_t i = 0; i < extremeParams.size(); ++i) {
            brdf.setParameters(extremeParams[i]);
            
            Vec3 wo(0, 0, 1);
            Vec3 n(0, 0, 1);
            
            Color3 totalReflectance(0.0f);
            const int numSamples = 1000;
            
            for (int j = 0; j < numSamples; ++j) {
                Vec3 wi;
                float pdf;
                Color3 f = brdf.sample(wo, n, sampler, wi, pdf);
                
                if (pdf > 0.0f && wi.dot(n) > 0.0f) {
                    totalReflectance += f * wi.dot(n) / pdf;
                }
            }
            
            totalReflectance = totalReflectance / float(numSamples);
            float maxReflectance = std::max({totalReflectance.r, totalReflectance.g, totalReflectance.b});
            
            if (maxReflectance > 1.05f) {
                std::cout << "    Extreme case " << i << " energy violation: " << maxReflectance << std::endl;
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * @brief Test physical accuracy of material parameters
     */
    bool runPhysicalAccuracyTests() {
        std::cout << "\n🔬 Physical Accuracy Tests" << std::endl;
        std::cout << "--------------------------" << std::endl;

        bool allPassed = true;

        // Test parameter validation
        bool paramTest = testParameterValidation();
        recordTest("Parameter Validation", paramTest);
        allPassed &= paramTest;

        // Test Fresnel accuracy
        bool fresnelTest = testFresnelAccuracy();
        recordTest("Fresnel Accuracy", fresnelTest);
        allPassed &= fresnelTest;

        // Test reciprocity
        bool reciprocityTest = testBRDFReciprocity();
        recordTest("BRDF Reciprocity", reciprocityTest);
        allPassed &= reciprocityTest;

        return allPassed;
    }

    /**
     * @brief Test parameter validation and clamping
     */
    bool testParameterValidation() {
        DisneyBRDFParams params;

        // Test invalid parameters
        params.metallic = -0.5f;
        params.roughness = 1.5f;
        params.specular = 2.0f;
        params.subsurface = -0.1f;

        params.validate();

        // Check if parameters were clamped correctly
        return (params.metallic >= 0.0f && params.metallic <= 1.0f &&
                params.roughness >= 0.0f && params.roughness <= 1.0f &&
                params.specular >= 0.0f && params.specular <= 1.0f &&
                params.subsurface >= 0.0f && params.subsurface <= 1.0f);
    }

    /**
     * @brief Test Fresnel calculation accuracy
     */
    bool testFresnelAccuracy() {
        // Test known Fresnel values for common materials
        float ior_glass = 1.5f;
        float cosTheta = 0.5f; // 60 degrees

        // Schlick approximation should be close to exact Fresnel
        float f0 = std::pow((1.0f - ior_glass) / (1.0f + ior_glass), 2.0f);
        float schlick = f0 + (1.0f - f0) * std::pow(1.0f - cosTheta, 5.0f);

        // Exact Fresnel for comparison (simplified)
        float exact = 0.04f + 0.96f * std::pow(1.0f - cosTheta, 5.0f); // Approximation

        float error = std::abs(schlick - exact);
        return error < 0.1f; // Allow reasonable approximation error
    }

    /**
     * @brief Test BRDF reciprocity
     */
    bool testBRDFReciprocity() {
        DisneyBRDF brdf;
        DisneyBRDFParams params = DisneyMaterialPresets::createPlastic();
        brdf.setParameters(params);

        Vec3 n(0, 0, 1);
        Vec3 wo(0.5f, 0.3f, 0.8f);
        Vec3 wi(0.2f, 0.7f, 0.6f);
        wo = wo.normalized();
        wi = wi.normalized();

        // Test reciprocity: f(wo, wi) should equal f(wi, wo)
        Color3 f1 = brdf.eval(wo, wi, n);
        Color3 f2 = brdf.eval(wi, wo, n);

        float diff = (f1 - f2).length();
        return diff < 1e-5f;
    }

    /**
     * @brief Run performance benchmarks
     */
    bool runPerformanceBenchmarks() {
        std::cout << "\n⚡ Performance Benchmarks" << std::endl;
        std::cout << "------------------------" << std::endl;

        bool allPassed = true;

        // Benchmark BRDF evaluation
        bool evalTest = benchmarkBRDFEvaluation();
        recordTest("BRDF Evaluation Performance", evalTest);
        allPassed &= evalTest;

        // Benchmark BRDF sampling
        bool sampleTest = benchmarkBRDFSampling();
        recordTest("BRDF Sampling Performance", sampleTest);
        allPassed &= sampleTest;

        // Benchmark texture sampling
        bool textureTest = benchmarkTextureSampling();
        recordTest("Texture Sampling Performance", textureTest);
        allPassed &= textureTest;

        return allPassed;
    }

    /**
     * @brief Benchmark BRDF evaluation performance
     */
    bool benchmarkBRDFEvaluation() {
        DisneyBRDF brdf;
        DisneyBRDFParams params = DisneyMaterialPresets::createMetal(Color3(0.8f, 0.6f, 0.4f));
        brdf.setParameters(params);

        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);
        Vec3 wi(0.5f, 0.5f, 0.707f);
        wi = wi.normalized();

        const int numEvaluations = 100000;

        auto start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < numEvaluations; ++i) {
            Color3 f = brdf.eval(wo, wi, n);
            // Prevent optimization
            volatile float dummy = f.r + f.g + f.b;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);

        float nsPerEval = float(duration.count()) / numEvaluations;
        std::cout << "    BRDF eval: " << nsPerEval << " ns/call" << std::endl;

        // Target: < 200 ns per evaluation
        return nsPerEval < 200.0f;
    }

    /**
     * @brief Benchmark BRDF sampling performance
     */
    bool benchmarkBRDFSampling() {
        DisneyBRDF brdf;
        DisneyBRDFParams params = DisneyMaterialPresets::createPlastic();
        brdf.setParameters(params);

        RandomSampler sampler;
        Vec3 n(0, 0, 1);
        Vec3 wo(0, 0, 1);

        const int numSamples = 50000;

        auto start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < numSamples; ++i) {
            Vec3 wi;
            float pdf;
            Color3 f = brdf.sample(wo, n, sampler, wi, pdf);
            // Prevent optimization
            volatile float dummy = f.r + pdf;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);

        float nsPerSample = float(duration.count()) / numSamples;
        std::cout << "    BRDF sample: " << nsPerSample << " ns/call" << std::endl;

        // Target: < 500 ns per sample
        return nsPerSample < 500.0f;
    }

    /**
     * @brief Benchmark texture sampling performance
     */
    bool benchmarkTextureSampling() {
        // Create a simple texture
        auto texture = ImageTexture::createSolid(Color3(0.8f, 0.6f, 0.4f));

        const int numSamples = 100000;

        auto start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < numSamples; ++i) {
            Vec2 uv(float(i % 100) / 100.0f, float(i / 100) / 1000.0f);
            Color3 color = texture->sample(uv);
            // Prevent optimization
            volatile float dummy = color.r + color.g + color.b;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);

        float nsPerSample = float(duration.count()) / numSamples;
        std::cout << "    Texture sample: " << nsPerSample << " ns/call" << std::endl;

        // Target: < 50 ns per sample
        return nsPerSample < 50.0f;
    }

    /**
     * @brief Print final validation report
     */
    void printFinalReport(bool allPassed) {
        std::cout << "\n🎊 PBR Validation Final Report" << std::endl;
        std::cout << "==============================" << std::endl;
        std::cout << "Total Tests: " << m_totalTests << std::endl;
        std::cout << "Passed: " << m_passedTests << std::endl;
        std::cout << "Failed: " << (m_totalTests - m_passedTests) << std::endl;
        std::cout << "Success Rate: " << (100.0f * m_passedTests / m_totalTests) << "%" << std::endl;

        if (allPassed) {
            std::cout << "\n🎉 ALL TESTS PASSED! Phase 3.2.1 Disney PBR Materials System COMPLETE!" << std::endl;
            std::cout << "🚀 Ready for Phase 3.2.2 Advanced Lighting System!" << std::endl;
        } else {
            std::cout << "\n❌ Some tests failed. Failed tests:" << std::endl;
            for (const auto& test : m_failedTests) {
                std::cout << "  - " << test << std::endl;
            }
        }
    }

    /**
     * @brief Run reference image tests
     */
    bool runReferenceImageTests() {
        std::cout << "\n🖼️ Reference Image Tests" << std::endl;
        std::cout << "-----------------------" << std::endl;

        bool allPassed = true;

        // Test material sphere rendering
        bool sphereTest = testMaterialSphereRendering();
        recordTest("Material Sphere Rendering", sphereTest);
        allPassed &= sphereTest;

        // Test texture integration
        bool textureIntegrationTest = testTextureIntegrationRendering();
        recordTest("Texture Integration Rendering", textureIntegrationTest);
        allPassed &= textureIntegrationTest;

        return allPassed;
    }

    /**
     * @brief Test material sphere rendering for visual validation
     */
    bool testMaterialSphereRendering() {
        try {
            // Create simple sphere scene for each material preset
            std::vector<std::string> presets = {"plastic", "metal", "glass", "wood", "skin"};

            for (const auto& preset : presets) {
                // Create material
                auto material = PBRMaterial::createPreset(preset, Color3(0.8f, 0.6f, 0.4f));

                // Simple validation: ensure material can be created and used
                Intersection isect;
                isect.n = Vec3(0, 0, 1);
                isect.p = Vec3(0, 0, 0);

                Vec3 wo(0, 0, 1);
                RandomSampler sampler;

                BSDFSample sample = material->sample(isect, wo, sampler);

                if (!sample.isValid()) {
                    std::cout << "    Failed to sample " << preset << " material" << std::endl;
                    return false;
                }

                std::cout << "    " << preset << " sphere: OK" << std::endl;
            }

            return true;

        } catch (const std::exception& e) {
            std::cout << "    Exception in sphere rendering: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief Test texture integration rendering
     */
    bool testTextureIntegrationRendering() {
        try {
            // Create material with textures
            auto material = PBRMaterial::createPreset("plastic", Color3(0.8f, 0.6f, 0.4f));

            // Create simple textures
            auto baseColorTexture = ImageTexture::createSolid(Color3(0.8f, 0.2f, 0.2f));
            auto roughnessTexture = ImageTexture::createSolid(0.3f);

            // Test texture application (simplified validation)
            Vec2 uv(0.5f, 0.5f);
            Color3 baseColor = baseColorTexture->sample(uv);
            float roughness = roughnessTexture->sampleFloat(uv);

            // Validate texture values
            if (baseColor.r < 0.7f || baseColor.r > 0.9f) {
                std::cout << "    Base color texture sampling failed" << std::endl;
                return false;
            }

            if (roughness < 0.25f || roughness > 0.35f) {
                std::cout << "    Roughness texture sampling failed" << std::endl;
                return false;
            }

            std::cout << "    Texture integration: OK" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "    Exception in texture integration: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief Run integration tests
     */
    bool runIntegrationTests() {
        std::cout << "\n🔗 Integration Tests" << std::endl;
        std::cout << "-------------------" << std::endl;

        bool allPassed = true;

        // Test complete PBR workflow
        bool workflowTest = testCompletePBRWorkflow();
        recordTest("Complete PBR Workflow", workflowTest);
        allPassed &= workflowTest;

        // Test material library
        bool libraryTest = testMaterialLibrary();
        recordTest("Material Library", libraryTest);
        allPassed &= libraryTest;

        // Test subsurface scattering integration
        bool subsurfaceTest = testSubsurfaceIntegration();
        recordTest("Subsurface Scattering Integration", subsurfaceTest);
        allPassed &= subsurfaceTest;

        return allPassed;
    }

    /**
     * @brief Test complete PBR workflow
     */
    bool testCompletePBRWorkflow() {
        try {
            // 1. Create Disney BRDF
            DisneyBRDF brdf;
            DisneyBRDFParams params = DisneyMaterialPresets::createMetal(Color3(0.9f, 0.7f, 0.3f));
            brdf.setParameters(params);

            // 2. Test BRDF functionality
            Vec3 n(0, 0, 1);
            Vec3 wo(0, 0, 1);
            RandomSampler sampler;

            Vec3 wi;
            float pdf;
            Color3 f = brdf.sample(wo, n, sampler, wi, pdf);

            if (!std::isfinite(f.r) || !std::isfinite(pdf) || pdf <= 0.0f) {
                std::cout << "    BRDF sampling failed" << std::endl;
                return false;
            }

            // 3. Test PBR material
            auto material = PBRMaterial::createPreset("metal", Color3(0.9f, 0.7f, 0.3f));

            Intersection isect;
            isect.n = n;
            isect.p = Vec3(0, 0, 0);

            BSDFSample sample = material->sample(isect, wo, sampler);

            if (!sample.isValid()) {
                std::cout << "    PBR material sampling failed" << std::endl;
                return false;
            }

            // 4. Test energy conservation
            if (!material->validateEnergyConservation()) {
                std::cout << "    Energy conservation failed" << std::endl;
                return false;
            }

            std::cout << "    Complete workflow: OK" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "    Exception in workflow test: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief Test material library functionality
     */
    bool testMaterialLibrary() {
        try {
            // Test all material presets
            std::vector<std::string> allPresets = {
                "plastic", "metal", "glass", "wood", "fabric",
                "skin", "ceramic", "rubber", "wax", "marble", "jade"
            };

            for (const auto& preset : allPresets) {
                auto material = PBRMaterial::createPreset(preset, Color3(0.7f, 0.5f, 0.3f));

                if (!material) {
                    std::cout << "    Failed to create " << preset << " preset" << std::endl;
                    return false;
                }

                // Test basic functionality
                if (!material->validateEnergyConservation()) {
                    std::cout << "    " << preset << " preset violates energy conservation" << std::endl;
                    return false;
                }
            }

            std::cout << "    Material library (" << allPresets.size() << " presets): OK" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "    Exception in material library test: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief Test subsurface scattering integration
     */
    bool testSubsurfaceIntegration() {
        try {
            // Test subsurface materials
            std::vector<std::string> subsurfacePresets = {"skin", "wax", "marble", "jade"};

            for (const auto& preset : subsurfacePresets) {
                auto material = PBRMaterial::createPreset(preset, Color3(0.8f, 0.6f, 0.4f));

                // Get Disney BRDF parameters
                const auto& params = material->getParameters();

                // Check subsurface parameter
                if (params.subsurface <= 0.0f) {
                    std::cout << "    " << preset << " should have subsurface > 0" << std::endl;
                    return false;
                }

                // Test subsurface rendering
                Intersection isect;
                isect.n = Vec3(0, 0, 1);
                isect.p = Vec3(0, 0, 0);

                Vec3 wo(0, 0, 1);
                RandomSampler sampler;

                BSDFSample sample = material->sample(isect, wo, sampler);

                if (!sample.isValid()) {
                    std::cout << "    " << preset << " subsurface sampling failed" << std::endl;
                    return false;
                }
            }

            std::cout << "    Subsurface integration (" << subsurfacePresets.size() << " materials): OK" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "    Exception in subsurface test: " << e.what() << std::endl;
            return false;
        }
    }
};

} // namespace photon

/**
 * @brief Main validation function
 */
bool runPBRValidationTests() {
    photon::PBRValidator validator;
    return validator.runCompleteValidation();
}

/**
 * @brief Main function for standalone execution
 */
int main() {
    std::cout << "PhotonRender PBR Validation Suite" << std::endl;
    std::cout << "Phase 3.2.1 Completion Test" << std::endl;
    
    bool success = runPBRValidationTests();
    
    return success ? 0 : 1;
}
