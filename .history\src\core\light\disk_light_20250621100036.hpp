// src/core/light/disk_light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Disk Area Light implementation

#pragma once

// Windows math constants
#define _USE_MATH_DEFINES
#include <cmath>

#include "area_light_base.hpp"
#include "../math/vec3.hpp"
#include "../math/vec2.hpp"
#include "../math/color3.hpp"

namespace photon {

/**
 * @brief Disk Area Light
 */
class DiskLight : public AreaLightBase {
public:
    /**
     * @brief Constructor
     * @param center Disk center
     * @param normal Disk normal
     * @param radius Disk radius
     * @param emission Emission color
     * @param intensity Intensity multiplier
     * @param twoSided Two-sided emission
     */
    DiskLight(const Vec3& center, const Vec3& normal, float radius,
              const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Destructor
     */
    virtual ~DiskLight() = default;
    
    // AreaLightBase interface implementation
    void sampleSurface(Sampler& sampler, Vec3& point, Vec3& normal, float& pdf) const override;
    float getArea() const override;
    AreaLightShape getShape() const override { return AreaLightShape::DISK; }
    bool intersect(const Ray& ray, float& t, Vec3& point, Vec3& normal) const override;
    std::string getName() const override { return "Disk"; }
    
    /**
     * @brief Get disk center
     */
    const Vec3& getCenter() const { return m_center; }
    
    /**
     * @brief Get disk normal
     */
    const Vec3& getNormal() const { return m_normal; }
    
    /**
     * @brief Get disk radius
     */
    float getRadius() const { return m_radius; }
    
    /**
     * @brief Set disk geometry
     * @param center New center
     * @param normal New normal
     * @param radius New radius
     */
    void setGeometry(const Vec3& center, const Vec3& normal, float radius);
    
    /**
     * @brief Set disk center
     */
    void setCenter(const Vec3& center) { m_center = center; }
    
    /**
     * @brief Set disk normal
     */
    void setNormal(const Vec3& normal) { m_normal = normal.normalized(); }
    
    /**
     * @brief Set disk radius
     */
    void setRadius(float radius) { 
        m_radius = std::max(0.0f, radius); 
        m_area = M_PI * m_radius * m_radius;
    }
    
    /**
     * @brief Sample point using uniform distribution
     * @param u Random sample [0,1]^2
     * @return Sampled point on disk
     */
    Vec3 sampleUniform(const Vec2& u) const;
    
    /**
     * @brief Sample point using concentric disk mapping
     * @param u Random sample [0,1]^2
     * @return Sampled point on disk
     */
    Vec3 sampleConcentric(const Vec2& u) const;
    
    /**
     * @brief Get solid angle subtended by disk from point
     * @param point Point from which to compute solid angle
     * @return Solid angle in steradians
     */
    float getSolidAngle(const Vec3& point) const;
    
    /**
     * @brief Get distance from point to disk center
     * @param point Query point
     * @return Distance to center
     */
    float getDistanceToCenter(const Vec3& point) const;
    
    /**
     * @brief Check if point is inside disk (in disk plane)
     * @param point Point to test
     * @return True if inside disk
     */
    bool isInsideDisk(const Vec3& point) const;

private:
    Vec3 m_center;      // Disk center
    Vec3 m_normal;      // Disk normal
    float m_radius;     // Disk radius
    float m_area;       // Cached area
    
    // Cached orthonormal basis
    Vec3 m_tangent;     // Tangent vector
    Vec3 m_bitangent;   // Bitangent vector
    
    /**
     * @brief Update cached values
     */
    void updateCachedValues();
    
    /**
     * @brief Convert world point to disk local coordinates
     * @param worldPoint World space point
     * @return Local coordinates (x, y) on disk plane
     */
    Vec2 worldToLocal(const Vec3& worldPoint) const;
    
    /**
     * @brief Convert disk local coordinates to world point
     * @param localPoint Local coordinates on disk plane
     * @return World space point
     */
    Vec3 localToWorld(const Vec2& localPoint) const;
    
    /**
     * @brief Sample uniform disk using rejection sampling
     * @param u Random sample [0,1]^2
     * @return Point on unit disk
     */
    Vec2 sampleUniformDiskRejection(const Vec2& u) const;
    
    /**
     * @brief Sample uniform disk using concentric mapping
     * @param u Random sample [0,1]^2
     * @return Point on unit disk
     */
    Vec2 sampleConcentricDisk(const Vec2& u) const;
};

/**
 * @brief Disk Light factory functions
 */
namespace DiskLightFactory {
    /**
     * @brief Create disk light
     */
    std::shared_ptr<DiskLight> create(
        const Vec3& center, const Vec3& normal, float radius,
        const Color3& emission, float intensity = 1.0f, bool twoSided = false);
    
    /**
     * @brief Create ceiling disk light (facing down)
     */
    std::shared_ptr<DiskLight> createCeiling(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create floor disk light (facing up)
     */
    std::shared_ptr<DiskLight> createFloor(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create wall disk light
     */
    std::shared_ptr<DiskLight> createWall(
        const Vec3& center, const Vec3& wallNormal, float radius,
        const Color3& emission, float intensity = 1.0f);
    
    /**
     * @brief Create small disk light (like a bulb)
     */
    std::shared_ptr<DiskLight> createBulb(
        const Vec3& center, float radius,
        const Color3& emission, float intensity = 1.0f);
}

} // namespace photon
