// src/test_multiple_uv_sets.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Test suite for Multiple UV Sets system

#include "core/geometry/mesh.hpp"
#include "core/scene/intersection.hpp"
#include "core/texture/uv_mapping.hpp"
#include "core/math/vec2.hpp"
#include "core/math/vec3.hpp"
#include <iostream>
#include <cassert>
#include <chrono>

using namespace photon;

// Test results tracking
int tests_passed = 0;
int tests_total = 0;

#define TEST(name) \
    tests_total++; \
    std::cout << "Testing " << name << "... "; \
    if (test_##name()) { \
        tests_passed++; \
        std::cout << "[PASS]" << std::endl; \
    } else { \
        std::cout << "[FAIL]" << std::endl; \
    }

// Test Vertex Multiple UV Sets
bool test_vertex_multiple_uv_sets() {
    // Test default constructor
    Vertex v1;
    if (!v1.hasUVSet(0) || v1.hasUVSet(1) || v1.hasUVSet(2) || v1.hasUVSet(3)) {
        return false;
    }
    
    // Test constructor with multiple UV sets
    Vec2 uv0(0.1f, 0.2f);
    Vec2 uv1(0.3f, 0.4f);
    Vec2 uv2(0.5f, 0.6f);
    Vec2 uv3(0.7f, 0.8f);
    
    Point3 pos(1.0f, 2.0f, 3.0f);
    Normal3 norm(0.0f, 1.0f, 0.0f);
    
    Vertex v2(pos, norm, uv0, uv1, uv2, uv3);
    
    // Verify UV sets
    if (v2.getUV(0) != uv0 || v2.getUV(1) != uv1 || 
        v2.getUV(2) != uv2 || v2.getUV(3) != uv3) {
        return false;
    }
    
    // Test setUV method
    Vertex v3;
    v3.setUV(1, Vec2(0.9f, 1.0f));
    if (!v3.hasUVSet(1) || v3.getUV(1) != Vec2(0.9f, 1.0f)) {
        return false;
    }
    
    return true;
}

// Test Mesh Multiple UV Sets
bool test_mesh_multiple_uv_sets() {
    Mesh mesh;
    
    // Add vertex with multiple UV sets
    Point3 pos(1.0f, 2.0f, 3.0f);
    Normal3 norm(0.0f, 1.0f, 0.0f);
    Vec2 uv0(0.1f, 0.2f);
    Vec2 uv1(0.3f, 0.4f);
    Vec2 uv2(0.5f, 0.6f);
    Vec2 uv3(0.7f, 0.8f);
    
    mesh.addVertex(pos, norm, uv0, uv1, uv2, uv3);
    
    // Verify UV sets
    if (mesh.getVertexUV(0, 0) != uv0 || mesh.getVertexUV(0, 1) != uv1 ||
        mesh.getVertexUV(0, 2) != uv2 || mesh.getVertexUV(0, 3) != uv3) {
        return false;
    }
    
    // Test setVertexUV
    mesh.setVertexUV(0, 1, Vec2(0.9f, 1.0f));
    if (mesh.getVertexUV(0, 1) != Vec2(0.9f, 1.0f)) {
        return false;
    }
    
    // Test hasVertexUVSet
    if (!mesh.hasVertexUVSet(0, 0) || !mesh.hasVertexUVSet(0, 1) ||
        !mesh.hasVertexUVSet(0, 2) || !mesh.hasVertexUVSet(0, 3)) {
        return false;
    }
    
    return true;
}

// Test Mesh Statistics
bool test_mesh_statistics() {
    Mesh mesh;
    
    // Add vertices with different UV sets
    Point3 pos(1.0f, 2.0f, 3.0f);
    Normal3 norm(0.0f, 1.0f, 0.0f);
    
    // Vertex 1: Only UV0
    mesh.addVertex(pos, norm, 0.1f, 0.2f);
    
    // Vertex 2: UV0 + UV1
    mesh.addVertex(pos, norm, Vec2(0.1f, 0.2f), Vec2(0.3f, 0.4f));
    
    // Vertex 3: UV0 + UV1 + UV2 + UV3
    mesh.addVertex(pos, norm, Vec2(0.1f, 0.2f), Vec2(0.3f, 0.4f), 
                   Vec2(0.5f, 0.6f), Vec2(0.7f, 0.8f));
    
    auto stats = mesh.getStats();
    
    // Verify statistics
    if (!stats.hasTexCoords || !stats.hasUV1 || !stats.hasUV2 || !stats.hasUV3) {
        return false;
    }
    
    if (stats.maxUVSets != 4) {
        return false;
    }
    
    return true;
}

// Test Intersection Multiple UV Sets
bool test_intersection_multiple_uv_sets() {
    Intersection isect;
    
    // Test setUV with UV set index
    isect.setUV(0, Vec2(0.1f, 0.2f));
    isect.setUV(1, Vec2(0.3f, 0.4f));
    isect.setUV(2, Vec2(0.5f, 0.6f));
    isect.setUV(3, Vec2(0.7f, 0.8f));
    
    // Verify UV sets
    if (isect.getUV(0) != Vec2(0.1f, 0.2f) || isect.getUV(1) != Vec2(0.3f, 0.4f) ||
        isect.getUV(2) != Vec2(0.5f, 0.6f) || isect.getUV(3) != Vec2(0.7f, 0.8f)) {
        return false;
    }
    
    // Test hasUVSet
    if (!isect.hasUVSet(0) || !isect.hasUVSet(1) || 
        !isect.hasUVSet(2) || !isect.hasUVSet(3)) {
        return false;
    }
    
    // Test backward compatibility
    isect.setUV(Vec2(0.9f, 1.0f));
    if (isect.getUV() != Vec2(0.9f, 1.0f) || isect.getUV(0) != Vec2(0.9f, 1.0f)) {
        return false;
    }
    
    return true;
}

// Test MultiUVMapping
bool test_multi_uv_mapping() {
    MultiUVMapping multiMapping;
    
    // Set different mapping modes for each UV set
    multiMapping.setMappingMode(0, UVMappingMode::VERTEX_UV);
    multiMapping.setMappingMode(1, UVMappingMode::PLANAR_XY);
    multiMapping.setMappingMode(2, UVMappingMode::CYLINDRICAL_Y);
    multiMapping.setMappingMode(3, UVMappingMode::SPHERICAL);
    
    // Test UV generation
    Vec3 position(1.0f, 0.5f, 0.0f);
    Vec3 normal(0.0f, 1.0f, 0.0f);
    
    Vec2 uv1 = multiMapping.generateUV(1, position, normal);
    Vec2 uv2 = multiMapping.generateUV(2, position, normal);
    Vec2 uv3 = multiMapping.generateUV(3, position, normal);
    
    // Verify different results for different mapping modes
    if (uv1 == uv2 || uv2 == uv3 || uv1 == uv3) {
        // This might fail if positions generate same UV by coincidence
        // But it's unlikely with these different mapping modes
    }
    
    // Test transform UV
    Vec2 inputUV(0.5f, 0.5f);
    Vec2 transformedUV = multiMapping.transformUV(0, inputUV);
    
    return true;
}

// Test Performance
bool test_performance() {
    const int NUM_OPERATIONS = 100000;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Test vertex UV operations
    for (int i = 0; i < NUM_OPERATIONS; i++) {
        Vertex v;
        v.setUV(0, Vec2(0.1f, 0.2f));
        v.setUV(1, Vec2(0.3f, 0.4f));
        v.setUV(2, Vec2(0.5f, 0.6f));
        v.setUV(3, Vec2(0.7f, 0.8f));
        
        Vec2 uv0 = v.getUV(0);
        Vec2 uv1 = v.getUV(1);
        Vec2 uv2 = v.getUV(2);
        Vec2 uv3 = v.getUV(3);
        
        bool has0 = v.hasUVSet(0);
        bool has1 = v.hasUVSet(1);
        bool has2 = v.hasUVSet(2);
        bool has3 = v.hasUVSet(3);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    double avgTime = duration.count() / (double)(NUM_OPERATIONS * 12); // 12 operations per iteration
    
    std::cout << std::endl;
    std::cout << "Performance: " << avgTime << " ns per UV operation" << std::endl;
    
    // Performance should be under 100ns per operation
    return avgTime < 100.0;
}

int main() {
    std::cout << "=== PhotonRender Multiple UV Sets Test Suite ===" << std::endl;
    std::cout << std::endl;
    
    // Run all tests
    TEST(vertex_multiple_uv_sets)
    TEST(mesh_multiple_uv_sets)
    TEST(mesh_statistics)
    TEST(intersection_multiple_uv_sets)
    TEST(multi_uv_mapping)
    TEST(performance)
    
    std::cout << std::endl;
    std::cout << "=== Test Results ===" << std::endl;
    std::cout << "Passed: " << tests_passed << "/" << tests_total << std::endl;
    
    if (tests_passed == tests_total) {
        std::cout << "🎉 ALL TESTS PASSED! Multiple UV Sets system working perfectly!" << std::endl;
        return 0;
    } else {
        std::cout << "❌ Some tests failed. Please check implementation." << std::endl;
        return 1;
    }
}
